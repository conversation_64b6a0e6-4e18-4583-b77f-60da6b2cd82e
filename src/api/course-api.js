import request from '@/utils/request'
import { getToken, getChildToken } from '@/utils/auth'
import { addLocationNameParam } from '@/utils/locationHelper'

export function exchangeCourse (params) {
  return request({
    url: '/api/v2/course/exchangeCourse',
    method: 'post',
    headers: {
      'authorization': getChildToken() || getToken()
    },
    params
  })
}

export function getCourseLessonPlanList (params, needChildToken = true) {
  return request({
    url: '/api/v2/course/getCourseLessonPlanList',
    method: 'get',
    headers: {
      'authorization': needChildToken ? getChildToken() || getToken() : getToken()
    },
    params
  })
}

export function teacherList (params) {
  return request({
    url: '/api/v1/students/teacherList',
    method: 'get',
    headers: {
      'authorization': getChildToken() || getToken()
    },
    params
  })
}

export function scheduleTeacher (params) {
  return request({
    url: '/api/v2/lesson/scheduleTeacher',
    method: 'post',
    headers: {
      'authorization': getChildToken() || getToken()
    },
    params
  })
}

export function teacherListByCourseId (params) {
  return request({
    url: '/api/v1/students/teacherListByCourseId',
    method: 'get',
    headers: {
      'authorization': getChildToken() || getToken()
    },
    params
  })
}

export function getTeacherAvailableTime (params) {
  return request({
    url: '/api/v1/students/getTeacherAvailableTime',
    method: 'get',
    headers: {
      'authorization': getChildToken() || getToken()
    },
    params
  })
}

export function cancelScheduledLesson (data) {
  return request({
    url: '/api/v1/students/cancelScheduledLesson',
    method: 'post',
    headers: {
      'authorization': getChildToken() || getToken()
    },
    data
  })
}

export function getPlanLessonCourse (data) {
  return request({
    url: '/api/v2/course/getPlanLessonCourse',
    method: 'get',
    headers: {
      'authorization': getChildToken() || getToken()
    },
    params: data
  })
}

export function getPlanLessonCourseUnit (params) {
  return request({
    url: '/api/v2/course/getPlanLessonCourseUnit',
    method: 'get',
    headers: {
      'authorization': getChildToken() || getToken()
    },
    params
  })
}

export function getCourseSeriesList () {
  const params = addLocationNameParam({})

  return request({
    url: '/api/v2/course/vt/getCourseSeriesList',
    method: 'get',
    params
  })
}

// 获取课程包系列-主题下的课程
export function getCoursePackageList (params = {}) {
  addLocationNameParam(params)

  return request({
    url: '/api/v2/course/vt/getCoursePackageList',
    method: 'get',
    params
  })
}

// 获取课程包系列-主题下的课程
export function getCoursePackageInfo (params) {
  return request({
    url: '/api/v2/course/getCoursePackageInfo',
    method: 'get',
    params
  })
}

// 用户喜欢/不喜欢课程
export function correlateCoursePackage (params) {
  return request({
    url: '/api/v2/course/correlateCoursePackage',
    method: 'get',
    params
  })
}

// 课程统计
export function userCourseStatistics (params) {
  return request({
    url: '/api/v2/course/userCourseStatistics',
    method: 'get',
    headers: {
      'authorization': getChildToken() || getToken()
    },
    params
  })
}

// 获取直播课程包信息
export function getCourse (params) {
  return request({
    url: '/api/v2/course/vt/getCourse',
    method: 'get',
    params
  })
}

// 获取ai课程包信息
export function getAiCourse (params) {
  return request({
    url: '/api/v2/aicourse/vt/getAiCourse',
    method: 'get',
    params
  })
}

// 获取AI课程包单元列表信息
export function getAiCourseUnitList (params) {
  return request({
    url: '/api/v2/aicourse/vt/getAiCourseUnitList',
    method: 'get',
    params
  })
}

// 学情交流
export function communicateLearning (params) {
  return request({
    url: '/api/v2/lesson/communicateLearning',
    method: 'post',
    params
  })
}

// 学情交流列表
export function learningCommunicationList (params) {
  return request({
    url: '/api/v2/lesson/learningCommunicationList',
    method: 'get',
    params
  })
}

/**
 * 获取用户AI课程包某单元答题结果汇总信息
 * @param {number} studentCourseId
 * @param {number} aicourseUnitId
 * @returns
 */
export function getAicourseUnitUser (params) {
  return request({
    url: 'api/v2/aicourse/getAicourseUnitUser',
    method: 'get',
    params
  })
}

/**
 * 获取ai互动课首页宾果数
 * @returns
 */
export function getWallet (params) {
  return request({
    url: 'api/v2/wallet/getWallet',
    method: 'get',
    params
  })
}

/**
 * 获取AI课程包单元包信息
 * @returns
 */
export function getAiCourseUnitSectionList (params) {
  return request({
    url: 'api/v2/aicourse/vt/getAiCourseUnitSectionList',
    method: 'get',
    params
  })
}

/**
 * 获取AI课程包单元片段信息
 * @returns
 */
export function getAiCourseUnitSection (params) {
  return request({
    url: 'api/v2/aicourse/vt/getAiCourseUnitSection',
    method: 'get',
    params
  })
}

// AI课家长获取学科教研列表
export function getAiCourseListCjt (params) {
  return request({
    url: '/api/v2/aicourse/vt/getAiCourseListCjt',
    method: 'get',
    params
  })
}
// AI课家长获取当前课时片段的作业
export function getUserAiCourseWorks (params) {
  return request({
    url: 'api/v2/aicourse/vt/getUserAiCourseWorks',
    method: 'get',
    params
  })
}

// AI课家长提交作业
export function submitAiCourseWorks (data) {
  return request({
    url: 'api/v2/aicourse/vt/submitAiCourseWorks',
    method: 'post',
    data
  })
}

export function exchangeEmpowerCourse (params) {
  return request({
    url: 'api/v2/course/exchangeEmpowerCourse',
    method: 'post',
    params
  })
}
export function getAiCourseBook (params) {
  return request({
    url: 'api/v2/aicourse/vt/getAiCourseBook',
    method: 'get',
    params
  })
}

export function getCourseList (params) {
  return request({
    url: '/api/v2/course/getCourseList',
    method: 'get',
    params
  })
}
export function getSubjectConfig (params) {
  return request({
    url: '/api/v2/comm/vt/getSubjectConfig',
    method: 'get',
    params
  })
}

export function courseApply (params) {
  return request({
    url: '/api/v1/coursecomm/vt/courseApply',
    method: 'post',
    params
  })
}

export function getAiCourseList (params = {}) {
  addLocationNameParam(params)

  return request({
    url: '/api/v2/course/getAiCourseList',
    method: 'get',
    params
  })
}

export function getAiCourseResourceList (params) {
  return request({
    url: '/api/v2/course/getAiCourseResourceList',
    method: 'get',
    params
  })
}
export function getTrainingByLinkType (params) {
  return request({
    url: '/api/v1/training/getTrainingByLinkType',
    method: 'get',
    params
  })
}
export function getStudentCourseList (params = {}) {
  addLocationNameParam(params)

  return request({
    url: '/api/v2/lesson/getStudentCourseList',
    method: 'get',
    params
  })
}

export function getTrainingPresetFile (params) {
  localStorage.setItem('jhub-userId', params.userId)
  return request({
    url: '/api/v1/training/vt/setTrainingPresetFile',
    method: 'post',
    params
  })
}
export function getSupportLocation () {
  return request({
    url: '/api/v2/course/vt/getSupportLocation',
    method: 'get'
  })
}
