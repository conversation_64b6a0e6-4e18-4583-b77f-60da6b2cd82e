import request from '@/utils/request'

export function getBook (params, headers = {}) {
  return request({
    url: '/api/v1/digital/vt/getBook',
    method: 'get',
    params,
    headers
  })
}
export function batchEditBookCatalogue(params, headers = {}) {
  return request({
    url: '/api/v1/digital/batchEditBookCatalogue',
    method: 'post',
    data: params
  })
}
export function getBookCatalogue (params, headers = {}) {
  return request({
    url: '/api/v1/digital/vt/getBookCatalogue',
    method: 'get',
    params,
    headers
  })
}
export function getBookCatalogueByVersion (params, headers = {}) {
  return request({
    url: '/api/v1/digital/vt/getBookCatalogueByVersion',
    method: 'get',
    params,
    headers
  })
}
export function editBookCatalogue (params, headers = {}) {
  return request({
    url: '/api/v1/digital/editBookCatalogue',
    method: 'post',
    data: params,
    headers
  })
}

export function deleteBookCatalogue (params, headers = {}) {
  return request({
    url: '/api/v1/digital/deleteBookCatalogue',
    method: 'post',
    params,
    headers
  })
}

export function saveContent (params, headers = {}) {
  return request({
    url: '/api/v1/digital/saveContent',
    method: 'post',
    data: params,
    headers
  })
}

export function getContentEditor (params, headers = {}) {
  return request({
    url: '/api/v1/digital/vt/getContentEditor',
    method: 'get',
    params,
    headers
  })
}
export function getContent (params, headers = {}) {
  return request({
    url: '/api/v1/digital/vt/getContent',
    method: 'get',
    params,
    headers
  })
}

export function dragCatalogue (params, headers = {}) {
  return request({
    url: '/api/v1/digital/dragCatalogue',
    method: 'post',
    params,
    headers
  })
}

export function takeBook (params, headers = {}) {
  return request({
    url: '/api/v1/digital/takeBook',
    method: 'get',
    params,
    headers
  })
}

export function bookList (params) {
  return request({
    url: '/api/v1/digital/vt/getBookList',
    method: 'get',
    params
  })
}

export function userDigitalBooks (params, headers = {}) {
  return request({
    url: '/api/v1/digital/userDigitalBooks',
    method: 'get',
    params,
    headers
  })
}

export function exchangeEmpowerCourse (params) {
  return request({
    url: '/api/v2/course/exchangeEmpowerCourse',
    method: 'post',
    params
  })
}

export function empowerExchangeGoodsInfo (params) {
  return request({
    url: '/api/v1/goods/vt/empowerExchangeGoodsInfo',
    method: 'get',
    params
  })
}

export function getUserClassList (params) {
  return request({
    url: '/api/v2/user/getUserClassList',
    method: 'get',
    params
  })
}

export function assistantDigitalBooks (params) {
  return request({
    url: '/api/v1/digital/assistantDigitalBooks',
    method: 'get',
    params
  })
}
export function getClassInfo (params) {
  return request({
    url: '/api/v2/user/getClassInfo',
    method: 'get',
    params
  })
}
export function joinClass (params) {
  return request({
    url: '/api/v2/user/joinClass',
    method: 'post',
    params
  })
}

export function quitClass (params) {
  return request({
    url: '/api/v2/user/quitClass',
    method: 'post',
    params
  })
}

export function getUserCatalogue (params, headers = {}) {
  return request({
    url: '/api/v1/digital/getUserCatalogue',
    method: 'get',
    params,
    headers
  })
}

export function getUserLotteryInfo (params) {
  return request({
    url: '/api/v2/user/getUserInfo',
    method: 'get',
    params
  })
}

export function getLessonGroup (params) {
  return request({
    url: '/api/v2/lesson/vt/getLessonGroup',
    method: 'get',
    params
  })
}

export function groupLesson (params) {
  return request({
    url: '/api/v2/lesson/vt/groupLesson',
    method: 'post',
    data: params
  })
}

export function changeGroupScore (params) {
  return request({
    url: '/api/v2/lesson/vt/changeGroupScore',
    method: 'post',
    params
  })
}

export function aiLessonRank (params) {
  return request({
    url: '/api/v2/aicourse/vt/aiLessonRank',
    method: 'post',
    params
  })
}

export function getLessonCatalogues (params) {
  return request({
    url: '/api/v1/digital/getLessonCatalogues',
    method: 'get',
    params
  })
}

export function getBookCatalogueResource (params) {
  return request({
    url: '/api/v1/digital/vt/getBookCatalogueResource',
    method: 'get',
    params
  })
}

export function removeBookCatalogueResource (params) {
  return request({
    url: '/api/v1/digital/removeBookCatalogueResource',
    method: 'post',
    params
  })
}

export function addBookCatalogueResource (params, data) {
  return request({
    url: '/api/v1/digital/addBookCatalogueResource',
    method: 'post',
    params,
    data
  })
}

export function updateReadProgress (params) {
  return request({
    url: '/api/v1/digital/updateReadProgress',
    method: 'post',
    params
  })
}

export function digitalHomework (params, data) {
  return request({
    url: '/api/v1/digital/digitalHomework',
    method: 'post',
    params,
    data
  })
}

export function getQuestionBankList (params) {
  return request({
    url: '/api/v2/question/vt/getQuestionBankList',
    method: 'get',
    params
  })
}

export function getDigitalHomeworkList (params) {
  return request({
    url: '/api/v1/digital/getDigitalHomeworkList',
    method: 'get',
    params
  })
}

export function getDigitalHomeworkProgress (params) {
  return request({
    url: '/api/v1/digital/getDigitalHomeworkProgress',
    method: 'get',
    params
  })
}

export function addResource (params, data, headers = {}) {
  return request({
    url: '/api/v1/coursecomm/addResource',
    method: 'post',
    params,
    data,
    headers
  })
}
export function saveMediaFile (params, data, headers = {}) {
  return request({
    url: '/api/v2/comm/saveMediaFile',
    method: 'post',
    params,
    data,
    headers
  })
}

export function getResourceList (params, headers = {}) {
  return request({
    url: '/api/v1/coursecomm/getResourceList',
    method: 'get',
    params,
    headers
  })
}

export function deleteResource (params, headers = {}) {
  return request({
    url: '/api/v1/coursecomm/deleteResource',
    method: 'post',
    params,
    headers
  })
}

export function userDigitalHomework (params, headers = {}) {
  return request({
    url: '/api/v1/digital/userDigitalHomework',
    method: 'post',
    params,
    headers
  })
}

export function getUserDigitalHomeworkNotice (params) {
  return request({
    url: '/api/v1/digital/getUserDigitalHomeworkNotice',
    method: 'get',
    params
  })
}

export function getDigitalBookProgress (params) {
  return request({
    url: '/api/v1/digital/getDigitalBookProgress',
    method: 'get',
    params
  })
}

export function getUserDigitalBookProgress (params) {
  return request({
    url: '/api/v1/digital/getUserDigitalBookProgress',
    method: 'get',
    params
  })
}
export function getDigitalBookEmbedResourceList (params) {
  return request({
    url: '/api/v1/digital/getDigitalBookEmbedResourceList',
    method: 'get',
    params
  })
}
export function feedBack (params) {
  return request({
    url: '/api/v2/notice/feedback',
    method: 'post',
    params
  })
}
export function digitalNotes (params) {
  return request({
    url: '/api/v1/digital/digitalNotes',
    method: 'post',
    params
  })
}
export function getDigitalNotesList (params) {
  return request({
    url: '/api/v1/digital/getDigitalNotesList',
    method: 'get',
    params
  })
}
export function hasKnowledge (params) {
  return request({
    url: '/api/v2/comm/vt/hasKnowledge',
    method: 'get',
    params
  })
}
export function getKnowledgeLinkList (params) {
  return request({
    url: '/api/v2/comm/vt/getKnowledgeLinkList',
    method: 'get',
    params
  })
}
export function getDigitalCatalogueAuthorList (params, headers = {}) {
  return request({
    url: '/api/v1/digital/review/getDigitalCatalogueAuthorList',
    method: 'get',
    params,
    headers
  })
}
export function getDigitalContentChangeLog (params, headers = {}) {
  return request({
    url: '/api/v1/digital/vt/getDigitalContentChangeLog',
    method: 'get',
    params,
    headers
  })
}
export function getDigitalAuthorList (params, headers = {}) {
  return request({
    url: '/api/v1/digital/review/getDigitalAuthorList',
    method: 'get',
    params,
    headers
  })
}
export function deleteDigitalAuthor (params, headers = {}) {
  return request({
    url: '/api/v1/digital/review/deleteDigitalAuthor',
    method: 'post',
    params,
    headers
  })
}
export function verifyDigitalAuthorInviteValid (params) {
  return request({
    url: '/api/v1/digital/review/vt/verifyDigitalAuthorInviteValid',
    method: 'get',
    params
  })
}
export function digitalTask (params, data, headers = {}) {
  return request({
    url: '/api/v1/digital/vt/digitalTask',
    method: 'post',
    params,
    data,
    headers
  })
}
export function getDigitalTaskList (params, headers) {
  return request({
    url: '/api/v1/digital/vt/getDigitalTaskList',
    method: 'get',
    params,
    headers
  })
}
export function getDigitalTaskListByCatalogueId (params, headers) {
  return request({
    url: '/api/v1/digital/vt/getDigitalTaskListByCatalogueId',
    method: 'get',
    params,
    headers
  })
}

export function dragDigitalTask (params, headers) {
  return request({
    url: '/api/v1/digital/dragDigitalTask',
    method: 'post',
    params,
    headers
  })
}
export function getBookCatalogueByVersionForLesson (params, headers) {
  return request({
    url: '/api/v1/digital/vt/getBookCatalogueByVersionForLesson',
    method: 'get',
    params,
    headers
  })
}
export function getCatalogueTreeContent (params, headers) {
  return request({
    url: '/api/v1/digital/getCatalogueTreeContent',
    method: 'get',
    params,
    headers
  })
}
export function digitalBookConfig (params, headers) {
  return request({
    url: '/api/v1/digital/digitalBookConfig',
    method: 'post',
    params,
    headers
  })
}
export function getDigitalBookConfig (params, headers) {
  return request({
    url: '/api/v1/digital/vt/getDigitalBookConfig',
    method: 'get',
    params,
    headers
  })
}

export function castingShareFile (data, params) {
  return request({
    url: '/api/v2/comm/vt/castingShareFile',
    method: 'post',
    data,
    params
  })
}

export function getCastingShareFileList (params) {
  return request({
    url: '/api/v2/comm/vt/getCastingShareFileList',
    method: 'get',
    params
  })
}
export function getDigitalCatalogueData (params) {
  return request({
    url: '/api/v1/digital/vt/getDigitalCatalogueData',
    method: 'get',
    params
  })
}
export function getSchoolInfo (params) {
  return request({
    url: '/api/v2/user/getSchoolInfo',
    method: 'get',
    params
  })
}
export function getDigitalBookReviewRecord (params) {
  return request({
    url: '/api/v1/digital/review/getDigitalBookReviewRecord',
    method: 'get',
    params
  })
}
export function checkAuthCodeValideStatus (params) {
  return request({
    url: '/api/v1/digital/vt/isDigitalBookReadable',
    method: 'get',
    params
  })
}

export function generateTestpaperByAigc (data) {
  return request({
    url: '/api/v2/question/vt/generateTestpaperByAigc',
    method: 'post',
    data
  })
}

export function batchQuestion (data, params, headers) {
  return request({
    url: '/api/v2/question/batchQuestion',
    method: 'post',
    params,
    data,
    headers
  })
}

export function getUserErrorQuestionList (params) {
  return request({
    url: '/api/v2/question/getUserErrorQuestionList',
    method: 'get',
    params
  })
}

export function userErrorQuestion (data) {
  return request({
    url: '/api/v2/question/userErrorQuestion',
    method: 'post',
    data
  })
}
