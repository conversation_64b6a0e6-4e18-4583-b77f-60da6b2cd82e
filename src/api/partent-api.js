import request from '@/utils/request'
import { getPartentToken } from '@/utils/auth'
import { addLocationNameParam } from '@/utils/locationHelper'

export function parentBindChild (params, data) {
  return request({
    url: '/api/v2/user/parentBindChild',
    method: 'post',
    headers: {
      'authorization': getPartentToken()
    },
    params,
    data
  })
}

export function getSchoolList (params) {
  return request({
    url: '/api/v2/user/vt/getSchoolList',
    method: 'get',
    headers: {
      'authorization': getPartentToken()
    },
    params
  })
}

export function getSchoolClassList (params) {
  return request({
    url: '/api/v2/user/vt/getSchoolClassList',
    method: 'get',
    headers: {
      'authorization': getPartentToken()
    },
    params
  })
}

export function getParentChildren (params) {
  return request({
    url: '/api/v2/user/getParentChildren',
    method: 'get',
    headers: {
      'authorization': getPartentToken()
    },
    params
  })
}

export function getChildInfo (params) {
  return request({
    url: '/api/v2/user/getChildInfo',
    method: 'get',
    headers: {
      'authorization': getPartentToken()
    },
    params
  })
}
export function getOrganizationList (params) {
  return request({
    url: '/api/v2/user/vt/getOrganizationList',
    method: 'get',
    headers: {
      'authorization': getPartentToken()
    },
    params
  })
}

export function getStudentCourseList (params = {}) {
  addLocationNameParam(params)
  return request({
    url: '/api/v2/lesson/getStudentCourseList',
    method: 'get',
    headers: {
      'authorization': getPartentToken()
    },
    params
  })
}
export function getGzhUserInfo (params) {
  return request({
    url: '/api/v2/user/getGzhUserInfo',
    method: 'get',
    headers: {
      'authorization': getPartentToken()
    },
    params
  })
}
export function unBindUser (params) {
  return request({
    url: '/api/v2/user/unBindUser',
    method: 'get',
    headers: {
      'authorization': getPartentToken()
    },
    params
  })
}

export function bindUser (params) {
  return request({
    url: '/api/v2/user/vt/bindUser',
    method: 'get',
    headers: {
      'authorization': getPartentToken()
    },
    params
  })
}
export function bindMobile (params) {
  return request({
    url: '/api/v2/user/bindMobile',
    method: 'post',
    headers: {
      'authorization': getPartentToken()
    },
    params
  })
}

export function pLogin (mobileOrEamil, password, loginType, code) {
  let param = {}
  if (loginType === 'PASSWORD') {
    param = {
      mobileOrEamil: mobileOrEamil,
      password: password,
      userType: 'PARENT',
      loginType: loginType
    }
  } else {
    param = {
      mobileOrEamil: mobileOrEamil,
      userType: 'PARENT',
      loginType: loginType,
      authCode: code
    }
  }
  return request({
    url: '/api/v2/user/vt/login',
    method: 'get',
    params: param
  })
}

export function updateMobileOrEmail (params) {
  return request({
    url: '/api/v2/user/updateMobileOrEmail',
    method: 'post',
    headers: {
      'authorization': getPartentToken()
    },
    params
  })
}

export function getWorkSubmitHistory (params) {
  return request({
    url: 'api/v2/aicourse/getWorkSubmitHistory',
    method: 'get',
    headers: {
      'authorization': getPartentToken()
    },
    params
  })
}

export function getClassInfo (params) {
  return request({
    url: 'api/v2/user/getClassInfo',
    method: 'get',
    headers: {
      'authorization': getPartentToken()
    },
    params
  })
}
export function parentQuitChildInterestClass (params) {
  return request({
    url: '/api/v2/user/parentQuitChildInterestClass',
    method: 'post',
    headers: {
      'authorization': getPartentToken()
    },
    params
  })
}
