import request from '@/utils/request'

/**
 * 获取数字作业统计数据
 * @param {Object} params - 请求参数
 * @param {number} params.digital_homework_id - 数字作业ID
 * @param {Object} headers - 请求头配置
 * @returns {Promise} API响应
 */
export function getStatisticsData(params, headers = {}) {
  return request({
    url: process.env.VUE_APP_ADMIN_API + '/api/digitalHomework/statisticsData',
    method: 'post',
    data: params,
    headers,
    urlType: 'admin'
  })
}

/**
 * 获取知识点掌握情况数据
 * @param {Object} params - 请求参数
 * @param {number} params.digital_homework_id - 数字作业ID
 * @param {Object} headers - 请求头配置
 * @returns {Promise} API响应
 */
export function getKnowledgeData(params, headers = {}) {
  return request({
    url: process.env.VUE_APP_ADMIN_API + '/api/digitalHomework/knowledgeData',
    method: 'post',
    data: params,
    headers,
    urlType: 'admin'
  })
}

/**
 * 获取学生成绩详情列表
 * @param {Object} params - 请求参数
 * @param {number} params.digital_homework_id - 数字作业ID
 * @param {Object} headers - 请求头配置
 * @returns {Promise} API响应
 */
export function getStudentList(params, headers = {}) {
  return request({
    url: process.env.VUE_APP_ADMIN_API + '/api/digitalHomework/studentList',
    method: 'post',
    data: params,
    headers,
    urlType: 'admin'
  })
}

/**
 * 获取AI教学建议
 * @param {Object} params - 请求参数
 * @param {number} params.digital_homework_id - 数字作业ID
 * @param {number} params.average_score - 平均分
 * @param {number} params.finished_rate - 完成率
 * @param {number} params.correct_rate - 正确率
 * @param {number} params.average_time - 平均完成时间
 * @param {Object} params.score_distribute - 分数分布
 * @param {Array} params.knowledge_situation - 知识点掌握情况
 * @param {Object} headers - 请求头配置
 * @returns {Promise} API响应
 */
export function getAiComment(params, headers = {}) {
  return request({
    url: process.env.VUE_APP_ADMIN_API + '/api/digitalHomework/aiComment',
    method: 'post',
    data: params,
    headers,
    urlType: 'admin'
  })
}
