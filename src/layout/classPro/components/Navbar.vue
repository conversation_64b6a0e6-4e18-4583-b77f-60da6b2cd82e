<template>
  <div class="navbar">
    <div class="left">
<!--      <img v-if="channel === 'digitalbook'" class="logo1" src="@/assets/images/login/logo-digitalbook.png" alt="图标" @click="backToDashboard" />-->
      <img v-if="channel !== 'digitalbook'" class="logo" src="@/assets/images/login/logo.png" alt="图标" @click="backToDashboard" />
      <!-- <router-link to="/classpro" tag="span">
        首页
        <div class="line"></div>
      </router-link>
      <router-link v-show="showCourseCenter" to="/course" tag="span">
        课程
        <div class="line"></div>
      </router-link>
      <router-link to="/educational" tag="span">
        教务
        <div class="line"></div>
      </router-link>
      <router-link to="/activity" tag="span">
        活动
        <div class="line"></div>
      </router-link>
      <router-link to="/data" tag="span">
        数据
        <div class="line"></div>
      </router-link> -->
    </div>
    <div class="user">
      <!-- <div v-if="userRelations.length > 1" class="grade-btn" style="padding-left:12px" @click="showGradeDialog = true">
        <p>{{ getGradeName() }}</p>
        <i class="iconfont icon-xialabeifen"></i>
      </div> -->
      <!-- <div v-if="false" class="grade-btn" style="z-index:1">
        <img :src="coin" alt="" />
        <p>0</p>
      </div> -->
      <!-- <div v-if="guideProgress === null || guideProgress === 1" class="guide-des">
        <div class="guide-des__top">
          <div class="line"></div>
          <div class="circle"></div>
        </div>
        <div class="name">班级切换</div>
        <div class="explain">这里可以更改「班级名称」和「切换班级」哦～</div>
        <div class="guide-btns">
          <div class="btn classpro-btn-disable left-btn" @click="setHomeGuideProgress(4)">跳过</div>
          <div class="btn classpro-btn right-btn" @click="setHomeGuideProgress(2)">下一步</div>
        </div>
      </div> -->
      <!-- <div class="guide-btn">
        <img :src="btnGuide" alt="" @click="openVideo" />
      </div> -->
      <location-display />
      <div class="bloc-refresh" @click="reload">
        <!-- <div class="top">
          <div class="refresh-icon"></div>
        </div> -->
        <svg-icon
          class="icon-refresh"
          icon-class="refresh"
          class-name="refresh"
        />
        <p class="bloc-text">刷新</p>
      </div>
      <!-- <div class="bloc-recharge" @click="showExchange">
        <i class="iconfont icon-a-bianzu2"></i>
        <p class="bloc-text">课程兑换</p>
      </div> -->
      <div class="bloc-recharge" @click="bindBookShow = true">
<!--        <i class="iconfont icon-a-bianzu2"></i>-->
        <svg-icon
          class="icon-a-bianzu2"
          icon-class="exchange"
          class-name="exchange"
        />
        <p class="bloc-text">兑换</p>
      </div>
      <setting-pop-over />
      <info-pop-over
        :avatar="avatar || DefaultAvatar"
        :name="name"
        :mobile="mobile"
        :school-name="schoolName"
      />
    </div>

    <div v-if="showVideo" class="navbar-video">
      <div class="shadow"></div>
      <div class="video-container">
        <my-video :options="videoOptions" />
        <div class="close" @click="closeVideo">
          <svg-icon icon-class="video-close" class-name="close-icon" />
        </div>
      </div>
    </div>

    <BindBook
      v-if="bindBookShow"
      :show="bindBookShow"
      :append-to-body="true"
      @close="bindBookShow = false"
    />
  </div>
</template>

<script>
import InfoPopOver from '@/components/classPro/Navbar/InfoPopOver'
import BindBook from '@/views/digitalbooks/myDigitalbooks/components/Dialog/bindBook.vue'
import SettingPopOver from '@/components/classPro/Navbar/SettingPopOver'
import MyVideo from '@/components/classPro/video'
import LocationDisplay from '@/components/LocationDisplay'
import { mapGetters } from 'vuex'
import DefaultAvatar from '@/assets/images/profile.png'
import iconChange from '@/assets/images/Navbar/icon-change.png'
import coin from '@/assets/images/coin.png'
import btnGuide from '@/assets/images/btn-guide.png'
import { getChildName, getChildId } from '@/utils/auth'
import localStore from '@/utils/local-storage.js'
import { getAiConfig } from '@/api/dictionary-api.js'
const ipc = window.ipc

export default {
  components: {
    InfoPopOver,
    SettingPopOver,
    MyVideo,
    BindBook,
    LocationDisplay
  },
  data () {
    return ({
      DefaultAvatar,
      iconChange,
      btnGuide,
      showGradeDialog: false,
      getChildName,
      getChildId,
      guideProgress: 1,
      coin,
      isElectron: !!ipc,
      showVideo: false,
      bindBookShow: false,
      channel: '',
      videoOptions: {
        preload: 'auto',
        controls: true,
        autoplay: true,
        fileShow: false,
        loop: false,
        controlBar: {
          children: [
            { name: 'playToggle' }, // 播放按钮
            { name: 'currentTimeDisplay' }, // 当前已播放时间
            { name: 'durationDisplay' }, // 总时间
            {
              name: 'volumePanel', // 音量控制
              inline: false // 不使用水平方式
            },
            // { name: 'FullscreenToggle' }, // 全屏
            { name: 'progressControl' }, // 播放进度条
            { name: 'remainingTimeDisplay' }
          ]
        }
      }
    })
  },
  computed: {
    schoolName () {
      return this.school ? this.school.name : ''
    },
    ...mapGetters([
      'name',
      'avatar',
      'mobile',
      'school',
      'childName',
      'userRelations',
      'showCourseCenter'
    ])
  },
  created () {
    // this.getVideoConfig()
    this.getChannelConfig()
  },
  mounted () {
    this.guideProgress = localStore.read('guideProgress')
    if ((this.guideProgress === null || this.guideProgress === 1) && this.$store.getters.userRelations.length < 2) {
      this.guideProgress = 2
    }
  },
  methods: {
    async getChannelConfig () {
      try {
        if (window.ipc) {
          const res = await window.ipc.invoke('getChannelConfig')
          this.channel = res
        } else {
          const channel = window.localStorage.getItem('channel')
          if (channel) {
            this.channel = channel
          }
        }
      } catch (error) {
        console.log(error)
      }
    },
    showExchange () {
      this.$emit('showExchange')
    },
    togglePhone () {
      this.isEditPhone = !this.isEditPhone
    },
    showForgetDialog () {
      this.forgetDialogVisible = true
    },
    closeForgetDialog () {
      this.forgetDialogVisible = false
    },
    backToDashboard () {
      this.$router.push('/classpro')
    },
    reload () {
      location.reload()
    },
    setHomeGuideProgress (progress) {
      this.guideProgress = progress
      this.$bus.$emit('setHomeGuideProgress', progress)
    },
    getGradeName () {
      if (getChildName()) return getChildName()
      for (var grade of this.$store.getters.userRelations) {
        if (grade.toUserId.toString() === this.$store.getters.id.toString()) return grade.toUserDisplayName
      }
      return this.$store.getters.name
    },
    openVideo () {
      if (this.videoOptions.sources) {
        this.showVideo = true
      } else {
        this.$message.error('暂无上课指导视频')
      }
    },
    closeVideo () {
      this.showVideo = false
    },
    async getVideoConfig () {
      const prams = {
        'configType': 'AI_GUIDE_VIDEO'
      }
      const response = await getAiConfig(prams)
      if (response && response.data.length > 0) {
        this.videoOptions = {
          ...this.videoOptions,
          sources: [{
            src: response.data[0].keyValue,
            type: 'video/mp4'
          }]
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
    background: url('../../../assets/images/bg-navbar.png') center center no-repeat;
    background-size: cover;
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px 0 28px;
    box-shadow: 0px 2px 13px 0px rgba(62, 89, 253, 0.04);
    position: relative;

    .left {
      display: flex;
      align-items: center;

      .logo {
          width: 100px;
          //height: 50px;
          margin-right: 71px;
          cursor: pointer;
          object-fit: contain;
      }
      .logo1 {
          width: 80px;
          margin-right: 71px;
          cursor: pointer;
          object-fit: contain;
      }
    }
    span {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 16px;
      color: #7F7F7F;
      letter-spacing: 0.26px;
      margin-right: 40px;
      cursor: pointer;

      .line {
        width: 50px;
        height: 2px;
        background: transparent;
        position: absolute;
        bottom: 0;
      }

      &.router-link-active {
        color: #3479FF;
        font-weight: bold;
        display: flex;
        flex-direction: column;
        align-items: center;

        .line {
          background: #3479FF;
          margin-top: auto;
        }
      }
    }

    .top {
      height: 24px;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
    }

    .icon-a-shuaxin2x,
    .icon-a-bianzu2 {
      width: 20px;
      height: 20px;
      font-size: 16px;
      border-radius: 5px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  .icon-refresh{
    width: 15px;
    height: 15px;
    font-size: 16px;
    border-radius: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 2px;
    margin-bottom: 3px;
  }

    .guide-btn {
      height: 45px;
      margin-right: 30px;

      img {
        height: 100%;
        object-fit: contain;
        cursor: pointer;
      }
    }

    .bloc-refresh {
      margin-right: 10px;
      cursor: pointer;
      display: flex;
      flex-direction: column;
      align-items: center;

      .refresh-icon {
        height: 22px;
        background: url('../../../assets/images/Navbar/icon-refresh.png') center center no-repeat;
        background-size: contain;
      }

      &:hover {
        .refresh-icon {
          height: 22px;
          background: url('../../../assets/images/Navbar/icon-refresh-blue.png') center center no-repeat;
          background-size: contain;
        }

        .icon-a-shuaxin2x {
          color: #1F66FF;
          background: #E9F0FF;
        }

        p {
          color: #1F66FF;
        }
      }
    }

    .bloc-recharge {
      //height: 40px;
      margin-right: 10px;
      cursor: pointer;
      display: flex;
      flex-direction: column;
      align-items: center;

      .recharge-icon {
        height: 19px;
        background: url('../../../assets/images/Navbar/icon-recharge.png') center center no-repeat;
        background-size: contain;
        margin-bottom: 3px;
      }

      &:hover {
        .recharge-icon {
          height: 19px;
          background: url('../../../assets/images/Navbar/icon-recharge-blue.png') center center no-repeat;
          background-size: contain;
        }

        .icon-a-bianzu2 {
          color: #1F66FF;
          background: #E9F0FF;
        }

        p {
          color: #1F66FF;
        }
      }
    }

    .bloc-text {
      font-size: 12px;
      font-weight: 400;
      color: #1D1B1B;
      margin: 0;
    }

    .user {
        display: flex;
        align-items: center;
        position: relative;

        .grade-btn {
          z-index: 12;
          // transform: translate(-100%,0);
          padding: 0 15px 0 0;
          height: 20px;
          background: #E9F0FF;
          border-radius: 15px;
          font-family: PingFangSC-Medium;
          font-weight: 500;
          font-size: 14px;
          color: #1F66FF;
          line-height: 30px;
          display: flex;
          align-items: center;
          cursor: pointer;
          margin-right: 20px;

          p {
            margin: 0;
            max-width: 300px;
            @include ellipsis;
          }

          img {
            width: 20px;
            height: 20px;
            object-fit: contain;
            margin-left: 6px;
            margin-right: 9px;
          }

          .icon-xialabeifen {
            margin-left: 13px;
            transform:rotate(90deg);
          }
        }

        .guide-container {
          position: absolute;
          z-index:11;
          background: #F8FAFF 100%;
          font-size: 21px;
          z-index: 11;
          border-radius: 100px;
          display: flex;
          padding: 18px 41px 18px 36px;
          transform: translate(-100%,0);
          left: -32px;

          .placeholder {
            width: 42px;
            height: 16px;
          }
        }

        .name {
            font-size: 14px;
            font-weight: 400;
            color: #8C9399;
            line-height: 20px;
            min-width: 40px;
        }
    }

    .navbar-video {
      position: absolute;
      left: 0;
      top: 0;
      width: 100vw;
      height: 100vh;
      z-index: 10;

      .shadow {
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.2);
      }

      .video-container {
        position: absolute;
        transform: translate(-50%, -50%);
        left: 50%;
        top: 50%;
        width: 1030px;
        height: 655px;
      }

      .close {
        position: absolute;
        object-fit: contain;
        top: 12px;
        right: 16px;
        width: 30px;
        height: 30px;
        cursor: pointer;

        .close-icon {
          width: 100%;
          height: 100%;
        }
      }
    }

.guide-des {
  position: absolute;
  z-index: 12;
  transform: translate(-100%,0);
  top: 80px;
  right: -260px;

    &__top {
      width: 12px;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 0 0 12px 35px;
    }

    .line {
      width: 4px;
      height: 89px;
      background: linear-gradient(62deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
    }

    .circle {
      width: 12px;
      height: 12px;
      border-radius: 100px;
      background: #FFFFFF;
    }

    .name {
      font-size: 20px;
      color: #FFFFFF;
      line-height: 28px;
    }

    .explain {
      font-size: 18px;
      color: #FFFFFF;
      line-height: 25px;
      margin: 13px 0 20px;
    }

    .guide-btns {
      display: flex;
      .btn {
        width: 130px;
        height: 30px;
        margin-right: 18px;
        cursor: pointer;
      }
    }
  }
}
</style>
