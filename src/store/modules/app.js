import Cookies from 'js-cookie'

const state = {
  sidebar: {
    opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,
    withoutAnimation: false
  },
  device: 'desktop',
  update: 0,
  refreshCourse: 0,
  activeCatalogueId: 0,
  locationName: '',
  selectedRegion: '',
  supportedRegions: []
}

const mutations = {
  TOGGLE_SIDEBAR: state => {
    state.sidebar.opened = !state.sidebar.opened
    state.sidebar.withoutAnimation = false
    if (state.sidebar.opened) {
      Cookies.set('sidebarStatus', 1)
    } else {
      Cookies.set('sidebarStatus', 0)
    }
  },
  CLOSE_SIDEBAR: (state, withoutAnimation) => {
    Cookies.set('sidebarStatus', 0)
    state.sidebar.opened = false
    state.sidebar.withoutAnimation = withoutAnimation
  },
  TOGGLE_DEVICE: (state, device) => {
    state.device = device
  },
  SET_UPDATE: (state) => {
    state.update += 1
  },
  REFRESH_COURSE: (state) => {
    state.refreshCourse += 1
  },
  SET_CATALOGUEID: (state, id) => {
    state.activeCatalogueId = id
  },
  SET_LOCATION_NAME: (state, locationName) => {
    state.locationName = locationName
  },
  SET_SELECTED_REGION: (state, region) => {
    state.selectedRegion = region
  },
  SET_SUPPORTED_REGIONS: (state, regions) => {
    state.supportedRegions = regions
  }
}

const actions = {
  toggleSideBar ({ commit }) {
    commit('TOGGLE_SIDEBAR')
  },
  closeSideBar ({ commit }, { withoutAnimation }) {
    commit('CLOSE_SIDEBAR', withoutAnimation)
  },
  toggleDevice ({ commit }, device) {
    commit('TOGGLE_DEVICE', device)
  },
  setUpdate ({ commit }) {
    commit('SET_UPDATE')
  },
  refreshCourseList ({ commit }) {
    commit('REFRESH_COURSE')
  },
  setCatalogueId({ commit }, id) {
    commit('SET_CATALOGUEID', id)
  },
  setLocationName({ commit }, locationName) {
    commit('SET_LOCATION_NAME', locationName)
  },
  setSelectedRegion({ commit }, region) {
    commit('SET_SELECTED_REGION', region)
  },
  setSupportedRegions({ commit }, regions) {
    commit('SET_SUPPORTED_REGIONS', regions)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
