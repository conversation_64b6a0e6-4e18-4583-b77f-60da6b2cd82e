<template>
  <div class="screen-protection"></div>
</template>

<script>
import Mousetrap from 'mousetrap'

export default {
  name: 'ScreenProtection',
  data() {
    return {
      isElectron: false,
      keydownHandler: null,
      recordingSoftwareDetected: [],
      recordingDetectionActive: false,
      persistentProtectionActive: false,
      currentProtectionLevel: 'none'
    }
  },
  mounted() {
    this.isElectron = window.navigator.userAgent.includes('Electron')

    if (this.isElectron) {
      this.initElectronProtection()
    } else {
      this.initWebProtection()
    }
  },

  beforeDestroy() {
    this.cleanupProtection()
  },
  methods: {
    initElectronProtection() {
      if (window.ipc) {
        window.ipc.on('screenshot-attempt-detected', this.handleScreenshotAttempt)
        window.ipc.on('recording-suspected', this.handleRecordingSuspected)
        window.ipc.on('recording-confirmed', this.handleRecordingConfirmed)
        window.ipc.on('recording-still-active', this.handleRecordingStillActive)
        window.ipc.on('recording-stopped', this.handleRecordingStopped)
      }
      this.setupKeybindingListeners()
      this.setupBasicWebProtection()
      this.setupRecordingSoftwareDetection()
    },

    initWebProtection() {
      this.enableKeyboardProtection()
      this.setupKeybindingListeners()
    },

    setupBasicWebProtection() {
      this.enableKeyboardProtection()
    },

    enableKeyboardProtection() {
      this.keydownHandler = (e) => {
        const key = e.key?.toLowerCase()
        const keyCode = e.keyCode || e.which

        if (keyCode === 44 || key === 'printscreen') {
          e.preventDefault()
          this.handleScreenshotDetected(e)
          return false
        }
      }

      document.addEventListener('keydown', this.keydownHandler)
    },

    handleScreenshotDetected(event) {
      if (event) {
        event.preventDefault()
        event.stopPropagation()
      }
      this.showProtectionEffect()
    },

    showProtectionEffect() {
      if (this.$root.$showScreenProtection) {
        this.$root.$showScreenProtection({
          duration: 3000
        })
      }
    },

    cleanupProtection() {
      if (this.keydownHandler) {
        document.removeEventListener('keydown', this.keydownHandler)
      }

      Mousetrap.reset()

      if (this.isElectron && window.ipc) {
        window.ipc.removeAllListeners('screenshot-attempt-detected')
        window.ipc.removeAllListeners('recording-suspected')
        window.ipc.removeAllListeners('recording-confirmed')
        window.ipc.removeAllListeners('recording-still-active')
        window.ipc.removeAllListeners('recording-stopped')
      }
    },
    handleScreenshotAttempt(event, data) {
      this.showProtectionEffect()
    },

    setupKeybindingListeners() {
      const commonKeys = [
        'printscreen',
        'ctrl+shift+s', 'control+shift+s', 'command+shift+s', 'meta+shift+s', 'cmd+shift+s'
      ]

      const electronKeys = [
        'command+shift+3', 'meta+shift+3', 'cmd+shift+3',
        'command+shift+4', 'meta+shift+4', 'cmd+shift+4',
        'command+shift+5', 'meta+shift+5', 'cmd+shift+5'
      ]

      Mousetrap.bind(commonKeys, (e) => {
        this.handleMousetrapScreenshot(e)
        return false
      })

      if (this.isElectron) {
        Mousetrap.bind(electronKeys, (e) => {
          this.handleMousetrapScreenshot(e)
          return false
        })
      }

      Mousetrap.bind(['command+shift+r', 'ctrl+shift+r'], () => {
        this.emergencyRestore()
        return false
      })
    },

    handleMousetrapScreenshot(event) {
      if (event) {
        event.preventDefault()
        event.stopPropagation()
      }
      this.showProtectionEffect()
    },

    setupRecordingSoftwareDetection() {
    },

    handleRecordingSuspected(_, data) {
      const safeData = {
        detectedApps: Array.isArray(data?.detectedApps) ? data.detectedApps : [],
        confidence: data?.confidence || 0,
        timestamp: data?.timestamp || new Date().toLocaleString()
      }

      this.recordingDetectionActive = true
      this.currentProtectionLevel = 'suspected'
      this.recordingSoftwareDetected = safeData.detectedApps
    },

    handleRecordingConfirmed(_, data) {
      const safeData = {
        detectedApps: Array.isArray(data?.detectedApps) ? data.detectedApps : [],
        confidence: data?.confidence || 0,
        timestamp: data?.timestamp || new Date().toLocaleString()
      }

      this.recordingDetectionActive = true
      this.persistentProtectionActive = true
      this.currentProtectionLevel = 'confirmed'
      this.recordingSoftwareDetected = safeData.detectedApps
    },

    handleRecordingStillActive(_, data) {
      const safeDetectedApps = Array.isArray(data?.detectedApps) ? data.detectedApps : []
      this.recordingSoftwareDetected = safeDetectedApps
    },

    handleRecordingStopped() {
      this.recordingDetectionActive = false
      this.persistentProtectionActive = false
      this.currentProtectionLevel = 'none'
      this.recordingSoftwareDetected = []
      this.restoreNormalDisplay()
    },

    restoreNormalDisplay() {
      this.recordingDetectionActive = false
      this.persistentProtectionActive = false
      this.currentProtectionLevel = 'none'
      this.recordingSoftwareDetected = []

      if (this.$root.$hideScreenProtection) {
        this.$root.$hideScreenProtection()
      }

      const recoveryBtn = document.getElementById('recording-recovery-btn')
      if (recoveryBtn) {
        document.body.removeChild(recoveryBtn)
      }

      const directBlack = document.getElementById('direct-blackscreen')
      if (directBlack) {
        document.body.removeChild(directBlack)
      }
    },

    async triggerIntelligentDetection() {
      if (!window.ipc) {
        return
      }

      try {
        await window.ipc.invoke('triggerIntelligentDetection')
      } catch (error) {
      }
    },

    emergencyRestore() {
      this.recordingDetectionActive = false
      this.persistentProtectionActive = false
      this.currentProtectionLevel = 'none'
      this.recordingSoftwareDetected = []

      const blackScreens = document.querySelectorAll('.screen-protection-effect')
      blackScreens.forEach(el => {
        el.style.display = 'none'
        el.remove()
      })

      document.body.classList.remove('screenshot-blackscreen-mode')

      const directBlack = document.getElementById('direct-blackscreen')
      if (directBlack) {
        document.body.removeChild(directBlack)
      }

      const recoveryBtn = document.getElementById('recording-recovery-btn')
      if (recoveryBtn) {
        document.body.removeChild(recoveryBtn)
      }

      if (this.$root.$hideScreenProtection) {
        this.$root.$hideScreenProtection()
      }

      const app = this.$root
      if (app && app.$refs && app.$refs.screenProtectionEffect) {
        const component = app.$refs.screenProtectionEffect
        component.isVisible = false
        if (component.hideTimer) {
          clearTimeout(component.hideTimer)
          component.hideTimer = null
        }
      }

      alert('紧急恢复完成！如果仍有问题，请刷新页面。')
    }
  }
}
</script>

<style scoped lang="scss">
:global(.screenshot-blackscreen-mode) {
  background: #000 !important;
  * {
    visibility: hidden !important;
  }
}
</style>
