<template>
  <div v-if="isVisible" class="screen-protection-effect">
  </div>
</template>

<script>
export default {
  name: 'ScreenProtectionEffect',

  data() {
    return {
      isVisible: false,
      hideTimer: null,
      isElectron: false
    }
  },

  mounted() {
    this.isElectron = window.navigator.userAgent.includes('Electron')
  },

  methods: {
    show(options = {}) {
      if (this.isVisible) {
        return
      }

      this.isVisible = true
      this.applyBodyEffect()

      const duration = options.duration || 3000

      if (duration > 0) {
        this.hideTimer = setTimeout(() => {
          this.hide()
        }, duration)
      }

      this.$emit('protection-shown', {
        type: 'screen-protection',
        environment: this.isElectron ? 'electron' : 'web',
        duration: duration
      })
    },

    hide() {
      this.isVisible = false
      this.removeBodyEffect()

      if (this.hideTimer) {
        clearTimeout(this.hideTimer)
        this.hideTimer = null
      }
    },

    applyBodyEffect() {
      document.body.classList.add('screenshot-blackscreen-mode')
    },

    removeBodyEffect() {
      document.body.classList.remove('screenshot-blackscreen-mode')
    }
  },

  beforeDestroy() {
    this.clearTimers()
    this.removeBodyEffect()
  }
}
</script>

<style scoped>
.screen-protection-effect {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background: #000 !important;
  z-index: 2147483647 !important;
  pointer-events: none !important;
  opacity: 1 !important;
  visibility: visible !important;
}
</style>

<style>
body.screenshot-blackscreen-mode {
  overflow: hidden !important;
}
</style>
