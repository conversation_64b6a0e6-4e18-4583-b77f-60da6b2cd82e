<template>
  <el-dialog
    :visible.sync="visible"
    width="320px"
    :close-on-click-modal="true"
    :show-close="true"
    custom-class="location-select-dialog"
  >
    <div class="location-dialog">
      <div class="header-row">
        <span class="current-text">当前定位：{{ displayCurrentLocation || '未知' }}</span>
        <span class="support-title">当前支持区域</span>
      </div>

      <div class="location-grid">
        <div
          v-for="location in supportLocations"
          :key="location.id || location.name"
          class="location-item"
          :class="{ 'selected': selectedLocation === location.name }"
          @click="selectLocation(location)"
        >
          {{ location.name }}
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'LocationSelectDialog',
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      supportLocations: [],
      selectedLocation: ''
    }
  },
  computed: {
    ...mapGetters(['locationName']),
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    currentLocation() {
      return this.locationName || '定位中...'
    },
    displayCurrentLocation() {
      if (this.selectedLocation && this.selectedLocation !== '全国') {
        return this.selectedLocation
      }
      return this.locationName || '定位中...'
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initializeDialog()
      }
    }
  },
  methods: {
    initializeDialog() {
      const supportedRegions = this.$store.getters.supportedRegions
      const selectedRegion = this.$store.getters.selectedRegion
      this.supportLocations = [{ name: '全国' }]

      if (supportedRegions && supportedRegions.length > 0) {
        this.supportLocations.push(...supportedRegions)
      }
      this.selectedLocation = selectedRegion || '全国'
    },

    selectLocation(location) {
      this.selectedLocation = location.name
      this.handleClose()
      this.$emit('location-selected', location)
    },

    handleClose() {
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.location-dialog {
  background: white;
  padding: 16px;
  border-radius: 15px;

  .header-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .current-text {
      font-size: 14px;
      color: #000000;
    }

    .support-title {
      font-size: 12px;
      color: #666666;
    }
  }

  .location-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;

    .location-item {
      height: 28px;
      padding: 0 10px;
      background-color: #E0E0E0;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 12px;
      white-space: nowrap;

      &:hover {
        background-color: #D0D0D0;
      }

      &.selected {
        background-color: #2F80ED;
        color: #FFFFFF;
      }
    }
  }
}
</style>

<style lang="scss">
.location-select-dialog.el-dialog {
  width: 320px !important;
  max-width: 320px !important;
  min-width: 320px !important;
  height: auto !important;
  max-height: 200px !important;
  border-radius: 12px !important;
}

.location-select-dialog .el-dialog__body {
  padding: 0 !important;
  max-height: 200px !important;
  overflow: hidden !important;
}

.location-select-dialog .el-dialog__header {
  display: none !important;
}

.el-dialog__wrapper .location-select-dialog.el-dialog {
  width: 320px !important;
  max-width: 320px !important;
  min-width: 320px !important;
}

@media screen and (max-width: 768px) {
  .location-select-dialog.el-dialog {
    width: 320px !important;
    max-width: 320px !important;
    min-width: 320px !important;
  }
}
</style>
