<template>
  <NormalDialog
    v-if="show"
    width="500px"
    :title="'提示'"
    :dialog-visible="show"
    :append-to-body="true"
    :is-center="true"
    @closeDialog="show = false"
  >
    <div class='prompt-main'>
      <div class='prompt-content'>
        <i class="el-icon-warning-outline"></i>
        <span>{{ title }}</span>
      </div>
    </div>
    <template #footer>
      <div class='prompt-footer'>
        <el-button type='primary' size='mini' @click="handleConfirm">{{ confirmTitle }}</el-button>
        <el-button size='mini' @click='show = false'>取消</el-button>
      </div>
    </template>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index.vue'
export default {
  name: 'PromptView',
  components: { NormalDialog },
  props: {
    title: {
      type: String,
      default: '请填写提示内容'
    },
    confirmTitle: {
      type: String,
      default: '确认'
    }
  },
  data() {
    return {
      show: false
    }
  },
  methods: {
    open() {
      this.show = true
    },
    handleConfirm() {
      this.$emit('confirm')
      this.show = false
    }
  }
}
</script>

<style scoped lang='scss'>
.prompt-main{
  font-size: 18px;
  .prompt-content{
    display: flex;
    align-items: center;
    justify-content: center;
    i{
      font-size: 24px;
      color: rgba(255, 127, 40, 1);
      margin-right: 5px;
    }
  }
}
.prompt-footer{
  width: 100%;
  display: flex;
  gap: 10px;
  justify-content: center;
}
</style>
