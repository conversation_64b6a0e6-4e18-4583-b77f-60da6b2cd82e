<template>
  <div class='no-conversion' @click="handleClick" v-loading="loading" :class="{'no-conversion-h5' : source === 'h5'}">
    <JwChat-index
      v-model="inputMsg"
      :tale-list="list"
      :show-right-box="false"
      scroll-type="scroll"
      width="100%"
      height="100%"
      :config="config"
      :tool-config="toolConfig"
      placeholder="请输入你的问题..."
      @enter="bindEnter"
    >
      <template #enterBtn>
        <img
          style="width: 30px"
          src="@/assets/digitalbooks/read/send_button.png"
          alt=""
        />
      </template>
      <template #tools>
        <div v-if="source === 'web'" class="tools-view">
          <div style='height: 100%;display: flex;align-items: center;gap: 10px'>
            <div class="tool-btn" @click="createDialogue">
              <img src="@/assets/digitalbooks/tools-img/newDialogue.svg" />
              新对话
            </div>
            <div class="tool-btn" @click="type = 'AI对话'" :class="{'tool-btn-active' : type === 'AI对话'}">
              <img src="@/assets/digitalbooks/tools-img/dialogue.svg" />
              AI对话
            </div>
            <div class="tool-btn" @click="type = '图片生成'" :class="{'tool-btn-active' : type === '图片生成'}">
              <img src="@/assets/digitalbooks/tools-img/image.svg" />
              生成图片
            </div>
          </div>
          <div class="tool-btn" @click="showHistory">
            <img src="@/assets/digitalbooks/tools-img/history.svg" />
            历史
          </div>
        </div>
        <!--        <el-dropdown v-if="source === 'web'" class="type_select">-->
        <!--          <span class="el-dropdown-link">-->
        <!--            {{ type }}<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i>-->
        <!--          </span>-->
        <!--          <el-dropdown-menu slot="dropdown">-->
        <!--            <el-dropdown-item-->
        <!--              @click.native="changeType('AI对话')"-->
        <!--            >AI对话</el-dropdown-item>-->
        <!--            <el-dropdown-item-->
        <!--              @click.native="changeType('图片生成')"-->
        <!--            >AI画图</el-dropdown-item>-->
        <!--          </el-dropdown-menu>-->
        <!--        </el-dropdown>-->
        <template v-else>
          <!--          <div style="color: #606266;margin-left: 5px;font-size: 12px;height: 100%;display: flex;align-items: center" @click="showTypePicker = true">-->
          <!--            {{type}}-->
          <!--            <i class="el-icon-arrow-down el-icon&#45;&#45;right"></i>-->
          <!--          </div>-->
          <!--          <van-popup-->
          <!--            v-model="showTypePicker"-->
          <!--            position="bottom"-->
          <!--            :style="{ zIndex: 3000 }"-->
          <!--          >-->
          <!--            <van-picker-->
          <!--              show-toolbar-->
          <!--              :columns="['AI对话', 'AI画图']"-->
          <!--              @confirm="onTypeConfirm"-->
          <!--              @cancel="showTypePicker = false"-->
          <!--            />-->
          <!--          </van-popup>-->
          <div class="tools-view-h5">
            <div style='height: 100%;display: flex;align-items: center;gap: 5px'>
              <div class="tool-btn" @click="createDialogue">
                <img src="@/assets/digitalbooks/tools-img/newDialogue.svg" />
                新对话
              </div>
              <div class="tool-btn" @click="type = 'AI对话'" :class="{'tool-btn-active' : type === 'AI对话'}">
                <img src="@/assets/digitalbooks/tools-img/dialogue.svg" />
                AI对话
              </div>
              <div class="tool-btn" @click="type = '图片生成'" :class="{'tool-btn-active' : type === '图片生成'}">
                <img src="@/assets/digitalbooks/tools-img/image.svg" />
                生成图片
              </div>
            </div>
            <div class="tool-btn" @click="showHistory">
              <img src="@/assets/digitalbooks/tools-img/history.svg" />
              历史
            </div>
          </div>
        </template>
      </template>
    </JwChat-index>
    <transition name="fade" v-if="source === 'web'">
      <div v-if="historyShow" class="AIdrawer-history">
        <i class="el-icon-close close_history" @click="historyShow = false"></i>
        <div class="history_main">
          <div
            v-for="(item, index) in historyList.length / 2"
            :key="index"
          >
            <div v-if='historyList[2 * index]' class="history_item">
              <i
                class="el-icon-delete history_delete"
                style="color: #f56c6c"
                @click="deleteHistory(index)"
              ></i>
              <p>{{ historyList[2 * index].date }}</p>
              <p v-html="historyList[2 * index].text.text"></p>
              <div
                class="content"
                v-html="historyList[2 * index + 1].text.text"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </transition>
    <van-popup
      v-if="source === 'h5'"
      v-model="historyShow"
      round
      closeable
      position="bottom"
      :style="{ zIndex: 3000, height: '95%' }"
    >
      <div class="AIVant-history">
        <div class="history_title">
          历史对话
        </div>
        <div class="history_main">
          <div
            v-for="(item, index) in historyList.length / 2"
            :key="index"
          >
            <div v-if='historyList[2 * index]' class="history_item">
              <i
                class="el-icon-delete history_delete"
                style="color: #f56c6c"
                @click="deleteHistory(index)"
              ></i>
              <p>{{ historyList[2 * index].date }}</p>
              <p v-html="historyList[2 * index].text.text"></p>
              <div
                class="content"
                v-html="historyList[2 * index + 1].text.text"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </van-popup>
    <van-popup
      v-if="source === 'h5'"
      v-model="testShow"
      round
      :closeable="false"
      :close-on-click-overlay="false"
      position="bottom"
      :style="{ zIndex: 2005 , height: '95%' }"
    >
      <div class="test-main">
        <bingoBookTest :test-id-props="testId" :student-course-id-props="`${studentCourseId}`" @close="testShow = false"/>
      </div>
    </van-popup>
    <doExcelTraing ref="excelTraining" :student-course-id="studentCourseId" />
    <doAiTraing ref="doAiTraining" :student-course-id="studentCourseId" />
    <doTest ref="doTest" :test-id="testId" />
    <CaseDetail ref='detailRef' />
    <doExercise ref="doExerciseRef" />
  </div>
</template>

<script>
import doExercise from '@/views/digitalbooks/read/doExercise/doExercise'
import CaseDetail from '@/views/digitalbooks/interactiveCase/caseDetail'
import bingoBookTest from '@/views/bingoBook/test.vue'
import doTest from '@/views/digitalbooks/editor/components/doTest.vue'
import doExcelTraing from '@/views/digitalbooks/editor/components/doExcelTraing.vue'
import doAiTraing from '@/views/digitalbooks/editor/components/doAiTraing.vue'
import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'
import mk from 'markdown-it-katex'
import { getFileUploadAuthor } from '@/api/user-api'
import { getWelcomeMessage, getStaticDataByBook } from '@/api/system-api'
import axios from 'axios'
import { getTrainingPresetFile } from '@/api/course-api'
import { mapGetters } from 'vuex'
import { getTraining } from '@/api/training-api'
import { getToken } from 'utils/auth'
import { debounce } from 'utils'

export default {
  props: {
    unitId: {
      type: [String, Number],
      default: 0
    },
    AIType: {
      type: String,
      default: 'digital_book'
    },
    sourceStr: {
      type: String,
      default: 'web'
    },
    chapterName: {
      type: String,
      default: ''
    }
  },
  components: {
    doExercise,
    doExcelTraing,
    doAiTraing,
    doTest,
    bingoBookTest,
    CaseDetail
  },
  data () {
    return {
      token:'',
      testPaperList: [],
      trainingList: [],
      testId: '0',
      studentCourseId: 0,
      loading: false,
      systemMsg: '',
      source: this.$route.query.source || this.sourceStr,
      drawer: false,
      drawerHistory: false,
      historyShow: false,
      testShow: false,
      isWork: false,
      canInput: true,
      requestId: '',
      bookId: this.sourceStr === 'web' ? this.$route.query.id : this.$route.query.bookId,
      direction: 'rtl',
      copyUrl: require('@/assets/digitalbooks/read/copy.png'),
      resendUrl: require('@/assets/digitalbooks/read/resend_button.png'),
      addUrl: require('@/assets/digitalbooks/read/add_button.png'),
      aiTipUrl: require('@/assets/digitalbooks/read/ai_tip.png'),
      showTypePicker: false,
      type: 'AI对话',
      list: [],
      systemList: [],
      inputMsg: '',
      config: {
        img: this.AIType === 'lecture' ? require('@/assets/digitalbooks/tools-img/ai-title.png') : require('@/assets/digitalbooks/read/aimate.png'),
        name: '',
        dept: ''
      },
      historyList: [],
      md: new MarkdownIt({
        html: true,
        breaks: false,
        linkify: true,
        typographer: true,
        tables: true,
        highlight: function (str, lang) {
          if (!lang) lang = 'plaintext'
          if (hljs.getLanguage(lang)) {
            try {
              return '<pre class="hljs"><div class="code-header">' +
                '<span class="code-lang">' + lang + '</span>' +
                '<button class="copy-btn" onclick="copyCode(this)">复制</button>' +
                '</div><code class="' + lang + '">' +
                hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +
                '</code></pre>'
            } catch (__) {
              return '<pre class="hljs"><code>' + this.md.utils.escapeHtml(str) + '</code></pre>'
            }
          }
          return '<pre class="hljs"><code>' + this.md.utils.escapeHtml(str) + '</code></pre>'
        }
      }).use(mk, {
        throwOnError: false,
        trust: true,
        macros: {
          '\\sum': '\\sum\\limits',
          '\\f': '\\frac'
        },
        delimiters: [
          { left: '$$', right: '$$', display: true },
          { left: '\\[', right: '\\]', display: true },
          { left: '$', right: '$', display: false },
          { left: '\\(', right: '\\)', display: false }
        ]
      }),
      toolConfig: {
        // show: ['history'],
        show: [],
        showEmoji: false,
        callback: (type, plyload) => {
          this.historyShow = true
          this.historyList = JSON.parse(localStorage.getItem('chatHistory')) ? JSON.parse(localStorage.getItem('chatHistory')) : []
        }
      }
    }
  },
  // 如果章节中有相关的"图片"、"音频"、"视频"、"实训"和"习题"，可以推荐给我
  watch: {
    chapterName: {
      handler: function (val) {
        this.systemMsg = `。${val !== '' ? `当前所在章节为：${val}，` : ''}请根据用户问题回答，重点着眼于本章内容，知识库中的相关资源可以不用返回。在所有回答完后，最后推荐进一步学习的3个问题，问题格式：<div class="message-text question" style="color: rgba(47, 128, 237, 1);display: flex;align-items: center;cursor: pointer">序号.问题内容（问题内容不包含特殊符号）</div>`
      },
      immediate: true
    }
  },
  computed: {
    ...mapGetters({
      'userId': 'id'
    })
  },
  mounted () {
    document.addEventListener('compositionstart', this.setTypeFalse)
    document.addEventListener('compositionend', this.setTypeTrue)
    this.studentCourseId = this.$route.query.studentCourseId ? Number(this.$route.query.studentCourseId) : 0
    if (this.AIType === 'digital_book') {
      this.getSystemInfo()
    }
    let tokenQuery = this.$route.query && this.$route.query.token ? this.$route.query.token : ''
    if (tokenQuery && tokenQuery.startsWith('Bearer ')) {
      tokenQuery = tokenQuery.replace('Bearer ', '')
    }
    this.token = this.$route.query && this.$route.query.token ? `Bearer ${tokenQuery}` : getToken()
  },
  beforeDestroy () {
    document.removeEventListener('compositionstart', this.setTypeFalse)
    document.removeEventListener('compositionend', this.setTypeTrue)
  },
  methods: {
    async getSystemInfo() {
      try {
        this.loading = true
        const { data: staticData } = await getStaticDataByBook({ bookId: this.bookId })
        this.testPaperList = staticData.testpaperList
        this.trainingList = staticData.trainingList
        const params = {
          sourceType: 'DIGITAL_BOOK',
          sourceId: this.bookId
        }
        const { data } = await getWelcomeMessage(params)
        const obj = JSON.parse(data)
        const question = obj.questions.reduce((pre, cur) => {
          return pre + `<div class="message-text question" style="color: rgba(47, 128, 237, 1);display: flex;align-items: start;cursor: pointer"><div style='width: 10px;height: 10px;border-radius: 50%;background-color: rgba(47, 128, 237, 1);margin-right: 5px;margin-top: 5px;flex-shrink: 0'></div>${cur}</div>`
        }, '')
        this.systemList = [
          {
            date: this.getTime(),
            text: {
              text: `<div class="message-text" style="font-size: 16px;font-weight: 500">${obj.message && obj.message !== '' ? obj.message : '我是您的AI伴学助手，让我来辅助您学习'}</div>
                    ${question}`
            },
            mine: false
          }
        ]
        this.list = [...this.systemList]
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    },
    setTypeFalse () {
      this.canInput = false
    },
    setTypeTrue () {
      setTimeout(() => {
        this.canInput = true
      }, 200)
    },
    setButton () {
      // 先清理旧的按钮
      const oldButtons = document.querySelectorAll('.copy_button, .resend_button, .ai-tip-view')
      oldButtons.forEach(btn => btn.remove())
      // 添加新按钮
      const content = document.getElementsByClassName('web__main-item')
      for (let i = 0; i < content.length; i++) {
        if (!content[i].classList.contains('web__main-item--mine') && content[i].getElementsByClassName('copy_button').length === 0 && i !== 0) {
          const Image = document.createElement('img')
          Image.className = 'copy_button'
          Image.src = this.copyUrl
          Image.style = 'width:15px;height:15px; position: absolute;bottom: -20px;left: 30px;cursor: pointer;z-index: 999'
          Image.onclick = (e) => {
            const selection = window.getSelection()
            selection.removeAllRanges()
            const range = document.createRange()
            range.selectNode(e.target.parentNode.getElementsByClassName('item_msg')[0])
            selection.addRange(range)
            document.execCommand('copy')
            this.$nextTick(() => {
              selection.removeAllRanges()
            })
            this.$message.success('复制成功')
          }
          content[i].appendChild(Image)
          const Image2 = document.createElement('img')
          Image2.className = 'resend_button'
          Image2.src = this.resendUrl
          Image2.style = 'width:15px;height:15px; position: absolute;bottom: -20px;left:50px;cursor: pointer;z-index: 999'
          Image2.onclick = (e) => {
            this.reSend(e.target.parentNode.getElementsByClassName('item_msg')[0], i)
          }
          content[i].appendChild(Image2)
          const Image3 = document.createElement('img')
          Image3.className = 'ai-tip-view'
          Image3.src = this.aiTipUrl
          Image3.style = 'height:20px; position: absolute;bottom: -23px;left: 70px;z-index: 999;padding-left: 5px;padding-right: 5px'
          content[i].appendChild(Image3)
        }
      }
    },
    getBase64Image (url) {
      return new Promise((resolve, reject) => {
        const img = new Image()
        const canvas = document.createElement('canvas')
        img.crossOrigin = '*'
        img.onload = function () {
          const width = img.width; const height = img.height
          canvas.width = width
          canvas.height = height

          const ctx = canvas.getContext('2d')
          ctx.fillStyle = 'white'
          ctx.fillRect(0, 0, canvas.width, canvas.height)
          ctx.drawImage(img, 0, 0, width, height)
          const base64 = canvas.toDataURL()
          resolve(base64)
        }
        img.onerror = function (e) {
          reject(new Error(e))
        }
        img.src = url
      })
    },
    parseHtml(html) {
      const parser = new DOMParser()
      return parser.parseFromString(html, 'text/html')
    },
    async reSend (node, index) {
      this.isWork = true
      this.list[index].text = { text: '<div class="message-text" style="white-space: pre-wrap;">我正在重新思考...</div>' }
      const msg = this.parseHtml(this.list[index - 1].text.text).body.innerText
      if (node.getElementsByTagName('img').length > 0) {
        try {
          const response = await fetch(`${process.env.VUE_APP_BASE_API}/api/v2/comm/vt/aigcImg?question=${msg}`)
          if (!response.ok) throw new Error('Network response was not ok')

          const reader = response.body.getReader()
          const textDecoder = new TextDecoder()
          let result = true
          let res = ''
          while (result) {
            const { done, value } = await reader.read()
            if (done) {
              result = false
              this.isWork = false
              let img = JSON.parse(res).data.img
              const type = JSON.parse(res).data.type
              if (type !== 'imgUrl') {
                img = await this.uploadImg(img)
              } else {
                img = await this.uploadImg(await this.getBase64Image(img), false)
              }
              if (!img) {
                this.$message.error('图片生成失败，请重试')
                this.list[index].text = { text: '<div class="message-text" style="white-space: pre-wrap;">图片生成失败，请重试</div>' }
              } else {
                this.list[index].text = { text: `<div class="message-text" style="white-space: pre-wrap;"><img style="width:60%;"  src='${img}' data-src='${img}'/></div>` }
              }
              break
            }

            const chunkText = textDecoder.decode(value)
            res += chunkText
          }
        } catch (e) {
          this.$message.error('图片生成失败，请重试')
          this.list[index].text = { text: '<div class="message-text" style="white-space: pre-wrap;">图片生成失败，请重试</div>' }
          this.isWork = false
          setTimeout(() => {
            this.setButton()
          }, 1000)
        }
      } else {
        try {
          let message = msg
          if (this.AIType === 'digital_book' && this.type === 'AI对话') {
            message = `当前用户问题为"${msg}"` + '' + this.systemMsg
          }
          const url = this.AIType === 'lecture' ? `${process.env.VUE_APP_BASE_API}/api/v2/comm/vt/aiChat?question=${msg}&dataId=${this.unitId}&requestId=${this.requestId}&dataType=DIGITAL_CLOUD_LECTURE_COURSE_CONTENT` : `${process.env.VUE_APP_BASE_API}/api/v2/comm/vt/aiChat?question=${message}&dataId=${this.bookId}&requestId=${this.requestId}&dataType=DIGITAL_BOOK`
          const response = await fetch(url)
          if (!response.ok) throw new Error('Network response was not ok')

          const reader = response.body.getReader()
          const textDecoder = new TextDecoder()
          let result = true
          let res = ''
          let renderedHtml = ''
          while (result) {
            const { done, value } = await reader.read()
            if (done) {
              result = false
              if (this.AIType === 'digital_book') {
                this.list[index].text = { text: `<div class="message-text" style="white-space: pre-wrap;">${this.filterDivsByClasses(renderedHtml, ['exercise', 'training'])}</div>` }
                await this.getAIStatic(renderedHtml, msg, true, index)
              } else {
                this.setStorage()
                this.isWork = false
                this.list[index].text = { text: `<div class="message-text" style="white-space: pre-wrap;">${this.filterDivsByClasses(renderedHtml, ['exercise', 'training'])}</div>` }
              }
              break
            }
            const chunkText = textDecoder.decode(value).replace(/}{"code"/g, '}-down-{"code"')
            const arr = chunkText.split('-down-')
            arr.forEach(item => {
              if (this.isJSON(item)) {
                if (JSON.parse(item).data.result) {
                  res += JSON.parse(item).data.result
                  this.requestId = JSON.parse(item).data.id
                }
              }
            })
            renderedHtml = this.renderMarkdown(this.filterStatic(res))
            this.list[index].text = { text: `<div class="message-text" style="white-space: pre-wrap;">${renderedHtml}</div>` }
          }
        } catch (e) {
          this.$message.error('AI算力加速中，请重试')
          this.list[index].text = { text: '<div class="message-text" style="white-space: pre-wrap;">AI算力加速中，请重试</div>' }
          this.isWork = false
          setTimeout(() => {
            this.setButton()
          }, 1000)
        }
      }
      setTimeout(() => {
        this.setButton()
      }, 1000)
      if (localStorage.getItem('chatHistory')) {
        const chatHistory = JSON.parse(localStorage.getItem('chatHistory'))
        chatHistory.push(this.list[index - 1])
        chatHistory.push(this.list[index])
        localStorage.setItem('chatHistory', JSON.stringify(chatHistory))
      } else {
        const chatHistory = []
        chatHistory.push(this.list[index - 1])
        chatHistory.push(this.list[index])
        localStorage.setItem('chatHistory', JSON.stringify(chatHistory))
      }
    },
    open () {
      this.drawer = !this.drawer
    },
    onTypeConfirm(item) {
      this.type = item
      if (item === 'AI画图') {
        this.changeType('图片生成')
      } else {
        this.changeType('AI对话')
      }
      this.showTypePicker = false
    },
    changeType (val) {
      this.type = val
    },
    deleteHistory (index) {
      this.historyList.splice(2 * index, 2)
      localStorage.setItem('chatHistory', JSON.stringify(this.historyList))
    },
    setStorage () {
      if (localStorage.getItem('chatHistory')) {
        const chatHistory = JSON.parse(localStorage.getItem('chatHistory'))
        chatHistory.push(this.list[this.list.length - 2])
        chatHistory.push(this.list[this.list.length - 1])
        localStorage.setItem('chatHistory', JSON.stringify(chatHistory))
      } else {
        const chatHistory = []
        chatHistory.push(this.list[this.list.length - 2])
        chatHistory.push(this.list[this.list.length - 1])
        localStorage.setItem('chatHistory', JSON.stringify(chatHistory))
      }
    },
    getTime () {
      const now = new Date()

      const year = now.getFullYear()
      const month = ('0' + (now.getMonth() + 1)).slice(-2)
      const day = ('0' + now.getDate()).slice(-2)
      const hours = ('0' + now.getHours()).slice(-2)
      const minutes = ('0' + now.getMinutes()).slice(-2)
      const seconds = ('0' + now.getSeconds()).slice(-2)

      return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds
    },
    handleClick(e) {
      // debugger
      if (e.target.className.includes('question')) {
        if (this.isWork) {
          this.$message.warning('正在回答中，请稍候')
          return
        }
        this.isWork = true
        const textList = e.target.innerText.split('.')
        const text = textList[textList.length - 1]
        const msgObj = {
          date: this.getTime(),
          text: { text: `<div class="message-text" style="white-space: pre-wrap;">${text}</div>` },
          mine: true
        }
        const result = {
          date: this.getTime(),
          text: { text: '<div class="message-text" style="white-space: pre-wrap;">我正在思考...</div>' },
          mine: false
        }
        if (this.type === 'AI对话') {
          this.getStream(text)
        } else {
          this.getStreamImg(text)
        }
        this.list.push(msgObj)
        this.list.push(result)
      }
      if (e.target.className.includes('training')) {
        if (this.source === 'h5') {
          this.$toast('为体验完整功能，请在电脑客户端操作')
          return
        }
        const list = e.target.classList
        const trainingType = list[list.length - 1]
        if (!e.target.id || e.target.id === '') {
          this.$message.warning('未知实训，请重新提问')
          return
        }
        if (trainingType === 'COMM_PRACTICE') {
          this.$refs.doAiTraining.open(e.target.id)
        }
        if (trainingType === 'FINACE_PRACTICE') {
          this.$refs.excelTraining.open(e.target.id)
        }
        if (trainingType === 'PYTHON_PRACTICE') {
          this.getTrainingPresetFile(e.target.id)
        }
      }
      if (e.target.className.includes('case-training')) {
        this.getTrainingInfo(e.target.id)
      }
      if (e.target.className.includes('exercise')) {
        if (!e.target.id || e.target.id === '') {
          this.$message.warning('未知习题，请重新提问')
          return
        }
        this.testId = e.target.id
        if (this.source === 'h5') {
          this.testShow = true
          // this.$router.push({
          //   path: '/bingoBook/test',
          //   query: { testId: this.testId, ids: '0', studentCourseId: this.studentCourseId }
          // })
        } else {
          // this.$refs.doTest.open()
          this.$refs.doExerciseRef.open(this.testId, this.ids)
        }
      }
    },
    async getTrainingInfo(id) {
      const { data } = await getTraining({
        trainingId: id,
        studentCourseId: this.studentCourseId
      }, { authorization: this.token })
      this.$refs.detailRef.open({
        trainingName: data.trainingName,
        practiceUrl: data.practiceUrl
      })
    },
    getTrainingPresetFile: debounce(async function (trainingId) {
      const params = {
        trainingId: trainingId,
        userId: this.userId ? this.userId : null
      }
      await getTrainingPresetFile(params)
        .then(response => {
          if (response.code === 200) {
            window.open(`https://binguoketang.com/jupyterhub/hub/logout`, '_blank')
          } else {
            this.$message.error(response.message || '获取文件失败')
          }
        })
        .catch(err => {
          console.log(err)
          this.$message.error('获取文件失败')
        })
    }, 2000, true),
    // async getTrainingPresetFile (trainingId) {
    //   const params = {
    //     trainingId: trainingId,
    //     userId: this.userId ? this.userId : null
    //   }
    //   await getTrainingPresetFile(params)
    //     .then(response => {
    //       if (response.code === 200) {
    //         window.open(`https://binguoketang.com/jupyterhub/hub/logout`, '_blank')
    //       } else {
    //         this.$message.error(response.message || '获取文件失败')
    //       }
    //     })
    //     .catch(err => {
    //       console.log(err)
    //       this.$message.error('获取文件失败')
    //     })
    // },
    async bindEnter (e) {
      if (this.inputMsg.length === 0) {
        this.$message.warning('请输入内容')
        return
      }
      if (!this.canInput) {
        const data = this.inputMsg
        setTimeout(() => {
          this.inputMsg = data
        })
        return
      }
      if (this.isWork) {
        this.$message.warning('正在回答中，请稍候')
        return
      }
      this.isWork = true
      const msg = this.inputMsg
      if (!msg) return
      const msgObj = {
        date: this.getTime(),
        text: { text: `<div class="message-text" style="white-space: pre-wrap;">${msg}</div>` },
        mine: true
      }
      const result = {
        date: this.getTime(),
        text: { text: '<div class="message-text" style="white-space: pre-wrap;">我正在思考...</div>' },
        mine: false
      }
      if (this.type === 'AI对话') {
        this.getStream(msg)
      } else {
        this.getStreamImg(msg)
      }
      this.list.push(msgObj)
      this.list.push(result)
    },
    async getStream (msg) {
      try {
        msg = msg.replace(/[\n\t]/g, '')
        let message = msg
        if (this.AIType === 'digital_book') {
          message = `当前用户问题为"${msg}"` + '' + this.systemMsg
        }
        const url = this.AIType === 'lecture' ? `${process.env.VUE_APP_BASE_API}/api/v2/comm/vt/aiChat?question=${msg}&dataId=${this.unitId}&requestId=${this.requestId}&dataType=DIGITAL_CLOUD_LECTURE_COURSE_CONTENT` : `${process.env.VUE_APP_BASE_API}/api/v2/comm/vt/aiChat?question=${message}&dataId=${this.bookId}&requestId=${this.requestId}&dataType=DIGITAL_BOOK`
        const response = await fetch(url)
        if (!response.ok) throw new Error('Network response was not ok')

        const reader = response.body.getReader()
        const textDecoder = new TextDecoder()
        let result = true
        let res = ''
        let renderedHtml = ''
        while (result) {
          const { done, value } = await reader.read()
          if (done) {
            result = false
            if (this.AIType === 'digital_book') {
              this.list[this.list.length - 1].text = { text: `<div class="message-text" style="white-space: pre-wrap;">${this.filterDivsByClasses(renderedHtml, ['exercise', 'training'])}</div>` }
              await this.getAIStatic(renderedHtml, msg)
            } else {
              this.setStorage()
              this.isWork = false
              this.list[this.list.length - 1].text = { text: `<div class="message-text" style="white-space: pre-wrap;">${this.filterDivsByClasses(renderedHtml, ['exercise', 'training'])}</div>` }
              setTimeout(() => {
                this.setButton()
              }, 1000)
            }
            break
          }
          const chunkText = textDecoder.decode(value).replace(/}{"code"/g, '}-down-{"code"')
          const arr = chunkText.split('-down-')
          arr.forEach(item => {
            if (this.isJSON(item)) {
              if (JSON.parse(item).data.result) {
                res += JSON.parse(item).data.result
                this.requestId = JSON.parse(item).data.id
              }
            }
          })
          renderedHtml = this.renderMarkdown(this.filterStatic(res))

          this.list[this.list.length - 1].text = { text: `<div class="message-text" style="white-space: pre-wrap;">${renderedHtml}</div>` }
        }
      } catch (e) {
        this.$message.error('AI算力加速中，请重试')
        this.list[this.list.length - 1].text = { text: '<div class="message-text" style="white-space: pre-wrap;">AI算力加速中，请重试</div>' }
        this.isWork = false
        setTimeout(() => {
          this.setButton()
        }, 1000)
      }
    },
    async getAIStatic(originalHtml, msg, resend = false, currentIndex = 0) {
      const index = resend ? currentIndex : this.list.length - 1
      this.list[index].text = { text: `<div class="message-text" style="white-space: pre-wrap;">${originalHtml + '资源推荐中...'}</div>` }
      const htmlString = originalHtml + `<div style="margin-top: 5px;font-weight: bold">相关资源：</div>`
      const message = `推荐几个专有知识库原文中有的与“${msg}”相关的实训、交互案例、视频或者习题，如有相关资源将原文内容按照要求的格式（如："<div id='xxx' style='xxx'>【习题、实训或交互案例】标题</div>或<video src='xxx'></video>"）封装好格式后返回给我,不要直接返回知识库里的样式(如【实训，习题或交互案例】此处有一个xxxx)。如果没有相关资源则回复：没有相关资源推荐`
      const url = `${process.env.VUE_APP_BASE_API}/api/v2/comm/vt/aiChat?question=${message}&dataId=${this.bookId}&requestId=${this.requestId}&dataType=DIGITAL_BOOK`
      const response = await fetch(url)
      if (!response.ok) throw new Error('Network response was not ok')
      const reader = response.body.getReader()
      const textDecoder = new TextDecoder()
      let result = true
      let res = ''
      let renderedHtml = ''
      while (result) {
        const { done, value } = await reader.read()
        if (done) {
          this.setStorage()
          result = false
          this.isWork = false
          this.list[index].text = { text: `<div class="message-text" style="white-space: pre-wrap;">${this.filterDivsByClasses(htmlString + renderedHtml, ['exercise', 'training'])}</div>` }
          setTimeout(() => {
            this.setButton()
          }, 1000)
          break
        }
        const chunkText = textDecoder.decode(value).replace(/}{"code"/g, '}-down-{"code"')
        const arr = chunkText.split('-down-')
        arr.forEach(item => {
          if (this.isJSON(item)) {
            if (JSON.parse(item).data.result) {
              res += JSON.parse(item).data.result
              this.requestId = JSON.parse(item).data.id
            }
          }
        })
        renderedHtml = this.renderMarkdown(this.filterStatic(res))
        this.list[index].text = { text: `<div class="message-text" style="white-space: pre-wrap;">${htmlString + renderedHtml}</div>` }
      }
    },
    filterByIdAndName (id, name) {
      for (const training of this.trainingList) {
        if (`${training.trainingId}` === id) {
          return false
        }
      }
      for (const testPaper of this.testPaperList) {
        if (`${testPaper.id}` === id) {
          return false
        }
      }
      return true
    },
    filterDivsByClasses(htmlString, targetClasses) {
      // 创建临时DOM元素解析HTML
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = htmlString
      // 生成选择器，匹配包含任意目标class的div
      // 例如：targetClasses为['a', 'b']时，选择器为'div.a, div.b'
      const selector = targetClasses.map(cls => `div.${cls}`).join(', ')
      // 找到所有匹配的div元素
      const divs = tempDiv.querySelectorAll(selector)
      // 遍历每个div进行检查
      divs.forEach(div => {
        const id = div.id || ''
        const name = div.innerText
        // 检查id是否存在且仅包含数字
        // 如果id不存在或不只包含数字，则移除该div
        if (id === '' || this.filterByIdAndName(id, name)) {
          div.remove()
        }
      })
      // 返回处理后的HTML字符串
      return tempDiv.innerHTML
    },
    filterStatic(htmlString) {
      // 创建临时DOM元素解析HTML
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = htmlString
      const images = tempDiv.querySelectorAll('img')
      images.forEach(img => {
        // 检查img的data-src属性是否存在
        if (!img.src.includes('static.bingotalk.cn') || img.src === '') {
          // 如果存在，则将src设置为data-src的值
          img.remove()
        }
      })
      const videos = tempDiv.querySelectorAll('video')
      videos.forEach(video => {
        // 检查img的data-src属性是否存在
        if (!video.src.includes('static.bingotalk.cn') || video.src === '') {
          // 如果存在，则将src设置为data-src的值
          video.remove()
        }
      })
      const audios = tempDiv.querySelectorAll('audio')
      audios.forEach(audio => {
        // 检查img的data-src属性是否存在
        if (!audio.src.includes('static.bingotalk.cn') || audio.src === '') {
          // 如果存在，则将src设置为data-src的值
          audio.remove()
        }
      })
      // 返回处理后的HTML字符串
      return tempDiv.innerHTML
    },
    isJSON (str) {
      if (typeof str === 'string') {
        try {
          JSON.parse(str)
          return true
        } catch (e) {
          return false
        }
      }
      return false
    },
    base64ToImg (base64) {
      return new Promise((resolve, reject) => {
        const img = new Image()
        img.src = 'data:image/jpeg;base64,' + base64
        img.onload = function () {
          resolve(img)
        }
        img.onerror = function (e) {
          reject(e)
        }
      })
    },
    async uploadImg (base64, type = true) {
      let imgData
      if (type) { imgData = 'data:image/png;base64,' + base64 } else { imgData = base64 }
      const arr = imgData.split(',')
      const mime = arr[0].match(/:(.*?);/)[1]
      const suffix = mime.split('/')[1]
      const bstr = atob(arr[1])
      let n = bstr.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      const file = new File([u8arr], `'test'.${suffix}`, {
        type: mime
      })
      const { data } = await getFileUploadAuthor({
        mediaType: 'IMAGE',
        contentType: '',
        quantity: 1,
        fileName: file.name
      })
      const ossCDN = data[0].ossConfig.ossCDN
      try {
        const formData = new FormData()
        formData.append('success_action_status', '200')
        formData.append('callback', '')
        formData.append('key', data[0].fileName)
        formData.append('policy', data[0].policy)
        formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
        formData.append('signature', data[0].signature)
        formData.append('file', file)
        await axios.post(data[0].ossConfig.host, formData, {
        })
        return `${ossCDN}/${data[0].fileName}`
      } catch (error) {
        console.log(error)
        return false
      }
    },
    async getStreamImg (msg) {
      try {
        const response = await fetch(`${process.env.VUE_APP_BASE_API}/api/v2/comm/vt/aigcImg?question=${msg}`)
        if (!response.ok) {
          throw new Error('Network response was not ok')
        }

        const reader = response.body.getReader()
        const textDecoder = new TextDecoder()
        let result = true
        let res = ''
        while (result) {
          const { done, value } = await reader.read()
          if (done) {
            this.isWork = false
            result = false
            let img = JSON.parse(res).data.img
            const type = JSON.parse(res).data.type
            if (type !== 'imgUrl') {
              img = await this.uploadImg(img)
            } else {
              img = await this.uploadImg(await this.getBase64Image(img), false)
            }
            console.log('img+++++++++++++++++', img, type)
            if (!img) {
              this.$message.error('图片生成失败，请重试')
              this.list[this.list.length - 1].text = { text: '<div class="message-text" style="white-space: pre-wrap;">图片生成失败，请重试</div>' }
              this.setStorage()
              setTimeout(() => {
                this.setButton()
              }, 1000)
            } else {
              this.list[this.list.length - 1].text = { text: `<div class="message-text" style="white-space: pre-wrap;"><img style="width:60%;"  src='${img}' data-src='${img}'/></div>` }
              this.setStorage()
              setTimeout(() => {
                this.setButton()
              }, 1000)
            }
            break
          }
          const chunkText = textDecoder.decode(value)
          res += chunkText
        }
      } catch (e) {
        this.$message.error('图片生成失败，请重试')
        this.list[this.list.length - 1].text = { text: '<div class="message-text" style="white-space: pre-wrap;">图片生成失败，请重试</div>' }
        this.setStorage()
        this.isWork = false
      }
    },
    renderMarkdown (text) {
      text = text.replace(/&lt;/g, '<').replace(/&gt;/g, '>')
      let html = this.md.render(text)
      html = html.replace(/>\s+</g, '><')
      html = html.replace(/<\/td>\s+<td>/g, '</td><td>')
      html = html.replace(/<\/th>\s+<th>/g, '</th><th>')
      html = html.replace(/<\/tr>\s+<tr>/g, '</tr><tr>')
      return html
    },
    createDialogue() {
      this.list = [...this.systemList]
      this.inputMsg = ''
      this.requestId = ''
      this.isWork = false
      this.historyShow = false
    },
    showHistory() {
      this.historyList = JSON.parse(localStorage.getItem('chatHistory')) ? JSON.parse(localStorage.getItem('chatHistory')) : []
      this.historyList = this.historyList.reverse()
      this.historyShow = true
    }
  }
}
</script>

<style scoped lang='scss'>
.no-conversion-h5{
  ::v-deep pre {
    width: 280px !important;
  }
  ::v-deep img {
    max-width: 280px !important;
    max-height: 120px !important;
  }
  ::v-deep audio {
    max-width: 280px !important;
  }
}
.test-main{
  width: 100%;
  height: 100%;
  z-index: 2010 !important;
  background-color: white;
}
.no-conversion{
  width: 100%;
  height: 100%;
  position: relative;
  .AIVant-history{
    width: 100%;
    height: 100%;
    background: #fff;
    overflow: hidden;
    .history_title{
      font-size: 14px;
      color: #606266;
      margin-bottom: 5px;
      width: 100%;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #e6e6e6;
      padding: 10px;
    }
    .history_main {
      width: 100%;
      height: calc(100% - 60px);
      overflow: auto;
      padding: 10px;
      .history_item {
        border-bottom: 1px solid #e6e6e6;
        font-size: 12px;
        padding-bottom: 10px;
        position: relative;
        .history_delete {
          position: absolute;
          display: block;
          right: 0;
          top: 10px;
          cursor: pointer;
        }
        .content {
          ::v-deep img {
            max-width: 100%;
          }
        }
      }
    }
  }
  ::v-deep .cover {
    width: 60px !important;
    height: auto !important;
    border-radius: 0% !important;
    box-shadow: none !important;
  }
  ::v-deep pre {
    width: 360px;
  }
  ::v-deep img {
    max-width: 360px !important;
    max-height: 180px !important;
  }
  .tools-view{
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 3px 10px;
    .tool-btn {
      height: 25px;
      display: flex;
      align-items: center;
      background: linear-gradient(90deg, rgba(151, 150, 240, 0.2) 0%, rgba(251, 199, 212, 0.2) 100%);
      padding: 0 10px;
      border-radius: 30px;
      gap: 5px;
      font-size: 14px;
      color: rgba(51, 51, 51, 1);
      cursor: pointer;
      img {
        width: 15px;
        height: 15px;
      }
    }
    .tool-btn-active {
      background-color: #cbe2ff !important;
    }
  }
}

.tools-view-h5{
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 30px;
  .tool-btn {
    height: 60px;
    display: flex;
    align-items: center;
    //border: 1px solid rgba(51, 51, 51, 1);
    background: linear-gradient(90deg, rgba(151, 150, 240, 0.2) 0%, rgba(251, 199, 212, 0.2) 100%);
    padding: 0 30px;
    border-radius: 150px;
    gap: 15px;
    font-size: 36px;
    color: rgba(51, 51, 51, 1);
    cursor: pointer;
    img {
      width: 35px;
      height: 35px;
    }
  }
  .tool-btn-active {
    background-color: #cbe2ff !important;
  }
}
.type_select {
  cursor: pointer;
  font-size: 0.9rem;
  margin-left: 5px;
}
::v-deep .el-dropdown-menu__item {
  transform: scale(0.6);
  padding: 0.5rem;
}
::v-deep .el-dropdown-menu__item {
  line-height: 2rem;
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
  opacity: 0;
}
.AIdrawer-history {
  width: 80%;
  height: 80%;
  position: absolute;
  left: 10%;
  bottom: 10%;
  border-radius: 1rem;
  background: #fff;
  box-shadow: 0 0.727273vw 0.909091vw -0.454545vw rgba(0, 0, 0, 0.2),
  0 1.454545vw 2.181818vw 0.181818vw rgba(0, 0, 0, 0.14),
  0 0.545455vw 2.727273vw 0.454545vw rgba(0, 0, 0, 0.12);
  .close_history {
    width: 20px;
    height: 20px;
    position: absolute;
    display: block;
    right: 0;
    top: 10px;
    cursor: pointer;
  }
  .history_main {
    width: 90%;
    height: 90%;
    margin-top: 12%;
    margin-left: 5%;
    overflow: auto;
    @include noScrollBar;
    .history_item {
      border-bottom: 1px solid #e6e6e6;
      font-size: 8px;
      padding-bottom: 10px;
      position: relative;
      .history_delete {
        position: absolute;
        display: block;
        right: 0;
        top: 10px;
        cursor: pointer;
      }
      .content {
        ::v-deep img {
          max-width: 100%;
        }
      }
    }
  }
}

::v-deep .web__msg-menu {
  position: absolute;
  right: 20px;
  bottom: 20px;
  cursor: pointer;
}
::v-deep .web__main-item--mine {
  padding-right: 0 !important;
  .web__main-text {
    background: #2d9cdb !important;
  }
  .web__main-arrow::after {
    border-left-color: #2d9cdb !important;
  }
}
::v-deep .web__main-user {
  display: none !important;
}
::v-deep .web__main-item {
  padding-left: 20px !important;
}
::v-deep .web__main-text {
  max-width: 95% !important;
  table {
    border-collapse: collapse;
    width: 100%;
    margin: 1em 0;
    th, td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }
    th {
      background-color: #f5f5f5;
      font-weight: bold;
    }
    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
  }
}
::v-deep .wrapper {
  width: 100%;
  height: 100% !important;
  padding-bottom: 2rem;
}
::v-deep .chatPage {
  height: 100% !important;
}
::v-deep .web__main-user {
  display: none;
}
::v-deep .header {
  background: #d3e6ff !important;
}

::v-deep .el-drawer__container {
  pointer-events: none;
  .close_drawer {
    position: absolute;
    z-index: 999;
    right: 20px;
    top: 10px;
    font-size: 15px;
    cursor: pointer;
  }
}
.AIdrawerWrapper {
  pointer-events: none;
}
::v-deep .el-drawer {
  box-shadow: 0 0.727273vw 0.909091vw -0.454545vw rgba(0, 0, 0, 0.2),
  0 1.454545vw 2.181818vw 0.181818vw rgba(0, 0, 0, 0.14),
  0 0.545455vw 2.727273vw 0.454545vw rgba(0, 0, 0, 0.12);
}
::v-deep .el-drawer__body {
  pointer-events: auto;
  padding: 0px;
  background: #fff;
}

</style>
