<template>
  <el-select
    v-model="selectedLabel"
    :clearable="clearable"
    :placeholder="placeholder"
    @clear="handleClear"
    ref="selectRef"
    class="tree-select"
  >
    <el-option
      :value="selectedValue"
      :label="selectedLabel"
      class="tree-select-option"
    >
      <el-tree
        :data="treeData"
        :props="treeProps"
        :node-key="nodeKey"
        :default-expanded-keys="defaultExpandedKeys"
        :filter-node-method="filterNode"
        @node-click="handleNodeClick"
        ref="treeRef"
        :check-strictly="checkStrictly"
        :show-checkbox="multiple"
        @check="handleCheck"
        :default-checked-keys="selectedValue"
      />
    </el-option>
  </el-select>
</template>

<script>
export default {
  name: 'TreeSelect',
  props: {
    // 树形数据
    treeData: {
      type: Array,
      default: () => []
    },
    // 选中值
    value: {
      type: [String, Number, Array],
      default: () => ([])
    },
    // 节点标识属性
    nodeKey: {
      type: String,
      default: 'id'
    },
    // 树形配置项
    treeProps: {
      type: Object,
      default: () => ({
        label: 'title',
        children: 'childCatalogue'
      })
    },
    // 占位符
    placeholder: {
      type: String,
      default: '请选择'
    },
    // 是否可清空
    clearable: {
      type: Boolean,
      default: false
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 多选时是否严格遵循父子不互相关联
    checkStrictly: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      selectedValue: this.value,
      selectedLabel: '',
      defaultExpandedKeys: []
    }
  },
  watch: {
    value(val) {
      this.selectedValue = val
      this.updateSelectedLabel()
    },
    selectedValue(val) {
      this.$emit('input', val)
      this.$emit('change', val)
    }
  },
  mounted() {
    this.updateSelectedLabel()
  },
  methods: {
    // 处理节点点击（单选）
    handleNodeClick(node) {
      if (!this.multiple) {
        this.selectedValue = node[this.nodeKey]
        this.selectedLabel = node[this.treeProps.label]
        this.$refs.selectRef.blur() // 关闭下拉框
      }
    },
    // 处理复选（多选）
    handleCheck(checkedNodes) {
      if (this.multiple) {
        this.selectedValue = checkedNodes.map(node => node[this.nodeKey])
        this.selectedLabel = checkedNodes.map(node => node[this.treeProps.label]).join(', ')
      }
    },
    // 清空选择
    handleClear() {
      this.selectedValue = this.multiple ? [] : ''
      this.selectedLabel = ''
      this.$refs.treeRef.setCheckedKeys([])
    },
    // 过滤节点（可搜索功能）
    filterNode(value, data) {
      if (!value) return true
      return data[this.treeProps.label].indexOf(value) !== -1
    },
    // 更新选中的标签显示
    updateSelectedLabel() {
      if (!this.selectedValue) {
        this.selectedLabel = ''
        return
      }

      // 处理单选
      if (!this.multiple) {
        const findLabel = (nodes) => {
          // eslint-disable-next-line no-unused-vars
          for (const node of nodes) {
            if (node[this.nodeKey] === this.selectedValue) {
              return node[this.treeProps.label]
            }
            if (node[this.treeProps.children] && node[this.treeProps.children].length) {
              const result = findLabel(node[this.treeProps.children])
              if (result) return result
            }
          }
          return ''
        }
        this.selectedLabel = findLabel(this.treeData)
      } else { // 处理多选
        const labels = []
        const findLabels = (nodes) => {
          // eslint-disable-next-line no-unused-vars
          for (const node of nodes) {
            if (this.selectedValue.includes(node[this.nodeKey])) {
              labels.push(node[this.treeProps.label])
            }
            if (node[this.treeProps.children] && node[this.treeProps.children].length) {
              findLabels(node[this.treeProps.children])
            }
          }
        }
        findLabels(this.treeData)
        this.selectedLabel = labels.join(', ')
      }
    }
  }
}
</script>

<style scoped>
.tree-select {
  width: 100%;
}
.tree-select-option {
  padding: 0;
  overflow: hidden;
}
.el-tree {
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  padding: 5px 0;
}
</style>
