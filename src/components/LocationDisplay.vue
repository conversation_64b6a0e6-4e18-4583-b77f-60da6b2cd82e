<template>
  <div v-if="shouldShowLocationDisplay">
    <div class="location-display" @click="showLocationDialog">
      <svg-icon
        class="location-icon"
        icon-class="location"
        class-name="location"
      />
      <p class="location-text">{{ displayText }}</p>
      <svg-icon
        class="chevron-icon"
        icon-class="chevron-right"
        class-name="chevron-right"
      />
    </div>

    <location-select-dialog
      v-model="dialogVisible"
      @location-selected="onLocationSelected"
    />
  </div>
</template>

<script>
import LocationSelectDialog from './LocationSelectDialog.vue'

export default {
  name: 'LocationDisplay',
  components: {
    LocationSelectDialog
  },
  data() {
    return {
      dialogVisible: false,
      selectedRegionForAPI: ''
    }
  },
  computed: {
    currentChannel() {
      return window.localStorage.getItem('currentChannel')
    },
    shouldShowLocationDisplay() {
      return this.currentChannel === 'aigc'
    },
    locationName() {
      return this.$store.getters.locationName
    },
    selectedRegion() {
      return this.$store.getters.selectedRegion
    },
    displayText() {
      if (this.selectedRegion && this.selectedRegion !== '全国') {
        return this.selectedRegion
      }
      return this.locationName || '定位中...'
    }
  },
  methods: {
    showLocationDialog() {
      this.dialogVisible = true
    },

    onLocationSelected(location) {
      this.selectedRegionForAPI = location.name
      this.$store.dispatch('app/setSelectedRegion', location.name)
      this.$emit('region-selected', location.name)
    }
  }
}
</script>

<style lang="scss" scoped>
.location-display {
  margin-right: 10px;
  cursor: pointer;
  display: flex;
  flex-direction: row;
  align-items: center;
  transition: all 0.3s ease;
  padding: 4px 6px;
  border-radius: 4px;

  .location-icon {
    font-size: 16px;
    color: #1D1B1B;
    margin-right: 4px;
  }

  .location-text {
    font-size: 12px;
    font-weight: 400;
    color: #1D1B1B;
    margin: 0;
    margin-right: 2px;
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .chevron-icon {
    font-size: 12px;
    color: #1D1B1B;
  }

  &:hover {
    .location-icon,
    .location-text,
    .chevron-icon {
      color: #1F66FF;
    }
  }
}
</style>
