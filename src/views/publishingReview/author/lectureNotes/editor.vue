<template>
  <div class="editor-dig">
    <div class="dig-head">
      <div class="flex">
        <div class="pointer flex items-center" style="white-space: nowrap;" @click="goHome">
          <i class="el-icon-arrow-left"></i>
          返回
        </div>
        <div class="title">{{ titleStr }}</div>
      </div>
      <div class="flex" style='align-items: center'>
        <img class="word_button" src="@/assets/digitalbooks/read/aibutton.png" @click="showChat" />
        <div v-show="autoTips" class="dig-tips">自动保存中...</div>
        <div class="dig-btn" @click="preShowFn()">预览</div>
        <div class="dig-btn" @click="clickSave">保存</div>
        <el-dropdown trigger="click">
          <i class="el-icon-more more_btn" style='margin-left: 10px'></i>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <span @click='setTemplate("DIGITAL_CLOUD_LECTURE_COURSE_TEMPLATE_CONTENT_TEAM")'>设为团队模板</span>
            </el-dropdown-item>
            <el-dropdown-item>
              <span @click='setTemplate("DIGITAL_CLOUD_LECTURE_COURSE_TEMPLATE_CONTENT_PERSONAL")'>设为我的模板</span>
            </el-dropdown-item>
            <el-dropdown-item>
              <span @click='handleManagement'>模板管理</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <div class="dig-box">
      <div class="dig-left">
        <div class="dig-menu">
          <div class="chapter-title">
            <div class="btn_view pointer" @click="menuAppend(null, null)">创建环节</div>
            <div class="btn_view pointer" @click="contentAppend(null)">创建页面</div>
          </div>
          <div v-loading="loading" element-loading-background="rgba(255, 255, 255, 0.3)" class="menu_list">
            <el-tree
              v-if="showMenu"
              ref="treeRef"
              :data="treeList"
              :props="treeProps"
              node-key="id"
              :current-node-key="currentKey"
              draggable
              highlight-current
              default-expand-all
              :expand-on-click-node="false"
              @node-drop="handleDrop"
              @node-click="handleNodeClick"
            >
              <div slot-scope="{ node, data }" class="menu_item">
                <template v-if="data.type === 'DIGITAL_CLOUD_LECTURE_COURSE_CATALOGUE'">
                  <div class="item_title">
                    <div :title="data.title" class="w article-singer-container">
                      {{ data.title }}
                    </div>
                  </div>
                  <div class="item_option">
                    <el-tooltip class="item" effect="dark" content="新增" placement="top-start">
                      <el-button
                        type="text"
                        size="mini"
                        @click.stop="() => menuAppend(node, data)"
                      >
                        <svg-icon
                          class="svgIcon"
                          icon-class="add"
                          class-name="add"
                        />
                      </el-button>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="编辑" placement="top-start">
                      <el-button
                        type="text"
                        size="mini"
                        @click.stop="() => menuEdit(node, data)"
                      >
                        <svg-icon
                          class="svgIcon"
                          icon-class="edit"
                          class-name="edit"
                        />
                      </el-button>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="删除" placement="top-start">
                      <el-button
                        type="text"
                        size="mini"
                        @click.stop="() => menuRemove(node, data)"
                      >
                        <svg-icon
                          class="svgIcon"
                          icon-class="delete"
                          class-name="delete"
                        />
                      </el-button>
                    </el-tooltip>
                  </div>
                </template>
                <template v-else>
                  <div class="menu_content">
                    <div class="content_view">
                      <div v-if="data.contents && data.contents.length > 0" class="html_view" v-html="data.contents[0].data"></div>
                    </div>
                    <div class="option_view">
                      <el-tooltip class="item" effect="dark" content="复制" placement="top-start">
                        <el-button
                          type="text"
                          size="mini"
                          @click.stop="() => handleCopy(node, data)"
                        >
                          <i class="el-icon-document-copy" style='font-size: 16px'></i>
                        </el-button>
                      </el-tooltip>
                      <el-tooltip class="item" effect="dark" content="删除" placement="top-start">
                        <el-button
                          type="text"
                          size="mini"
                          style='margin-left: 0'
                          @click.stop="() => menuRemove(node, data)"
                        >
                          <i class="el-icon-delete" style='font-size: 16px'></i>
                        </el-button>
                      </el-tooltip>
                    </div>
                  </div>
                </template>
              </div>
            </el-tree>
          </div>
        </div>
      </div>
      <div v-loading="loading" element-loading-background="rgba(255, 255, 255, 0.3)" class="dig-center">
<!--        <div style='width: 100%;padding: 20px;box-sizing: border-box'>-->
<!--          <div style='width: 120px;height: 40px;font-size: 16px;color: #4997FD;font-weight: 500; padding: 10px 20px;position: relative;margin-left: 5px;box-sizing: border-box'>-->
<!--            <div style="position:absolute; top:0; left:0; right:0; bottom:0; border:1px solid #57A0FF; transform:skew(-10deg); z-index: 10;box-sizing: border-box;box-shadow: 3px 3px 1px #57A0FF"></div>-->
<!--            <div style='width: 120px;height: 40px;position: absolute;top: 0;left: 0;line-height: 40px;text-align: center;z-index: 11'>知识拓展</div>-->
<!--          </div>-->
<!--          <div style='width: 100%;padding: 20px 30px;border: 1px solid #ACD0FF;line-height: 25px;font-size: 14px;color: #4F4F4F;margin-top: 10px;white-space: pre-wrap;box-sizing: border-box' v-html="'课程名称：厚植家国情怀·培育时代新人\n课程类型：数字云讲堂\n课程目标：通过本课程的学习，帮助学员树立正确的价值观、人生观和世界观，培养家国情怀，增强社会责任感和使命感。\n课程内容：\n1. 家国情怀的内涵与重要性\n2. 培育时代新人的方法与途径\n3. 实践案例分析与讨论\n4. 结业考核与反馈'">-->
<!--          </div>-->
<!--        </div>-->
<!--        <p></p>-->
<!--        <p></p>-->
<!--        <p></p>-->
        <div v-show="otherUse" class="other-use">
          <div style="background-color: white;min-width: 1vw;position: absolute;left: 35%;top: 50%">其他作者正在编写中，无法进入编写</div>
        </div>
        <div class="eidtor">
          <tinymce-editor
            ref="tinyMceRef"
            :id="tinymceId"
            v-model="html"
            :init="init"
            @onFocus="onFocus"
            @input="onChange"
          />
        </div>
      </div>
      <div class="right_content">
        <div
          class="right_input"
          :class="{'disabled_bg':!preNode || preNode.type !== 'DIGITAL_CLOUD_LECTURE_COURSE_CONTENT'}"
          @click="()=>{
            if (preNode && preNode.type === 'DIGITAL_CLOUD_LECTURE_COURSE_CONTENT') {
              this.$refs.remarkInput.focus()
            }
          }"
        >
          <el-input
            ref="remarkInput"
            v-model="currentRemark"
            placeholder="本页讲义备注(非必填)"
            type="textarea"
            autosize
            :disabled="!preNode || preNode.type !== 'DIGITAL_CLOUD_LECTURE_COURSE_CONTENT'"
            @blur="setRemark"
          />
        </div>
        <div class='right_history'>
          <span v-if="this.mode === 'default'" style='cursor: pointer;text-decoration: underline' @click='handleHistory'>历史记录</span>
        </div>
      </div>
    </div>
    <HistoryDrawer
                   v-if='preNode'
                   ref='historyRef'
                   :catalogue-id='preNode.id'
                   :token='token'
                   @setHistoryContent='setHistoryContent'
                   @showHistoryDetail='showHistoryDetail'/>
    <templateDialog ref="templateDialog" :book-id="bookId" @handleUse="useTemplate" @handleReview='previewTemplate' />
    <templateManagement ref='templateManagementRef' :book-id="bookId"  @handleReview='previewTemplateManagement'/>
    <editChapter
      v-if="editChapterShow"
      :show="editChapterShow"
      :title-obj="{
        addTitle: '新建环节',
        editTitle: '编辑环节'
      }"
      type="DIGITAL_CLOUD_LECTURE_COURSE_CATALOGUE"
      :append-to-body="true"
      :node-info="currentNode"
      @close="editChapterShow = false"
      @emitSucess="editDone"
    />
    <tipsPop ref="tips" :position="tipsPosition" :info="tipsInfo" />
    <imgGroupPop ref="imgs" :info="imgListInfo" />
    <videoCardPop ref="videoCard" :info="videoInfo" />
    <AudioPop ref="audioRef" />
    <chatDrawer ref="chatDrawer" />
    <showHistory ref="history" :content="historyContent" />
    <doExercise ref="doExerciseRef" />
    <doTest ref="doTest" :test-id="testId" :ids="ids" />
    <officeView ref="officeView" :url="officeUrl" :ctoken="token" />
    <doExcelTraing ref="excelRraing" />
    <doAiTraing ref="aiTraining" />
    <CaseDetail ref='detailRef' />
    <NormalDialog
      v-if="historyDetailShow"
      width='770px'
      title="历史记录详情"
      :dialog-visible="historyDetailShow"
      :is-center="true"
      :append-to-body="true"
      :dig-class="true"
      @closeDialog='hiddenHistoryDetail'
    >
      <div class="history_detail" v-html="historyHtml"></div>
    </NormalDialog>
  </div>
</template>

<script>
import doExercise from '@/views/digitalbooks/read/doExercise/doExercise'
import AudioPop from '@/views/digitalbooks/editor/components/audioPop'
import CaseDetail from '@/views/digitalbooks/interactiveCase/caseDetail'
import templateManagement from '@/views/publishingReview/author/components/templateManagement'
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import templateDialog from '@/views/publishingReview/author/components/templateDialog'
import tinymce from 'tinymce/tinymce'
import TinymceEditor from '@tinymce/tinymce-vue'
import 'tinymce/themes/silver/theme'
import chatDrawer from '@/views/digitalbooks/editor/components/aiChat.vue'
import defaultConfig from '@/views/digitalbooks/editor/utils/config'
import editChapter from '@/views/digitalbooks/editor/components/editChapter.vue'
import HistoryDrawer from '@/views/publishingReview/author/components/historyDrawer'
import { getSqlPlatformToken } from '@/api/training-api'
import {
  deleteBookCatalogue,
  saveContent,
  dragCatalogue,
  takeBook,
  getDigitalContentChangeLog,
  editBookCatalogue
} from '@/api/digital-api.js'
import { Notification } from 'element-ui'
import { throttle, debounce, getFileType } from '@/utils/index'
import { saveAs } from 'file-saver'
import { uploadVideo } from '@/views/digitalbooks/editor/utils/video'
import { test } from '@/views/digitalbooks/editor/utils/test'
import { uploadAudio } from '@/views/digitalbooks/editor/utils/audio'
import { tips } from '@/views/digitalbooks/editor/utils/tips'
import { imgGroup } from '@/views/digitalbooks/editor/utils/imgGroup'
import { uploadVideoCard } from '@/views/digitalbooks/editor/utils/videoCard'
import { fileDownLoad } from '@/views/digitalbooks/editor/utils/fileDownload'
import { indent2em } from '@/views/digitalbooks/editor/utils/indent2em'
import { lineHeight } from '@/views/digitalbooks/editor/utils/lineHeight'
import tipsPop from '@/views/digitalbooks/editor/components/tipsPop.vue'
import { uploadImg } from '@/views/digitalbooks/editor/utils/uploadImg'
import { formateImg } from '@/views/digitalbooks/editor/utils/formateImg'
import { addIframe } from '@/views/digitalbooks/editor/utils/iframe'
import { excelTrain } from '@/views/digitalbooks/editor/utils/excelTrain'
import imgGroupPop from '@/views/digitalbooks/editor/components/imgGroupPop.vue'
import videoCardPop from '@/views/digitalbooks/editor/components/videoPop.vue'
import { mapGetters } from 'vuex'
import showHistory from '@/views/digitalbooks/editor/components/showHistory.vue'
import doTest from '@/views/digitalbooks/editor/components/doTest.vue'
import { trainingdPlugin } from '@/views/digitalbooks/editor/utils/addTraining'
import officeView from '@/views/digitalbooks/editor/components/officeView.vue'
import doExcelTraing from '@/views/digitalbooks/editor/components/doExcelTraing.vue'
import doAiTraing from '@/views/digitalbooks/editor/components/doAiTraing.vue'
import { aiTraining } from '@/views/digitalbooks/editor/utils/addAiTraining'
import { pythonTraining } from '@/views/digitalbooks/editor/utils/addPythonTraining'
tinymce.PluginManager.add('uploadVideo', uploadVideo)
tinymce.PluginManager.add('uploadAudio', uploadAudio)
tinymce.PluginManager.add('formateImg', formateImg)
tinymce.PluginManager.add('imgGroup', imgGroup)
tinymce.PluginManager.add('lineHeight', lineHeight)
tinymce.PluginManager.add('uploadImg', uploadImg)
tinymce.PluginManager.add('indent2em', indent2em)
tinymce.PluginManager.add('tips', tips)
tinymce.PluginManager.add('uploadVideoCard', uploadVideoCard)
tinymce.PluginManager.add('fileDownLoad', fileDownLoad)
tinymce.PluginManager.add('test', test)
tinymce.PluginManager.add('customIframe', addIframe)
tinymce.PluginManager.add('trainingdPlugin', trainingdPlugin)
tinymce.PluginManager.add('excelTrain', excelTrain)
tinymce.PluginManager.add('aiTraining', aiTraining)
tinymce.PluginManager.add('pythonTraining', pythonTraining)
tinymce.PluginManager.add('addCase', addCase)
import VueViewer from 'v-viewer'
import Vue from 'vue'
import { getCloudLectureDetail, usedCloudLectureTemplate, addCloudLectureTemplate } from '@/api/cloudLecture-api'
import { getTrainingPresetFile } from '@/api/course-api'
import { addCase } from '@/views/digitalbooks/editor/utils/addCase'
Vue.use(VueViewer)
VueViewer.setDefaults({
  title: (image) => image.alt || ''
})
export default {
  components: { doExercise, AudioPop, CaseDetail, TinymceEditor, editChapter, tipsPop, imgGroupPop, videoCardPop, chatDrawer, showHistory, doTest, officeView, doExcelTraing, doAiTraing, templateDialog, HistoryDrawer, NormalDialog, templateManagement },
  data () {
    return {
      tinymceId: 'lectureNotesEditor',
      ids: '',
      testId: '0',
      customStyle: {
        borderBottom: '1px solid #ccc',
        width: '100%',
        height: '580px',
        padding: '0 8px',
        margin: '0',
        boxSizing: 'border-box',
        boxShadow: '0px 4px 4px 0px rgba(0, 0, 0, 0.25);',
        backgroundColor: '#fff'
      },
      init: Object.assign(defaultConfig, {
        plugins: defaultConfig.plugins.replaceAll('autoresize', ''),
        setup: (editor) => {
          if (editor.id === 'lectureNotesEditor') {
            editor.on('init', (e) => {
              const contentDocument = editor.getDoc()
              const editorBody = editor.getBody()
              editorBody.style.borderBottom = '1px solid #efefef'
              editorBody.style.width = '100%'
              editorBody.style.height = '580px'
              editorBody.style.padding = '15px'
              editorBody.style.margin = '0'
              editorBody.style.boxSizing = 'border-box'
              editorBody.style.boxShadow = '0px 4px 4px 0px rgba(0, 0, 0, 0.25)'
              editorBody.style.backgroundColor = 'white'
              const iframeDocument = document.getElementById('lectureNotesEditor_ifr').contentDocument
              iframeDocument.documentElement.style.backgroundColor = '#efefef'

              editorBody.style.msOverflowStyle = 'none'// IE 10+
              editorBody.style.scrollbarWidth = 'none' // Firefox
              // WebKit (Chrome, Safari) 浏览器隐藏滚动条
              const style = contentDocument.createElement('style')
              style.textContent = `
              ::-webkit-scrollbar {
                display: none;
              }
            `
              contentDocument.head.appendChild(style)
              // 添加自定义CSS
              editor.contentDocument.head.insertAdjacentHTML('beforeend', `
                <style>
                  .draggable-div {
                    /*min-width: 100px;*/
                    /*min-height: 60px;*/
                    transition: box-shadow 0.2s;
                    border: 0
                  }
                  .draggable-div-selected {
                    border: 2px solid #3498db;
                    box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
                  }
                  .resize-handle {
                    width: 12px;
                    height: 12px;
                    background-color: #3498db;
                    border-radius: 3px;
                    position: absolute;
                    bottom: 0;
                    right: 0;
                    z-index: 20;
                    cursor: se-resize;
                    display: none
                  }
                  /*.move-handle {*/
                  /*  width: 100%;*/
                  /*  height: 100%;*/
                  /*  border-radius: 3px;*/
                  /*  position: absolute;*/
                  /*  top: 0;*/
                  /*  left: 0;*/
                  /*  z-index: 10;*/
                  /*  cursor: move;*/
                  /*  display: none*/
                  /*}*/
                  .move-handle {
                    width: 12px;
                    height: 12px;
                    background-color: #3498db;
                    border-radius: 3px;
                    position: absolute;
                    top: 0;
                    left: 0;
                    z-index: 20;
                    cursor: move;
                    display: none
                  }
                </style>
              `)
            })
          }
        }
      }),
      activeName: 'first',
      isPublish: false,
      message: '',
      tipsPosition: {
        top: 0,
        left: 0
      },
      videoInfo: {
        src: '',
        poster: '',
        text: ''
      },
      tipsInfo: {
        keyword: '',
        content: ''
      },
      preShow: false,
      editor: null,
      chapterShow: true,
      loading: false,
      html: '',
      htmlId: 0,
      newHtml: '',
      mode: 'default', // or 'template'
      id: 1000,
      bookId: 0,
      catalogueId: '',
      treeData: null,
      titleStr: '',
      showMenu: true,
      treeList: [],
      treeProps: {
        children: 'childCatalogue',
        label: 'title'
      },
      currentNode: null,
      editChapterShow: false,
      selectTreeId: 0,
      preNode: null,
      token: '',
      saveTimer: null,
      autoTips: false,
      otherUse: false,
      keepHeartTime: null,
      contentChanged: false,
      imgListInfo: null,
      bugList: [],
      CatalogueAuthorList: [],
      reviewData: null,
      detailInfo: null,
      historyContent: '',
      uuid: this.$route.query.uuid,
      openFlag: false,
      officeUrl: '',
      isLoading: false,
      currentKey: 0,
      currentRemark: '',
      historyDetailShow: false,
      historyHtml: '',
      resizeFn: null,
      scale: 1
    }
  },
  computed: {
    ...mapGetters({
      'userId': 'id'
    })
  },
  beforeUpdate() {
    if (this.$route.query.uuid) {
      this.uuid = this.$route.query.uuid
    } else {
      if (this.$route.query && this.$route.query.mode && this.$route.query.mode === 'template') {
        this.mode = 'template'
      }
      this.$router.push(
        {
          path: '/author/lectureNotesEditor',
          query: {
            mode: this.mode,
            token: this.$route.query.token,
            uuid: this.getUUid(),
            bookId: this.$route.query.bookId,
            catalogueId: this.$route.query.catalogueId
          }
        })
    }
  },
  async mounted () {
    this.bookId = this.$route.query && Number(this.$route.query.bookId)
    if (!this.bookId) {
      this.bookId = 0
    }
    this.catalogueId = this.$route.query && this.$route.query.catalogueId
    this.token = this.$route.query && this.$route.query.token

    if (this.$route.query && this.$route.query.mode && this.$route.query.mode === 'template') {
      this.mode = 'template'
      this.token = 'Bearer ' + this.$route.query.token
    }
    await this._getBookCatalogue(true)
    window.addEventListener('beforeunload', e => this.beforeunloadFn(e))
    tinymce.init({}).then(() => {
      setTimeout(() => {
        this.initFun()
      }, 1000)
    })
    this.changeMenuWidth()
    this.resizeFn = debounce(this.changeMenuWidth, 1000)
    window.addEventListener('resize', this.resizeFn)

    if (localStorage.getItem('showManagement') && localStorage.getItem('showManagement') === 'true') {
      this.handleManagement()
      localStorage.removeItem('showManagement')
    }
  },
  beforeDestroy () {
    this.saveHtml()
    if (this.keepHeartTime) {
      clearInterval(this.keepHeartTime)
    }
    if (this.saveTimer) {
      clearInterval(this.saveTimer)
    }
    this.keepHeartTime = null
    this.saveTimer = null
    window.removeEventListener('resize', this.resizeFn)
  },
  methods: {
    // 模板管理
    handleManagement() {
      this.$refs.templateManagementRef.show()
    },
    // 设为模板
    async setTemplate(type) {
      try {
        await addCloudLectureTemplate({
          sourceCatalogueId: Number(this.catalogueId),
          type: type
        }, {
          authorization: this.token
        })
        this.$message.success('设置成功')
      } catch (e) {
        console.log(e)
      }
    },
    changeMenuWidth() {
      const domWidth = Number(document.querySelector('.dig-left').clientWidth)
      const domHeight = Number(document.querySelector('.dig-left').clientHeight)
      const menuDom = document.querySelector('.dig-menu')
      const scaleX = domWidth / menuDom.clientWidth
      this.scale = Math.min(scaleX, 1)
      menuDom.style.height = `${(domHeight - 10) / this.scale}px`
      menuDom.style.transform = `scale(${this.scale})`
    },
    parseHtml(html) {
      const parser = new DOMParser()
      return parser.parseFromString(html, 'text/html')
    },
    testElement(html) {
      const newDoc = this.parseHtml(html)
      const newElements = Array.from(newDoc.querySelectorAll('.test_card'))
        .map(el => el.getAttribute('data-id'))
        .filter(Boolean)
      console.log(newElements)
      return newElements.length === 0 ? '' : newElements.join(',')
    },
    trainingElement(html) {
      const newDoc = this.parseHtml(html)
      const newElements = Array.from(newDoc.querySelectorAll('.excel_card')).concat(Array.from(newDoc.querySelectorAll('.ai_card'))).concat(Array.from(newDoc.querySelectorAll('.python_card')))
        .map(el => JSON.parse(el.getElementsByClassName('info')[0].innerHTML).id)
        .filter(Boolean)
      console.log(newElements)
      return newElements.length === 0 ? '' : newElements.join(',')
    },
    caseElement(html) {
      const newDoc = this.parseHtml(html)
      const newElements = Array.from(newDoc.querySelectorAll('.case_card'))
        .map(el => JSON.parse(el.getElementsByClassName('info')[0].innerHTML).id)
        .filter(Boolean)
      console.log(newElements)
      return newElements.length === 0 ? '' : newElements.join(',')
    },
    openExcelTraining(id) {
      this.$refs.excelRraing.open(id)
    },
    openAiTraining(id) {
      this.$refs.aiTraining.open(id)
    },
    goHome() {
      if (this.mode === 'default') {
        this.$router.push({
          path: '/author/lectureNotes',
          query: { bookId: this.bookId, token: this.token, path: '/author/home', uuid: this.uuid }
        })
      } else {
        window.open('', '_self').close()
      }
    },
    getUUid() {
      let dt = new Date().getTime()
      const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = (dt + Math.random() * 16) % 16 | 0
        dt = Math.floor(dt / 16)
        return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16)
      })
      return uuid
    },
    showChat () {
      this.$refs.chatDrawer.open()
    },
    checkBase64 (val) {
      try {
        const content = val
        const imgReg = /<img.*?(?:>|\/>)/gi // 匹配图片中的img标签
        const srcReg = /src=[\'\"]?([^\'\"]*)[\'\"]?/i // 匹配图片中的src
        const arr = content.match(imgReg) // 筛选出所有的img
        if (arr != null) {
          for (let i = 0; i < arr.length; i++) {
            const src = arr[i].match(srcReg)
            // 获取图片地址判断是否是base64格式
            if (src[1].indexOf('base64') !== -1) {
              return true
            }
          }
        }
        return false
      } catch {
        // this.$message.warning('请检查图片格式是否正确')
        return false
      }
    },
    closePop () {
      if (this.$refs.tips) { this.$refs.tips.close() }
    },
    initFun () {
      if (!document.getElementsByClassName('tox-edit-area__iframe').length) {
        setTimeout(() => {
          this.initFun()
        }, 500)
        return
      }
      document.getElementsByClassName('tox-edit-area__iframe')[0].contentWindow.document.addEventListener('click', this.richEvent, false)
      document.getElementsByClassName('tox-edit-area__iframe')[0].contentWindow.document.addEventListener('dblclick', this.showImageList, false)
      document.addEventListener('click', this.closePop)
    },
    showImageList (e) {
      if (!e.target || !e.target.classList) {
        return
      }
      if (e.target.classList.contains('img_card_button')) {
        this.imgListInfo = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText)
        this.$refs.imgs.open()
      }
      if (e.target.classList.contains('video_button')) {
        this.videoInfo = {
          src: e.target.src,
          poster: e.target.poster,
          text: e.target.parentNode.getElementsByTagName('p')[1].innerText
        }
        this.$refs.videoCard.open()
      }
    },
    richEvent (e) {
      if (this.$refs.tips) { this.$refs.tips.close() }
      if (!e.target || !e.target.classList) {
        return
      }
      if (e.target.classList.contains('tips_item')) {
        const item = e.target
        const view = document.getElementsByClassName('tox-edit-area__iframe')[0]
        let y = item.getBoundingClientRect().top + view.getBoundingClientRect().top + 20
        if (window.innerHeight - e.pageY < 195) {
          y = window.innerHeight - 200
        }
        this.tipsPosition = {
          top: y,
          left: item.getBoundingClientRect().left + view.getBoundingClientRect().left
        }
        this.tipsInfo = {
          keyword: item.innerText,
          content: item.children[0].innerText
        }
        this.$refs.tips.show()
      }

      if (e.target.parentNode && e.target.parentNode.parentNode && e.target.parentNode.parentNode.classList && e.target.parentNode.parentNode.classList.contains('file_download')) {
        if (e.target.classList.contains('download_button')) {
          const url = e.target.parentNode.children[1].innerText
          const fileName = e.target.parentNode.children[2].innerText
          const notif = Notification({
            title: fileName,
            dangerouslyUseHTMLString: true,
            message: '',
            duration: 0
          })
          throttle(function () {
            const xhr = new XMLHttpRequest()
            xhr.open('get', url)
            xhr.responseType = 'blob'
            xhr.addEventListener('progress', (e) => {
              const complete = Math.floor(e.loaded / e.total * 100)
              notif.message = complete + '%'
              if (complete >= 100) {
                notif.close()
              }
            })
            xhr.send()
            xhr.onload = function () {
              if (this.status === 200 || this.status === 304) {
                const blob = new Blob([this.response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8' })
                saveAs(blob, fileName)
              }
            }
          }, 2000)
        } else if (e.target.classList.contains('show_button')) {
          const item = e.target
          this.downloadFun(item)
        } else {
          const item = e.target.parentNode.parentNode.children[3].children[0]
          this.downloadFun(item)
        }
      }
      if (e.target.classList.contains('do_test')) {
        this.testId = e.target.parentNode.getAttribute('data-id')
        this.ids = e.target.parentNode.getAttribute('data-ids')
        // this.$refs.doTest.open()
        this.$refs.doExerciseRef.open(this.testId, this.ids)
      }
      if (e.target.classList.contains('to_training')) {
        const type = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText).type
        if (type === 'sql') {
          if (this.openFlag) {
            return
          }
          this.openFlag = true
          this.$message.success({ duration: 3000, message: '正在跳转请稍后...' })
          getSqlPlatformToken({}, { authorization: this.token }).then(res => {
            this.openFlag = false
            window.open(res.data)
          })
        }
      }
      if (e.target.classList.contains('to_excel')) {
        const id = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText).id
        this.openExcelTraining(id)
      }
      if (e.target.classList.contains('to_aiTrain')) {
        const id = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText).id
        this.openAiTraining(id)
      }
      if (e.target.classList.contains('to_pythonTrain')) {
        const id = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText).id
        this.getTrainingPresetFile(id)
      }
      if (e.target.classList.contains('case-btn')) {
        const obj = JSON.parse(e.target.parentNode.parentNode.getElementsByClassName('info')[0].innerText)
        this.$refs.detailRef.open({
          title: obj.title,
          practiceUrl: obj.data.practiceUrl,
          id: obj.id
        })
      }
    },
    getTrainingPresetFile: debounce(async function (trainingId) {
      const params = {
        trainingId: trainingId,
        userId: this.userId ? this.userId : null
      }
      await getTrainingPresetFile(params)
        .then(response => {
          if (response.code === 200) {
            window.open(`https://binguoketang.com/jupyterhub/hub/logout`, '_blank')
          } else {
            this.$message.error(response.message || '获取文件失败')
          }
        })
        .catch(err => {
          console.log(err)
          this.$message.error('获取文件失败')
        })
    }, 2000, true),
    // async getTrainingPresetFile (trainingId) {
    //   const params = {
    //     trainingId: trainingId,
    //     userId: this.userId ? this.userId : null
    //   }
    //   await getTrainingPresetFile(params)
    //     .then(response => {
    //       if (response.code === 200) {
    //         window.open(`https://binguoketang.com/jupyterhub/hub/logout`, '_blank')
    //       } else {
    //         this.$message.error(response.message || '获取文件失败')
    //       }
    //     })
    //     .catch(err => {
    //       console.log(err)
    //       this.$message.error('获取文件失败')
    //     })
    // },
    isFileSizeGreaterThan200MB(sizeStr) {
      const sizeUnit = sizeStr.slice(-2).toUpperCase() // 获取单位
      const sizeValue = parseFloat(sizeStr) // 获取数值

      // 将大小转换为 MB
      let sizeInMB

      switch (sizeUnit) {
        case 'GB':
          sizeInMB = sizeValue * 1024
          break
        case 'MB':
          sizeInMB = sizeValue
          break
        case 'KB':
          sizeInMB = sizeValue / 1024
          break
        case 'B':
          sizeInMB = sizeValue / (1024 * 1024)
          break
        default:
          throw new Error('Unsupported size unit')
      }

      return sizeInMB > 200 // 判断是否大于 200MB
    },
    downloadFun(item) {
      const url = item.parentNode.children[1].innerText
      const fileName = item.parentNode.children[2].innerText
      const size = item.parentNode.parentNode.children[2].children[1].innerText
      if (this.getFileType(fileName) === 'video') {
        this.videoInfo = {
          src: url,
          poster: '',
          text: ''
        }
        this.$refs.videoCard.open()
      } else if (this.getFileType(fileName) === 'Office') {
        if (this.isFileSizeGreaterThan200MB(size)) {
          this.$message.warning('暂不支持大于200M的附件预览')
          return
        }
        this.officeUrl = url
        this.$nextTick(() => {
          this.$refs.officeView.open()
        })
      } else if (this.getFileType(fileName) === 'img') {
        this.imgListInfo = { content: [{ src: url, info: '' }] }
        this.$refs.imgs.open()
      } else if (getFileType(fileName) === '音频') {
        this.$refs.audioRef.open(url)
      } else {
        this.$message.warning('该类型文件暂不支持预览')
      }
    },
    getFileType(fileName) {
      // 获取文件后缀名
      const extension = fileName.split('.').pop().toLowerCase()
      // 定义不同类型的文件后缀
      const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp']
      const videoExtensions = ['mp4', 'avi', 'mov', 'mkv', 'flv', 'wmv']
      const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a']
      const officeExtensions = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf']
      const archiveExtensions = ['zip', 'rar', '7z', 'tar', 'gz']

      // 判断文件类型
      if (imageExtensions.includes(extension)) {
        return 'img'
      } else if (videoExtensions.includes(extension)) {
        return 'video'
      } else if (audioExtensions.includes(extension)) {
        return '音频'
      } else if (officeExtensions.includes(extension)) {
        return 'Office'
      } else if (archiveExtensions.includes(extension)) {
        return '压缩文件'
      } else {
        return '其他类型'
      }
    },
    async _takeBook () {
      // 判断是否有人正在使用该书
      const { data } = await takeBook({
        bookId: this.bookId,
        scene: 'BOOK_CATALOGUE_OWN',
        catalogueId: this.selectTreeId,
        webTabUuid: this.uuid
      }, {
        authorization: this.token
      })
      if (!data) {
        this.otherUse = true
        if (this.editor && this.editor.blur) {
          this.editor.blur()
        }
      } else {
        this.otherUse = false
      }
    },
    async _getBookCatalogue (first = false) {
      const { data } = await getCloudLectureDetail({
        catalogueId: this.catalogueId
      })
      this.detailInfo = data
      this.titleStr = this.detailInfo.title ?? ''
      this.treeList = this.detailInfo.childCatalogue ?? []
      if (data && data.childCatalogue.length === 0) {
        if (this.mode === 'default') {
          this.$refs.templateDialog.show()
        }
      } else {
        if (first) {
          if (data.childCatalogue && data.childCatalogue.length > 0) {
            if (localStorage.getItem('contentId')) {
              data.childCatalogue.forEach((item) => {
                if (item.childCatalogue && item.childCatalogue.length > 0) {
                  item.childCatalogue.forEach((subItem) => {
                    if (subItem.id === Number(localStorage.getItem('contentId'))) {
                      this.preNode = subItem
                      this.selectTreeId = subItem.id
                      this.currentKey = subItem.id
                    }
                  })
                }
                if (item.id === Number(localStorage.getItem('contentId'))) {
                  this.preNode = item
                  this.selectTreeId = item.id
                  this.currentKey = item.id
                }
              })
              localStorage.removeItem('contentId')
            } else {
              this.preNode = data.childCatalogue[0]
              this.selectTreeId = data.childCatalogue[0].id
              this.currentKey = data.childCatalogue[0].id
            }
            if (this.preNode && this.preNode.type === 'DIGITAL_CLOUD_LECTURE_COURSE_CONTENT') {
              this.currentRemark = this.preNode.subtitle
              tinymce.activeEditor.setMode('design')
              if (this.preNode.contents && this.preNode.contents.length > 0) {
                this.html = this.preNode.contents[0].data
                this.htmlId = this.preNode.contents[0].id
              } else {
                this.html = ''
                this.htmlId = 0
              }
            } else {
              this.html = ''
              this.htmlId = 0
              tinymce.activeEditor.setMode('readonly')
            }
          }
        } else {
          if (this.currentKey !== 0) {
            data.childCatalogue.forEach((item) => {
              if (item.childCatalogue && item.childCatalogue.length > 0) {
                item.childCatalogue.forEach((subItem) => {
                  if (subItem.id === this.currentKey) {
                    this.preNode = subItem
                    this.selectTreeId = subItem.id
                    this.currentKey = subItem.id
                  }
                })
              }
              if (item.id === this.currentKey) {
                this.preNode = item
                this.selectTreeId = item.id
                this.currentKey = item.id
              }
            })
          } else {
            this.preNode = data.childCatalogue[0]
            this.selectTreeId = data.childCatalogue[0].id
            this.currentKey = data.childCatalogue[0].id
          }
          if (this.preNode && this.preNode.type === 'DIGITAL_CLOUD_LECTURE_COURSE_CONTENT') {
            this.currentRemark = this.preNode.subtitle
            tinymce.activeEditor.setMode('design')
          }
        }
        this.showMenu = false
        this.$nextTick(() => {
          this.showMenu = true
          setTimeout(() => {
            const tree = this.$refs.treeRef
            if (tree) {
              const nodeEl = tree.$el.querySelector(`.is-current`)
              if (nodeEl) {
                nodeEl.scrollIntoView({ behavior: 'smooth', block: 'nearest' })
              }
            }
          }, 100)
        })
      }
    },
    // 使用模板
    async useTemplate(templateData) {
      const { data } = await usedCloudLectureTemplate({
        templateCatalogueId: templateData.id,
        courseCatalogueId: Number(this.catalogueId)
      })
      this.detailInfo = data
      this.titleStr = this.detailInfo.title ?? ''
      this.treeList = this.detailInfo.childCatalogue ?? []
      if (data.childCatalogue && data.childCatalogue.length > 0) {
        this.preNode = data.childCatalogue[0]
        this.selectTreeId = data.childCatalogue[0].id
        this.currentKey = data.childCatalogue[0].id
        if (this.preNode && this.preNode.type === 'DIGITAL_CLOUD_LECTURE_COURSE_CONTENT') {
          this.currentRemark = this.preNode.subtitle
          tinymce.activeEditor.setMode('design')
          if (this.preNode.contents && this.preNode.contents.length > 0) {
            this.html = this.preNode.contents[0].data
            this.htmlId = this.preNode.contents[0].id
          } else {
            this.html = ''
            this.htmlId = 0
          }
        } else {
          this.html = ''
          this.htmlId = 0
          tinymce.activeEditor.setMode('readonly')
        }
        this.showMenu = false
        this.$nextTick(() => {
          this.showMenu = true
        })
      }
      this.$refs.templateDialog.close()
    },
    previewTemplate(templateData) {
      this.$router.push({
        path: '/author/lectureNotesPreview',
        query: { catalogueId: templateData.id }
      })
    },
    previewTemplateManagement(templateData) {
      localStorage.setItem('showManagement', 'true')
      this.$router.push({
        path: '/author/lectureNotesPreview',
        query: { catalogueId: templateData.id }
      })
    },
    onCreated (editor) {
      this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
      if (!this.selectTreeId) {
        editor.disable()
      }
    },
    getHtml () {
      return tinymce.activeEditor.getContent()
    },

    // 菜单栏
    handleCopy(node, data) {
      this.$confirm('确认复制该内容?(实训块、习题块不支持复制，已为您忽略)', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const htmlString = data.contents && data.contents.length > 0 ? data.contents[0].data : ''
        const targetClasses = ['.training_card', '.excel_card', '.test_card', '.ai_card']
        // const replacementHtml = '<p style="width: 100%;height: 158px"></p>'
        const replacementHtml = '<p><br></p><p><br></p><p><br></p><p><br></p>'
        const newHtml = this.replaceDiv(htmlString, targetClasses, replacementHtml)
        this.currentKey = data.id
        this.selectTreeId = data.id
        this.contentAppend(null, newHtml, 'copy')
      }).catch((error) => {
        console.log(error)
      })
    },
    replaceDiv(htmlString, targetClasses, replacementHtml) {
      // 创建一个临时DOM容器
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = htmlString
      targetClasses.forEach(selector => {
        // 查找所有匹配的div元素
        const targetDivs = tempDiv.querySelectorAll(selector)
        // 替换每个目标div元素
        targetDivs.forEach(div => {
          const newDiv = document.createElement('div')
          newDiv.innerHTML = replacementHtml
          // 将新div的内容移动到目标div的位置
          while (newDiv.firstChild) {
            div.parentNode.insertBefore(newDiv.firstChild, div)
          }
          // 移除目标div
          div.parentNode.removeChild(div)
        })
      })
      // 返回修改后的HTML字符串
      return tempDiv.innerHTML
    },
    async contentAppend(data, html = '', type = 'add') {
      this.loading = true
      try {
        const obj = data ? {
          bookId: this.bookId,
          parentId: data.id,
          title: '',
          type: 'DIGITAL_CLOUD_LECTURE_COURSE_CONTENT'
        } : {
          bookId: this.bookId,
          parentId: this.catalogueId,
          title: '',
          type: 'DIGITAL_CLOUD_LECTURE_COURSE_CONTENT'
        }
        const { data: info } = await editBookCatalogue(obj, { authorization: this.token })
        if (type === 'copy') {
          await dragCatalogue({
            catalogueId: info.id,
            referCatalogueId: this.currentKey,
            position: 'AFTER'
          }, {
            authorization: this.token
          })
        }
        this.selectTreeId = info.id
        this.currentKey = info.id
        this.html = html
        this.htmlId = 0
        await this._getBookCatalogue()
        if (type === 'copy') {
          this.htmlTemp = ''
          await this.saveHtml(false, false, true)
          this.$message.success('复制成功')
        } else {
          this.$message.success('创建成功')
        }
      } catch (error) {
        console.log(error)
      } finally {
        this.loading = false
      }
    },
    menuAppend (node, data) {
      if (this.isPublish) return
      if (node) {
        this.currentNode = { bookId: this.bookId, parentId: data.id, data: null }
        this.contentAppend(data)
      } else {
        this.currentNode = { bookId: this.bookId, parentId: this.catalogueId, data: null }
        this.editChapterShow = true
      }
    },
    menuEdit (node, data) {
      this.currentNode = { bookId: this.bookId, parentId: data.parentId, data }
      this.editChapterShow = true
    },
    async menuRemove (node, data) {
      if (this.currentKey === data.id) {
        this.html = ''
        this.currentRemark = ''
        this.preNode = null
      }
      try {
        this.$confirm('确认删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          await deleteBookCatalogue({ catalogueId: data.id }, {
            authorization: this.token
          })
          this.$message.success('删除成功')
          await this._getBookCatalogue()
        }).catch((error) => {
          console.log(error)
        })
      } catch (error) {
        console.log(error)
      }
    },
    editDone() {
      this.editChapterShow = false
      this._getBookCatalogue()
    },
    async handleDrop (draggingNode, dropNode, dropType, ev) {
      if (dropType === 'inner' && dropNode.level === 2) {
        await this._getBookCatalogue()
        return
      }
      await dragCatalogue({
        catalogueId: draggingNode.data.id,
        referCatalogueId: dropNode.data.id,
        position: dropType.toUpperCase()
      }, {
        authorization: this.token
      })
      await this._getBookCatalogue()
    },
    endsWithParagraph(html) {
      const trimmedHtml = html.trimEnd()
      return trimmedHtml.endsWith('</p>')
    },
    async handleNodeClick (nodeData) {
      if (!tinymce.activeEditor || this.loading) return
      if (this.saveTimer) {
        clearInterval(this.saveTimer)
        this.saveTimer = null
      }
      await this.saveHtml(false, true)
      if (this.checkBase64(this.getHtml())) {
        this.$message.warning('还有未保存的编辑，请稍候')
        return
      }
      this.loading = true
      this.preNode = nodeData
      this.selectTreeId = nodeData.id
      this.currentKey = nodeData.id
      const editorComponent = this.$refs.tinyMceRef
      if (editorComponent && editorComponent.editor) {
        editorComponent.editor.undoManager.clear()
      }
      if (this.preNode && this.preNode.type === 'DIGITAL_CLOUD_LECTURE_COURSE_CONTENT') {
        this.currentRemark = this.preNode.subtitle
        tinymce.activeEditor.setMode('design')
        if (this.preNode.contents && this.preNode.contents.length > 0) {
          if (this.endsWithParagraph(this.preNode.contents[0].data)) {
            this.html = this.preNode.contents[0].data
          } else {
            this.html = this.preNode.contents[0].data + '<br>'
          }
          this.htmlId = this.preNode.contents[0].id
        } else {
          this.html = ''
          this.htmlId = 0
        }
      } else {
        this.html = ''
        this.htmlId = 0
        tinymce.activeEditor.setMode('readonly')
      }
      if (this.mode === 'default') {
        // 判断是否加锁
        if (this.keepHeartTime) {
          clearInterval(this.keepHeartTime)
        }
        await this._takeBook()
        if (!this.keepHeartTime) {
          this.keepHeartTime = setInterval(() => {
            this._takeBook()
          }, 5000)
        }
      }
      setTimeout(async () => {
        await this.$store.dispatch('app/setCatalogueId', this.currentKey)
        this.loading = false
        // tinymce.activeEditor.focus()
        if (this.mode === 'default') {
          await this._getDigitalContentChangeLog(nodeData.id)
        }
        this.contentChanged = false
        setTimeout(() => {
          this.initFun()
        }, 1000)
      }, 0)
    },
    async _getDigitalContentChangeLog(id) {
      const { data } = await getDigitalContentChangeLog({ catalogueId: id, pageNo: 1, pageSize: 1000 }, {
        authorization: this.token
      })
      this.CatalogueAuthorList = data.content
    },
    async setRemark (refresh = true) {
      try {
        const obj = {
          bookId: this.bookId,
          parentId: this.preNode.parentId,
          title: this.preNode.title,
          type: this.preNode.type,
          id: this.preNode.id,
          subtitle: this.currentRemark
        }
        await editBookCatalogue(obj, { authorization: this.token })
        if (refresh) {
          await this._getBookCatalogue()
        }
      } catch (e) {
        console.log(e)
      }
    },
    deleteDiv(htmlString, targetClasses) {
      // 创建一个临时DOM容器
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = htmlString
      targetClasses.forEach(selector => {
        // 查找所有匹配的div元素
        const targetDivs = tempDiv.querySelectorAll(selector)
        targetDivs.forEach(div => {
          div.parentNode.removeChild(div)
        })
      })
      const targetDivs = tempDiv.querySelectorAll('.draggable-div')
      targetDivs.forEach(div => {
        div.classList.remove('draggable-div-selected')
      })
      // 返回修改后的HTML字符串
      return tempDiv.innerHTML
    },
    async saveHtml (type = false, clickNode = false, isCopy = false) {
      if (tinymce.activeEditor && this.selectTreeId && this.catalogueId !== 0 && !this.otherUse) {
        if (!isCopy && this.loading) {
          return
        }
        if (!this.preNode) {
          return
        }
        if (this.checkBase64(this.getHtml())) {
          return
        }
        const newHtml = this.deleteDiv(this.getHtml(), ['.resize-handle', '.move-handle'])
        // debugger
        const obj = {
          data: newHtml,
          catalogueId: this.currentKey,
          webTabUuid: this.uuid,
          testpaperIdList: this.testElement(newHtml),
          trainingIdList: this.trainingElement(newHtml),
          trainingCaseIdList: this.caseElement(newHtml),
          digitalCatalogueData: {
            videoQuantity: this.findVideo(newHtml),
            attachFileQuantity: this.findFile(newHtml),
            imgQuantity: this.findImg(newHtml),
            audioQuantity: this.findAudio(newHtml),
            trainingQuantity: this.findTraining(newHtml)
          }
        }
        if (obj.data === this.htmlTemp) {
          console.log('文本内容一样，不提交')
          if (type) {
            this.$message.success('保存成功')
          }
          return
        }
        if (this.htmlId) {
          obj.id = this.htmlId
        }
        await saveContent(obj, {
          authorization: this.token
        })
        if (type) {
          this.$message.success('保存成功')
        }
        this.htmlTemp = obj.data
        // this.htmlId = data
        // await this._getBookCatalogue()
        if (!clickNode) {
          await this._getBookCatalogue()
        } else {
          for (let i = 0; i < this.treeList.length; i++) {
            const item = this.treeList[i]
            if (item.id === this.currentKey) {
              if (item.contents) {
                item.contents[0].data = newHtml
              } else {
                item.contents = [{ data: newHtml }]
              }
              return
            }
            if (item.childCatalogue && item.childCatalogue.length > 0) {
              for (let j = 0; j < item.childCatalogue.length; j++) {
                const subItem = item.childCatalogue[j]
                if (subItem.id === this.currentKey) {
                  if (subItem.contents) {
                    subItem.contents[0].data = newHtml
                  } else {
                    subItem.contents = [{ data: newHtml }]
                  }
                  return
                }
              }
            }
          }
        }
      }
    },
    findTraining(htmlStr) {
      const parser = new DOMParser()
      const doc = parser.parseFromString(htmlStr, 'text/html')
      const Training1 = doc.querySelectorAll('.excel_card').length
      const Training2 = doc.querySelectorAll('.training_card').length
      const Training3 = doc.querySelectorAll('.ai_card').length
      const Training4 = doc.querySelectorAll('.python_card').length
      return Training1 + Training2 + Training3 + Training4
    },
    findAudio(htmlStr) {
      const parser = new DOMParser()
      const doc = parser.parseFromString(htmlStr, 'text/html')
      const Audio = doc.querySelectorAll('audio').length
      return Audio
    },
    findFile(htmlStr) {
      const parser = new DOMParser()
      const doc = parser.parseFromString(htmlStr, 'text/html')
      const fileCard = doc.querySelectorAll('.file_download').length
      return fileCard
    },
    findVideo(htmlStr) {
      const parser = new DOMParser()
      const doc = parser.parseFromString(htmlStr, 'text/html')
      const videoLength = doc.querySelectorAll('video').length
      return videoLength
    },
    findImg(htmlStr) {
      const parser = new DOMParser()
      const doc = parser.parseFromString(htmlStr, 'text/html')
      const images = doc.querySelectorAll('img')
      let validImagesCount = 0

      images.forEach((img) => {
        const parent = img.parentElement
        const grandparent = parent ? parent.parentElement : null

        // 检查父级或祖父级是否包含 mceNonEditable 类
        const parentHasClass = parent && parent.classList.contains('mceNonEditable')
        const grandparentHasClass = grandparent && grandparent.classList.contains('mceNonEditable')

        // 仅当父级和祖父级都不包含 mceNonEditable 时，计入
        if (!parentHasClass && !grandparentHasClass) {
          validImagesCount++
        }
      })
      const imgButton = doc.querySelectorAll('.img_card_button')
      imgButton.forEach((item) => {
        validImagesCount += JSON.parse(item.parentNode.getElementsByClassName('info')[0].innerText).content.length
      })
      return validImagesCount
    },

    onFocus (editor) {
      if (this.saveTimer) {
        clearInterval(this.saveTimer)
        this.saveTimer = null
      }
      this.saveTimer = setInterval(async () => {
        this.autoTips = true
        await this.saveHtml()
        this.autoTips = false
        this.contentChanged = false
      }, 5000)
    },
    onChange (editor) {
      this.contentChanged = true
    },
    beforeunloadFn (e) {
      if (this.getHtml() !== this.htmlTemp) {
        e.returnValue = ('确定离开当前页面吗？')
        return '有未保存的编辑'
      }
      clearInterval(this.saveTimer)
      this.saveTimer = null
    },
    async preShowFn() {
      await this.saveHtml()
      localStorage.setItem('contentId', this.preNode.id)
      await this.$router.push({
        path: '/author/lectureNotesPreview',
        query: { catalogueId: this.catalogueId, bookId: this.bookId }
      })
    },
    async clickSave() {
      if (!this.preNode || this.preNode.type !== 'DIGITAL_CLOUD_LECTURE_COURSE_CONTENT') {
        return
      }
      await this.setRemark(false)
      await this.saveHtml(true)
    },
    handleHistory() {
      if (!this.preNode || this.preNode.type !== 'DIGITAL_CLOUD_LECTURE_COURSE_CONTENT') {
        return
      }
      this.$nextTick(() => {
        this.$refs.historyRef.handleOpen()
      })
    },
    showHistoryDetail(data) {
      this.historyHtml = data
      this.historyDetailShow = true
    },
    hiddenHistoryDetail() {
      this.historyHtml = ''
      this.historyDetailShow = false
    },
    setHistoryContent(data) {
      this.html = data
      this.saveHtml()
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-collapse-item__content{
  font-size: 8px !important;
  position: relative;
  .submit_message{
    width: 100%;
    height: 30px;
    position: absolute;
    bottom:0;
    left:0;
    display: flex;
    justify-content: space-between;
    .message_input{
      width: 240px;
      height: 20px !important;
      font-size: 8px !important;
      padding: 2px;
      .el-input__inner{
        width: 100%;
        height: 20px !important;
      }
    }
    .message_button{
      width: 40px;
      height: 20px;
      margin-top: 2px;
      font-size: 8px !important;
      padding: 5px;
    }
  }
  .message_content{
    width: 100%;
    margin-bottom: 10px;
    .message_item{
      .header{
        width: 100%;
        display: flex;
        font-size: 8px !important;
        margin-top: 5px;
        display: flex;
        .message_tag{
          font-size: 6px;
          height: 14px;
          padding: 2px;
        }
      }
      .message_content{
        margin-top: 3px;
      }
    }
  }
}
::v-deep .el-collapse-item__header{
  font-size: 8px !important;
  height: 20px;
  position: relative;
  .add{
    position: absolute;
    color: #2F80ED;
    left: 110px;
  }
}
.disabled_bg{
  background-color: #F5F7FA !important;
}

#tinymce{
  img{
    width:100%;
  }
}

.editor-dig {
  width: 100%;
  height: 100%;
  background: #FFF;
  box-sizing: border-box;
  position: relative;
  .word_button{
    width: 82px;
    height:30px;
    cursor: pointer;
    margin-right: 220px;
  }
  .other-use {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba($color: #000000, $alpha: .4);
    z-index: 999999;
  }
  .dig-head {
    width: 100%;
    overflow-x: auto;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    box-sizing: border-box;
    border-bottom: 1px solid rgba(224, 224, 224, 1);
    .title{
      margin-left: 50px;
      white-space: nowrap;
    }
    .dig-tips {
      font-size: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .dig-btn {
      border-radius: 4px;
      background: #2F80ED;
      color: #FFF;
      font-size: 14px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 5px 10px;
      box-sizing: border-box;
      cursor: pointer;
      margin-left: 10px;
      white-space: nowrap;
    }
  }

  .dig-box {
    width: 100%;
    height: calc(100% - 50px);
    display: flex;
    justify-content: center;
    background: #F9F9F9;

    .dig-left {
      width: 240px;
      height: 100%;
      background: #fff;
      padding-right: 5px;
      padding-top: 10px;
      border-right: 1px solid rgba(224, 224, 224, 1);
      box-sizing: border-box;
      flex-shrink: 1;
      .dig-menu{
        width: 300px;
        height: 100%;
        overflow: auto;
        transform-origin: top left;
        .svgIcon{
          width: 18px;
          height: 18px;
        }
        .chapter-title {
          width: 100%;
          height: 40px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: 5px;
          margin-bottom: 10px;
          padding: 0 10px;
          .btn_view{
            border-radius: 4px;
            font-size: 14px;
            display: flex;
            color: #000;
            justify-content: center;
            align-items: center;
            padding: 10px 20px;
            box-sizing: border-box;
            cursor: pointer;
            margin-left: 10px;
            white-space: nowrap;
            border: 1px solid #ccc;
          }
          .icon1 {
            color: #000;
            font-size: 16px;
            display: flex;
            align-items: center;

            img {
              width: 27px;
              height: 27px;
              margin-right: 5px;
            }
          }

          .add-btn {
            color: #2F80ED;
            font-size: 14px;
            margin-right: 10px;
            cursor: pointer;
          }
        }
        .menu_list{
          width: 100%;
          height: calc(100% - 50px);
          overflow-x:hidden;
          overflow-y:auto;
          ::v-deep .el-tree-node__content {
            height: auto;
            padding: 5px 0;
          }
          ::v-deep .el-button + .el-button {
            margin-left: 5px;
          }
          ::v-deep .el-button {
            font-size: 14px;
          }
          ::v-deep .el-tree-node__content > .el-tree-node__expand-icon {
            padding: 5px;
          }
          .menu_item{
            width: calc(100% - 30px);
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 14px;
            padding-right: 8px;
            .item_title{
              flex: 1;
              overflow: hidden;
              font-weight: 600;
              color: #000;
              font-size: 16px;
              @include scrollBar;
            }
            .item_option{
              flex-shrink: 0;
            }
            .menu_content{
              width: 230px;
              height: 155px;
              display: flex;
              .content_view{
                width: 200px;
                height: 155px;
                border: 1px solid #ccc;
                border-radius: 4px;
                box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.25);
                box-sizing: border-box;
                overflow: hidden;
                padding: 2px;
                box-sizing: border-box;
                white-space: normal;
                .html_view{
                  width: 776px;
                  height: 580px;
                  padding: 5px;
                  transform-origin: top left;
                  transform: scale(0.25);
                  line-height: 1.4;
                  overflow: hidden;
                  box-sizing: border-box;
                }
              }
              .option_view{
                width: 20px;
                height: 100%;
                margin-left: 10px;
                display: flex;
                flex-direction: column;
                justify-content: center;
              }
            }
          }
        }
      }
    }
    .dig-center {
      width: 800px;
      height: 100%;
      display: flex;
      flex-direction: column;
      background: #FFF;
      position: relative;
      padding: 10px;
      flex-shrink: 0;
      .toolbar {
        width: 100%;
        height: 80px;
      }

      .eidtor {
        width: 100%;
        height: 100%;
        // padding: 10px;
        overflow-y: auto;
      }
    }
    .right_content {
      width: 200px;
      height: 100%;
      padding: 10px 10px 10px 0;
      background-color: #fff;
      flex-shrink: 0;
      .right_input{
        width: 100%;
        border-radius: 5px;
        cursor: text;
      }
      .right_history{
        width: 100%;
        height: 5%;
        display: flex;
        justify-content: end;
        align-items: center;
        color: rgba(47, 128, 237, 1);
      }
      ::v-deep .el-textarea__inner{
        height: 85vh !important;
        resize: none;
        font-size: 14px;
        padding: 8px;
      }
    }
  }
  ::v-deep .el-tree-node__expand-icon{
    color: #000;
  }
  ::v-deep .el-tree-node__expand-icon.is-leaf{
    color: transparent;
  }
  ::v-deep .w-e-bar-divider {
    display: none;
  }

  ::v-deep .w-e-textarea-video-container {
    background: transparent;
    border: none;
    padding: 0;
    margin: 0;

    video {
      width: 100%;
    }
  }

  .pre {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 100%;
    z-index: 99;
    overflow: auto;
  }
}
.history_detail{
  width: 770px;
  height: 520px;
  overflow: hidden;
}

.more_btn{
  color:rgba(47, 128, 237, 1);
  font-size: var(--font-size-XL);
  transform: rotate(90deg);
  &:hover{
    cursor: pointer;
  }
}
</style>
