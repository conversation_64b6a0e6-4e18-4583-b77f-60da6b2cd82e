<template>
  <div class="index-box">
    <div class="card-box h">
      <div class="card-title-box">
        <img src="@/assets/images/aiLab.png" alt="AI实验室" class="ai-lab-img" />
        <div class="t-title">我的AI实验</div>
      </div>
      <div class="w body-h">
        <Empty v-if="subjectList.length === 0" :msg="'暂无数据'" />
        <div v-else class="subject-list">
          <div v-for="item in subjectList" :key="item.id" class="subject-item" @click="toSubjectDetail(item)">
            <img v-if="item.aicourse.labCoverUrl || item.aicourse.coverUrl" :src="item.aicourse.labCoverUrl || item.aicourse.coverUrl" alt="学科封面" class="subject-cover" />
            <img v-else src="@/assets/images/default-cover.jpg" alt="学科封面" class="subject-cover" />
            <div class="subject-name">{{ item.aicourse.title }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Empty from '@/components/classPro/Empty/index.vue'
import { getStudentCourseList } from '@/api/course-api'
import { mapGetters } from 'vuex'
export default {
  components: {
    Empty
  },
  data () {
    return {
      subjectList: []
    }
  },
  computed: {
    ...mapGetters(['selectedRegion'])
  },
  watch: {
    selectedRegion() {
      this.getSubjectList()
    }
  },
  mounted () {
    this.getSubjectList()
  },
  methods: {
    async getSubjectList () {
      const { data } = await getStudentCourseList({
        resourceType: 'TRAINING_RESOURCE',
        studentCourseListType: 'ASSISTANT'
      })
      this.subjectList = data
    },
    toSubjectDetail(item) {
      this.$router.push({
        path: `/classpro/aitraining/detail/${item.aicourse.id}/${item.id}`,
        query: {
          from: 'my'
        }
      })
    }
  }
}
</script>

    <style lang="scss" scoped>
    .index-box {
      width: 100%;
      height: 100%;
      overflow: hidden;
      overflow-y: auto;
      box-sizing: border-box;
      @include scrollBar;
      background: #FFF;
      border-radius: 10px;

      .body-h {
        height: calc(100% - 50px);
        /*增加滚动条*/
        overflow-y:auto;
        overflow-x: hidden;
        @include scrollBar;
        &::-webkit-scrollbar {
          /*滚动条整体样式*/
          width: 5px;
          height: 1px;
        }
        .subject-list {
          display: flex;
          flex-wrap: wrap;
          justify-content: flex-start;
          gap: 10px;
          align-items: center;
          .subject-item {
            width: calc((100% - 20px) / 3);
            height: 200px;
            border-radius: 5px;
            border: 1px solid #cce5fe;
            background: #F5FAFF;
            overflow: hidden;
            padding: 10px;
            cursor: pointer;
            img{
              width: 100%;
              height: calc(100% * 3 / 4);
              border-radius: 5px;
              object-fit: cover;
            }
            .subject-name {
              font-size: var(--font-size-L);
              color: #000;
              margin-top: 10px;
            }
          }
        }
      }

      .card-box {
        padding: 5px 15px;
        margin-bottom: 10px;
        box-sizing: border-box;

        &:last-child {
          margin-bottom: 0;
        }

        .card-title-box {
          margin: 10px 0;
          display: flex;
          align-items: center;
          .ai-lab-img {
            width: 20px;
            height: 20px;
            margin-right: 5px;
          }
          .t-title {
            color: #000;
            font-size: var(--font-size-L);
            font-weight: 500;
          }
        }
      }
    }
    </style>
