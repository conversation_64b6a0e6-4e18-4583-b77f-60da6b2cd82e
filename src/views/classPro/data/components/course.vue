<template>
  <div class="course-data">
    <div class="wrap">
      <el-table
        :data="tableData"
        height="100%"
        style="width: 100%;"
        :header-row-class-name="tableHeader"
        :row-class-name="rowClass"
      >
        <el-table-column
          prop="course.name"
          label="课程包名称"
          align="center"
          :formatter="courseName"
        />
        <el-table-column
          prop="course.coursePackage.name"
          label="所属课程"
          align="center"
          :formatter="coursePackageName"
        />
        <el-table-column
          prop="total"
          label="包含课时数"
          align="center"
        />
        <el-table-column
          prop="finishedProgress"
          label="已上课时数"
          align="center"
        />
        <el-table-column
          label="上课率"
          align="center"
          :formatter="attendPer"
        />
        <el-table-column
          prop="createdAt"
          label="添加时间"
          align="center"
          :formatter="addTime"
        />
        <div slot="empty" class="empty">
          <img src="@/assets/images/empty.png" alt="ai课堂为空" />
          <div class="hint">暂时没有数据哦～</div>
        </div>
      </el-table>
    </div>
  </div>
</template>

<script>
import { getStuCourseList, getUserPlanLessonList } from '@/api/lesson-api.js'
import { formatYYYYMMDD2 } from '@/utils/date'
import { mapGetters } from 'vuex'
export default {
  data () {
    return {
      tableData: [],
      stuCourseList: [],
      planLessonList: [],
      planLessonJson: {},
      loadFinished1: false,
      loadFinished2: false
    }
  },
  computed: {
    ...mapGetters(['selectedRegion'])
  },
  watch: {
    selectedRegion() {
      this._getStuCourseList()
      this._getUserPlanLessonList()
    }
  },
  methods: {
    tableHeader (row, rowIndex) {
      return 'table-header'
    },
    rowClass (row, rowIndex) {
      return 'row-class'
    },
    //  获取普通课程包
    _getStuCourseList () {
      this.loadFinished1 = false
      const params = {
        'assistantUserId': this.$store.getters.id,
        'studentCourseListType': 'DATA_CENTER'
      }
      getStuCourseList(params).then(
        response => {
          this.stuCourseList = response.data || []
          this.loadFinished1 = true
          this.makeTableData()
        }
      )
    },
    // 获取补课课程包
    _getUserPlanLessonList () {
      getUserPlanLessonList().then(
        response => {
          this.planLessonList = response.data
          if (this.planLessonList) {
            if (this.planLessonList.length > 1) {
              this.planLessonJson = {
                'course': {
                  'name': '平台排课'
                },
                'total': this.planLessonList.length,
                'finishedProgress': this.planLessonList.length,
                'createdAt': [this.planLessonList[0].createdAt, this.planLessonList[this.planLessonList.length - 1].createdAt],
                'type': 'plan'
              }
            } else if (this.planLessonList.length === 1) {
              this.planLessonJson = {
                'course': {
                  'name': '平台排课'
                },
                'total': this.planLessonList.length,
                'finishedProgress': this.planLessonList.length,
                'createdAt': [this.planLessonList[0].createdAt],
                'type': 'plan'
              }
            }
          }
          this.loadFinished2 = true
          this.makeTableData()
        }
      )
    },
    // 整合课程包
    makeTableData () {
      if (this.loadFinished1 && this.loadFinished2) {
        this.tableData = []
        this.tableData = this.tableData.concat(this.stuCourseList)
        if (this.planLessonJson.type) this.tableData = this.tableData.concat(this.planLessonJson)
      }
    },
    courseName (row, column, cellValue, index) {
      return cellValue || '平台排课'
    },
    coursePackageName (row, column, cellValue, index) {
      return cellValue || '-'
    },
    addTime (row, column, cellValue, index) {
      if (this.tableData[index].type === 'plan' && cellValue.length === 1) return formatYYYYMMDD2(cellValue[0])
      if (this.tableData[index].type === 'plan' && cellValue.length > 1) return `${formatYYYYMMDD2(cellValue[0])}-${formatYYYYMMDD2(cellValue[1])}`
      return formatYYYYMMDD2(cellValue)
    },
    attendPer (row, column, cellValue, index) {
      if (this.tableData[index].total !== 0) {
        return (this.tableData[index].finishedProgress / this.tableData[index].total * 100).toFixed(0) + '%'
      }
      return '0%'
    }
  }
}
</script>

<style lang="scss" scoped>
.course-data {
    width: 100%;
    height: 100%;

    .wrap {
        width: 100%;
        height: 100%;
        padding: 20px;
    }
    .empty {
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        img {
            width: 126px;
            height: 128px;
        }

        .hint {
            display: flex;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 14px;
            color: #8C8C8C;
            letter-spacing: 0.22px;

            .hint-blue {
                color: rgba(31, 102, 255, 1);
                cursor: pointer;
            }
        }

        .hint-padding {
            padding:15px 0 4px
        }
    }
}
</style>

<style lang="scss">
.course-data {
    .wrap {
        .el-range-editor {
            margin: 0 10px
        }

        .el-input__inner {
            border: 1px solid #3479FF;
            height: 32px;
        }

        .el-range-separator {
            line-height: 22px;
        }

        .el-range__icon {
            display: none;
        }

        .el-range-input {
            font-weight: 400;
            font-size: 12px;
            color: rgba(125,125,125,0.90);
        }

        .el-range__close-icon {
            line-height: 22px;
            color: red;
        }

        .table-header {
            font-weight: 400;
            font-size: var(--font-size-L);
            color: #3479FF;
            letter-spacing: 0.26px;

            th {
                background: #F8FAFF;
            }

            .el-table__cell {
                border-bottom: none;
            }
        }

        .row-class {
            font-weight: 400;
            font-size: var(--font-size-L);
            color: #0E0E0E;
            letter-spacing: 0.22px;
        }
    }
}
</style>
