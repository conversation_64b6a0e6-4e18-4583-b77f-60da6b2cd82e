<template>
  <div v-loading="loading" class="task-class">
    <van-nav-bar
      left-text="返回"
      left-arrow
      title="任务阅读模式"
      @click-left="back"
    />
    <div v-if="isEmpty" class="task-content-empty">
      <ReadEmpty v-if='showEmpty' msg='本书暂无任务' source='h5'/>
    </div>
    <div v-else class="task-content">
      <div class="task-box">
        <div class="task-title">
          <div class="top_content">
            <div class="left" @click="handleDetail(currentDigitalHomework)">
              <p>当前任务</p>
              <p class="task_title">{{ currentDigitalHomework ? currentDigitalHomework.title : '暂无' }}</p>
              <p class="to">></p>
            </div>
            <div class="right">
              <p class="p1">已完成：{{ completed }}</p>
              <p class="p2">总任务：{{ total }}</p>
            </div>
          </div>
        </div>
        <div class="tbody-box">
          <van-collapse v-model="activeNames" style="width:100%" accordion @change="change">
            <van-collapse-item
              v-for="(item, index) in catalogueList"
              :key="index"
              :name="item.id"
              class="card-title"
              :title="item.title"
            >
              <template #title>
                <div class="title">
                  <div class="index">{{ index+1 }}</div>
                  <div class="title_text">{{ item.title }}</div>
                  <div class="proress"><span class="num">{{ findProgress(list[item.id])+'/'+findLength(list[item.id]) }}</span><span>已完成</span></div>
                  <div></div>
                </div>
              </template>
              <div v-if="!list[item.id]||list[item.id].length===0">
                <Empty />
              </div>
              <div
                v-for="(item1, index1) in list[item.id]"
                :key="index1"
                class="test_item"
                size="large"
                :icon="item1.userDigitalHomework||item1.userTestpaper&&item1.userTestpaper.progressStatus === 'FINISHED'?'el-icon-success':''"
                :color="item1.userDigitalHomework||item1.userTestpaper&&item1.userTestpaper.progressStatus === 'FINISHED'?'#FFFFFF':'#4FACFE'"
              >
                <div class="item" :class="item1.userDigitalHomework||item1.userTestpaper&&item1.userTestpaper.progressStatus === 'FINISHED'?'done':''">
                  <div class="tab">{{ index1+1 }}</div>
                  <div class="title1">{{ item1.title }}</div>
                  <el-button v-if="bookInfoStudentId!==0||(index===0&&catalogueList&&catalogueList[0].id===item1.digitalCatalogueId)||(index===1&&catalogueList&&catalogueList[1].id===item1.digitalCatalogueId)" class="button1" type="primary" plain @click="handleDetail(item1)">{{ item1.userDigitalHomework||item1.userTestpaper&&item1.userTestpaper.progressStatus === 'FINISHED'?'已完成':'做任务' }}</el-button>
                  <el-button v-else type="primary" class="button1" icon="el-icon-lock" plain @click="pay">购买学习</el-button>
                </div>
              </div>
            </van-collapse-item>
          </van-collapse>
        </div>
      </div>
    </div>
    <PayToast ref="pay" :good-info="bookInfo && bookInfo.goodsComm" />
  </div>
</template>

<script>
import ReadEmpty from '@/components/classPro/Empty/readEmpty'
import defaultCourse from '@/assets/images/default-cover.jpg'
import { getBook, getDigitalTaskListByCatalogueId, getBookCatalogueByVersion, getDigitalTaskList } from '@/api/digital-api.js'
import { formatYYYYMMDDHHmm } from '@/utils/time.js'
// import taskDetail from '../detail.vue'
import Empty from '@/components/classPro/Empty/index.vue'
import PayToast from '@/components/classPro/Pay/index.vue'
export default {
  components: { Empty, PayToast, ReadEmpty },
  data() {
    return {
      defaultCourse,
      formatYYYYMMDDHHmm,
      bookTitle: '',
      bookId: 0,
      studentCourseId: 0,
      visible: false,
      list: {},
      progressShow: false,
      selectTaskInfo: null,
      detailShow: false,
      taskId: '',
      testId: 0,
      bookInfo: null,
      catalogueList: [],
      currentDigitalHomework: null,
      CatalogueId: 0,
      freeId: 0,
      total: 0,
      completed: 0,
      activeNames: [],
      loading: true,
      bookInfoStudentId: 0,
      isEmpty: true,
      showEmpty: false
    }
  },
  watch: {
    async '$route' (to, from) {
      if (this.$route.query && this.$route.query.token) {
        await this.$store.dispatch(
          'user/AppLogin',
          'Bearer ' + to.query.token
        )
        await this.$store.dispatch('user/GetInfo')
      }
      if (to.query.id !== from.query.id) {
        this.bookId = this.$route.query && this.$route.query.id
        await this._getBook()
        this.studentCourseId = this.$route.query.studentCourseId || this.bookInfo.classstudentCourseId || this.bookInfo.studentCourseId
        await this._getBookCatalogueByVersion()
        await this.getCurrntHomework()
        this._getDigitalHomeworkList()
      }
    }
  },
  async mounted() {
    if (this.$route.query && this.$route.query.token) {
      await this.$store.dispatch('user/AppLogin', 'Bearer ' + this.$route.query.token)
    }
    this.bookId = this.$route.query && this.$route.query.id
    await this._getBook()
    this.studentCourseId = this.$route.query.studentCourseId || this.bookInfo.classstudentCourseId || this.bookInfo.studentCourseId
    await this._getBookCatalogueByVersion()
    await this.getCurrntHomework()
    this._getDigitalHomeworkList()
  },
  methods: {
    back() {
      if (this.$route.query.from === 'app') {
        if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.bingo_download) {
          window.webkit.messageHandlers.bingo_action.postMessage('event_back')
        } else if (window.bingo_action) {
          window.bingo_action.postMessage('event_back')
        } else {
          this.$router.replace(`/bingoBook/bookRead?bookId=${this.$route.query.id}`) // 返回上一页
        }
      } else {
        this.$router.replace(`/bingoBook/bookRead?bookId=${this.$route.query.id}`) // 返回上一页
      }
    },
    findProgress(val) {
      return val && val.filter(item1 => {
        return item1.userDigitalHomework || item1.userTestpaper && item1.userTestpaper.progressStatus === 'FINISHED'
      }).length
    },
    findLength(val) {
      return val && val.length
    },
    change(val) {
      console.log(val)
    },
    async getCurrntHomework() {
      const { data } = await getDigitalTaskList({
        digitalBookId: this.bookId,
        studentCourseId: this.studentCourseId
      })
      this.currentDigitalHomework = data.currentDigitalHomework
      this.total = data.total
      if (this.total === 0) {
        this.isEmpty = true
        this.showEmpty = true
      } else {
        this.isEmpty = false
        this.showEmpty = false
      }
      this.completed = data.completed
    },
    changeData(id) {
      this.CatalogueId = id
      this._getDigitalHomeworkList()
    },
    async _getBookCatalogueByVersion() {
      const { data } = await getBookCatalogueByVersion({
        bookId: this.bookId,
        type: 'CHAPTER'
        // approvedOnly: false
      })
      this.catalogueList = data
      if (data.length === 0) {
        this.$toast('当前教材暂无内容')
        this.loading = false
        return
      }
      this.CatalogueId = data[0].id
    },
    pay() {
      this.$toast('请前往教材详情购买！')
    },
    async _getBook() {
      if (this.bookId) {
        const { data } = await getBook({
          bookId: this.bookId,
          scene: 'BOOK_CATALOGUE_OWN'
        })
        this.bookTitle = data.title
        this.bookInfo = data
        this.bookInfoStudentId = this.bookInfo.studentCourseId || 0
      }
    },
    async _getDigitalHomeworkList() {
      try {
        const requests = this.catalogueList.map(item =>
          getDigitalTaskListByCatalogueId({
            digitalCatalogueId: item.id,
            studentCourseId: this.studentCourseId
          }).then(response => ({
            id: item.id,
            data: response.data
          }))
        )
        const results = await Promise.all(requests)
        this.list = {}
        results.forEach(result => {
          this.list[result.id] = result.data
        })
        this.loading = false
      } catch (error) {
        this.loading = false
        console.error('Error fetching digital homework list:', error)
      }
    },
    handleDetail(item) {
      if (!item) {
        this.$toast('暂无当前任务，请点击任务开始学习')
      }
      // if ((this.studentCourseId === '0') && this.freeId !== item.id) {
      //   this.pay()
      //   return
      // }
      if (item.digitalHomeworkType === 'TRAINING'){
        this.$toast('为体验完整功能，请在电脑客户端操作')
        return
      }
      if (item.sourceId) {
        const testId = item.sourceId
        this.$router.push({ path: '/bingoBook/test', query: { testId, studentCourseId: this.studentCourseId }})
        return
      } else {
        this.$router.push({ path: '/bingoBook/taskDetail', query: { taskId: item.id, studentCourseId: this.studentCourseId }})
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep .van-nav-bar__content {
  background: #ffffff;
  height: 46px;
}
::v-deep .van-nav-bar__text {
  color: #000000 !important;
}
::v-deep .van-icon-arrow-left {
  color: #000000 !important;
}
  .el-loading-parent--relative {
      position: relative;
  }
::v-deep .van-hairline--top-bottom::after, .van-hairline-unset--top-bottom::after{
    border: none;
}
.task-class {
    width: 100%;
    min-height: 100%;
    background: #EFF6FF;
    .head-box {
        height: 40px;
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        position: relative;

        .share {
            position: absolute;
            right: 40px;
            font-size: 12px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            cursor: pointer;

            img {
                width: 20px;
                height: 20px;
            }
        }

        .back {
            width: 20px;
            height: 20px;
            object-fit: cover;
            cursor: pointer;
        }

        .head-title {
            color: #000;
            font-size: var(--font-size-L);
            font-weight: 500;
            margin-left: 10px;
        }
    }
    .no-conversion {
      width: 100%;
      height: 80vh;
    }
    .task-content-empty {
      padding: 5px 0;
      width: 100%;
      height: 80vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .task-content {
        padding: 5px 0;
        width: 100%;
        .task-title {
            height: 100px;
            color: #000;
            font-size: var(--font-size-L);
            font-weight: 500;
            display: flex;
            justify-content: space-between;

            .top_content {
                width: 100%;
                display: flex;
                justify-content: space-between;

                .left {
                    width: 174px;
                    height: 86px;
                    border-radius: 5px;
                    background: #BFE8FF;
                    position: relative;
                    .task_title{
                      width: 120px;
                      overflow: hidden;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                    }
                    .to{
                      position: absolute;
                      bottom: 0px;
                      right: 10px;
                    }
                    p {
                        font-size: 12px;
                        font-weight: 400;
                        margin-left: 20px;
                        line-height: 25px;
                        color: #1C447A;
                        span {
                            font-weight: 700;
                        }
                    }
                }

                .right {
                    width: 174px;
                    height: 86px;
                    border-radius: 5px;
                    background: #9CE2B9;
                    font-size: 12px;
                    color: #297247;
                    .p1 {
                        margin-left: 20px;
                        line-height: 25px;
                    }

                    .p2 {
                        margin-left: 20px;
                        line-height: 25px;
                    }

                }
            }
        }

        .task-box {
            border-radius: 10px;
            width: 100%;
            padding: 10px;
            box-sizing: border-box;

            .tbody-box {
                width: 100%;
                display: flex;
            .card-title{
                width:100% ;
                margin-bottom: 20px;
                border-radius: 5px;
                .title{
                    height: 110px;
                    position: relative;
                    .index{
                        width: 24px;
                        height: 24px;
                        line-height: 24px;
                        text-align: center;
                        border-radius: 12px;
                        font-weight: bold;
                        background: linear-gradient(90deg, #4FACFE 0%, #00F2FE 100%);
                        position: absolute;
                        top:3px
                    }
                    .title_text{
                        position: absolute;
                        width: 250px;
                        position: absolute;
                        top:3px;
                        left: 50px;
                        font-weight: bold;
                    }
                    .proress{
                        padding: 5px;
                        border-radius: 5px;
                        position: absolute;
                        background: #EFF6FF;
                        color: #2F80ED;
                        font-weight: bold;
                        font-size: 10px;
                        position: absolute;
                        left: 50px;
                        bottom: 4px;
                        .num{
                            margin-right: 10px;
                        }
                    }
                }
            }
            .test_item{
                width:calc(100%-50px);
                margin-left: 50px;
                height: 80px;
                border-bottom: 1px solid #ebedf0;
                box-sizing: content-box;
                position: relative;
                color: #000;
                margin-bottom: 10px;
                .button1{
                    font-size: 12px;
                    padding: 5px;
                    position: absolute;
                    right: 10px;
                    bottom: 10px;
                }
                .tab{
                    width: 20px;
                    height: 20px;
                    border: 1px solid #000;
                    text-align: center;
                    line-height: 20px;
                    border-radius: 3px;
                    position: absolute;
                    top: 0px;
                    left: 0px;
                }
                .title1{
                    position: absolute;
                    top:0px;
                    left: 40px;
                    width: 200px;
                }
            }
            }
        }
    }

    .task-pop {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: #E9F2FF;
    }
}
</style>
