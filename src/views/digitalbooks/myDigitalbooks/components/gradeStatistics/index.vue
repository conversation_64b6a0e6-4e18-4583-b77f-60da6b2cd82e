<template>
  <div v-if="dialogShow" class="grade-statistics-container">
    <div class="header-bar">
      <img
        class="back-btn"
        src="@/assets/digitalbooks/arrow-left.svg"
        @click="goBack"
      />
      <div class="header-title" @click="goBack">成绩统计</div>
    </div>

    <div class="page-content">

      <div class="page-header">
        <!-- <h1 class="page-title">成绩统计分析</h1> -->
        <div class="class-info">
          <span class="info-text">当前班级：</span>
          <span class="info-value">{{ currentClass }}</span>
          <span class="info-text"> | {{ examType }}：</span>
          <span class="info-value">{{ examTitle }}</span>
        </div>
      </div>

      <div class="overview-section">
        <div class="stats-card">
          <div class="card-title">班级平均分</div>
          <div class="card-content">
            <div class="main-value">{{ statisticsData.averageScore }}</div>
          </div>
          <div class="card-subtitle">学生总平均分</div>
        </div>

        <div class="stats-card">
          <div class="card-title">学生完成率</div>
          <div class="card-content">
            <div class="main-value">{{ statisticsData.completionRate }}%</div>
          </div>
          <div class="card-subtitle">{{ statisticsData.completedCount }}/{{ statisticsData.totalCount }} 学生完成</div>
        </div>

        <div class="stats-card">
          <div class="card-title">总体正确率</div>
          <div class="card-content">
            <div class="main-value">{{ statisticsData.correctRate }}%</div>
          </div>
          <div class="card-subtitle">正确答题比例</div>
        </div>

        <div class="stats-card">
          <div class="card-title">平均用时</div>
          <div class="card-content">
            <div class="main-value">{{ statisticsData.averageTime === 0 ? '--' : statisticsData.averageTime + '分钟' }}</div>
          </div>
          <div class="card-subtitle">平均完成时间</div>
        </div>
      </div>

      <div class="chart-section">
        <div class="chart-card">
          <div class="chart-header">
            <div class="chart-title">分数分布</div>
          </div>
          <div class="chart-container">
            <div v-if="hasScoreData" id="scoreDistributionChart" class="chart"></div>
            <div v-else class="chart-empty">
              <Empty msg="暂无分数分布数据" />
            </div>
          </div>
        </div>

        <div class="chart-card">
          <div class="chart-header">
            <div class="chart-title">知识点掌握情况</div>
          </div>
          <div class="chart-container">
            <div v-if="hasKnowledgeData" id="knowledgeChart" class="chart"></div>
            <div v-else class="chart-empty">
              <Empty msg="暂无知识点数据" />
            </div>
          </div>
        </div>
      </div>

      <div class="suggestions-card">
        <div class="suggestions-title">教学建议</div>
        <div class="suggestions-list" v-if="suggestions && suggestions.length > 0">
          <div v-for="(suggestion, index) in suggestions" :key="index" class="suggestion-item">
            {{ suggestion }}
          </div>
        </div>
        <div v-else class="suggestions-empty">
          <Empty msg="暂无教学建议" />
        </div>
      </div>

      <div class="students-section">
        <div class="section-header">
          <div class="section-title">学生成绩详情</div>
          <div class="actions">
            <el-button class="export-btn" @click="handleExport">导出</el-button>
          </div>
        </div>

        <div class="students-table">
          <el-table :data="studentsData" style="width: 100%" class="grade-table">
            <el-table-column prop="name" label="学生姓名" min-width="140">
              <template slot-scope="scope">
                <div class="student-name">
                  <div class="avatar">{{ scope.row.name.charAt(0) }}</div>
                  <span>{{ scope.row.name }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="完成状态" min-width="100">
              <template slot-scope="scope">
                <span :class="['status-badge', scope.row.status === '已完成' ? 'completed' : 'incomplete']">
                  {{ scope.row.status }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="totalQuestions" label="答题数" min-width="80" />
            <el-table-column prop="correctAnswers" label="答对题数" min-width="90" />
            <el-table-column prop="correctRate" label="正确率" min-width="80" />
            <el-table-column prop="timeSpent" label="用时" min-width="80" />
            <el-table-column label="" width="30" />
            <el-table-column prop="score" :label="`得分（总分${totalScore}）`" min-width="130">
              <template slot-scope="scope">
                <span class="score-badge">{{ scope.row.score }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <!-- Loading功能已注释 -->
    <!-- <div v-if="loading" class="loading-overlay">
      <div class="loading-box" v-loading="true" element-loading-text="数据加载中..." element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0)" element-loading-custom-class="custom-loading">
      </div>
    </div> -->
  </div>
</template>

<script>
import { getStatisticsData, getKnowledgeData, getStudentList, getAiComment } from '@/api/digitalHomework'
import Empty from '@/components/classPro/Empty/index.vue'

export default {
  name: 'GradeStatistics',
  components: {
    Empty
  },
  computed: {
    hasScoreData() {
      const distribution = this.scoreDistribution
      return distribution && Object.keys(distribution).length > 0
    },

    hasKnowledgeData() {
      return this.knowledgePoints && this.knowledgePoints.length > 0
    }
  },
  data() {
    return {
      dialogShow: false,
      // loading: false, // Loading功能已注释
      digitalHomeworkId: null,
      currentClass: '--',
      examTitle: '--',
      examType: '--',
      statisticsData: {
        averageScore: 0,
        completionRate: 0,
        completedCount: 0,
        totalCount: 0,
        correctRate: 0,
        averageTime: 0
      },
      scoreDistribution: {
        '<60': 0,
        '60-69': 0,
        '70-79': 0,
        '80-89': 0,
        '90-100': 0
      },
      knowledgePoints: [],
      suggestions: [],
      studentsData: [],
      totalScore: 100,
      scoreChart: null,
      knowledgeChart: null
    }
  },
  mounted() {
    if (this.$route.path.includes('/gradeStatistics')) {
      const id = this.$route.params.id || this.$route.query.digital_homework_id
      this.digitalHomeworkId = id ? parseInt(id) : null
      if (this.$route.query.type) {
        this.examType = this.$route.query.type
      }

      if (this.digitalHomeworkId) {
        this.loadAllData()
      }
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)

    if (this.scoreChart) {
      this.scoreChart.dispose()
    }
    if (this.knowledgeChart) {
      this.knowledgeChart.dispose()
    }
  },
  methods: {
    async loadAllData() {
      // this.loading = true // Loading功能已注释
      // const startTime = Date.now() // Loading功能已注释

      try {
        await Promise.all([
          this.loadStatisticsData(),
          this.loadKnowledgeData(),
          this.loadStudentList()
        ])

        try {
          this.initCharts()
        } catch (chartError) {
          console.log('图表初始化失败:', chartError)
        }

        this.$nextTick(() => {
          this.loadAiComment()
        })

        // Loading功能已注释
        // const elapsed = Date.now() - startTime
        // const minLoadingTime = 800
        // if (elapsed < minLoadingTime) {
        //   await new Promise(resolve => setTimeout(resolve, minLoadingTime - elapsed))
        // }

      } catch (error) {
        console.error('加载数据失败:', error)
      } finally {
        // this.loading = false // Loading功能已注释
      }
    },

    async loadStatisticsData() {
      try {
        const response = await getStatisticsData({
          digital_homework_id: this.digitalHomeworkId
        })

        if (response.code === 200) {
          const data = response.data
          this.statisticsData = {
            averageScore: data.average_score || 0,
            completionRate: data.finished_rate || 0,
            completedCount: data.finished_user || 0,
            totalCount: data.total_user || 0,
            correctRate: data.correct_rate || 0,
            averageTime: data.average_time || 0
          }

          if (data.class_name) {
            this.currentClass = data.class_name
          }
          if (data.digital_homework_title) {
            this.examTitle = data.digital_homework_title
          }

          this.scoreDistribution = data.score_distribute || {
            '<60': 0,
            '60-69': 0,
            '70-79': 0,
            '80-89': 0,
            '90-100': 0
          }
        }
      } catch (error) {
        console.log('加载统计数据失败:', error)
      }
    },

    async loadKnowledgeData() {
      try {
        const response = await getKnowledgeData({
          digital_homework_id: this.digitalHomeworkId
        })

        if (response.code === 200) {
          this.knowledgePoints = response.data.knowledge_situation || []
        }
      } catch (error) {
        console.log('加载知识点数据失败:', error)
      }
    },

    async loadStudentList() {
      try {
        const response = await getStudentList({
          digital_homework_id: this.digitalHomeworkId
        })

        if (response.code === 200) {
          const items = response.data.items || []
          const totalQuestion = response.data.total_question || 0

          if (response.data.total_score) {
            this.totalScore = response.data.total_score
          }

          this.studentsData = items.map(item => ({
            name: item.display_name,
            status: item.progress_status === 'FINISHED' ? '已完成' : '未完成',
            totalQuestions: item.all_num || 0,
            correctAnswers: item.progress_status === 'FINISHED' ? item.correct_num : '-',
            correctRate: item.progress_status === 'FINISHED' && totalQuestion > 0
              ? `${Math.round((item.correct_num / totalQuestion) * 100)}%`
              : '-',
            timeSpent: item.progress_status === 'FINISHED' ? this.formatTime(item.time) : '-',
            score: item.score || 0
          }))
        }
      } catch (error) {
        console.log('加载学生列表失败:', error)
      }
    },

    async loadAiComment() {
      try {
        const response = await getAiComment({
          digital_homework_id: this.digitalHomeworkId,
          average_score: this.statisticsData.averageScore,
          finished_rate: this.statisticsData.completionRate,
          correct_rate: this.statisticsData.correctRate,
          average_time: this.statisticsData.averageTime,
          score_distribute: this.scoreDistribution,
          knowledge_situation: this.knowledgePoints
        })

        if (response.code === 200) {
          const comment = response.data.comment || ''
          this.suggestions = comment.split('\n\n').filter(item => item.trim())
        }
      } catch (error) {
        console.log('加载AI教学建议失败:', error)
      }
    },


    initCharts() {
      this.$nextTick(() => {
        if (this.hasScoreData) {
          this.initScoreDistributionChart()
        }
        if (this.hasKnowledgeData) {
          this.initKnowledgeChart()
        }
        window.addEventListener('resize', this.handleResize)
      })
    },
    handleResize() {
      if (this.scoreChart) {
        this.scoreChart.resize()
      }
      if (this.knowledgeChart) {
        this.knowledgeChart.dispose()
        this.knowledgeChart = null
        this.$nextTick(() => {
          this.initKnowledgeChart()
        })
      }
    },
    initScoreDistributionChart() {
      const chartElement = document.getElementById('scoreDistributionChart')
      if (!chartElement) {
        return
      }

      const echarts = require('echarts')
      this.scoreChart = echarts.init(chartElement)

      const option = {
        title: {
          show: false
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: Object.keys(this.scoreDistribution),
          axisLabel: {
            color: '#666'
          }
        },
        yAxis: {
          type: 'value',
          min: 0,
          max: function() {
            const maxValue = Math.max(...Object.values(this.scoreDistribution))
            if (maxValue <= 7) {
              return 7
            }
            return maxValue
          }.bind(this),
          splitNumber: 7,
          axisLabel: {
            color: '#666',
            formatter: function(value) {
              return Math.floor(value)
            }
          }
        },
        series: [{
          name: '学生人数',
          type: 'bar',
          data: Object.values(this.scoreDistribution),
          itemStyle: {
            color: function(params) {
              const colors = [
                'rgb(211, 76, 70)',
                'rgb(207, 113, 53)',
                'rgb(206, 160, 70)',
                'rgb(87, 159, 110)',
                'rgb(87, 159, 110)'
              ]
              return colors[params.dataIndex]
            }
          },
          barWidth: '60%'
        }]
      }

      this.scoreChart.setOption(option)
    },
    initKnowledgeChart() {
      const chartElement = document.getElementById('knowledgeChart')
      if (!chartElement) {
        return
      }

      const echarts = require('echarts')

      if (this.knowledgeChart) {
        this.knowledgeChart.dispose()
      }

      this.knowledgeChart = echarts.init(chartElement)

      const isVerySmall = window.innerWidth <= 320
      const isMobile = window.innerWidth <= 480
      const isTablet = window.innerWidth <= 768

      const option = {
        title: {
          show: false
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c}%'
        },
        radar: {
          indicator: this.knowledgePoints.slice(0, 6).map(item => ({
            name: item.knowledge,
            max: 100
          })),
          shape: 'polygon',
          splitNumber: 4,
          center: ['50%', '50%'],
          radius: isVerySmall ? '50%' : isMobile ? '55%' : isTablet ? '65%' : '70%',
          axisName: {
            color: '#666',
            fontSize: isVerySmall ? 10 : isMobile ? 11 : isTablet ? 12 : 12,
            distance: isVerySmall ? 8 : isMobile ? 10 : isTablet ? 15 : 20,
            fontWeight: 500
          },
          splitLine: {
            lineStyle: {
              color: '#E2E8F0'
            }
          },
          splitArea: {
            show: false
          },
          axisLine: {
            lineStyle: {
              color: '#E2E8F0'
            }
          }
        },
        series: [{
          name: '知识点掌握情况',
          type: 'radar',
          data: [{
            value: this.knowledgePoints.slice(0, 6).map(item => item.situation),
            name: '掌握率',
            itemStyle: {
              color: '#5A67D8'
            },
            lineStyle: {
              color: '#5A67D8',
              width: 2
            },
            areaStyle: {
              color: 'rgba(90, 103, 216, 0.2)'
            }
          }]
        }]
      }

      this.knowledgeChart.setOption(option)
    },
    handleExport() {
      try {
        import('xlsx').then(XLSX => {
          const exportData = this.prepareExportData()
          const wb = XLSX.utils.book_new()
          const ws = XLSX.utils.json_to_sheet(exportData.students)
          const colWidths = [
            { wch: 12 },
            { wch: 10 },
            { wch: 8 },
            { wch: 10 },
            { wch: 8 },
            { wch: 8 },
            { wch: 12 }
          ]
          ws['!cols'] = colWidths

          XLSX.utils.book_append_sheet(wb, ws, '学生成绩详情')
          const statsWs = XLSX.utils.json_to_sheet(exportData.statistics)
          XLSX.utils.book_append_sheet(wb, statsWs, '班级统计')
          const fileName = `${this.currentClass}_${this.examTitle}_成绩统计_${this.formatDate(new Date())}.xlsx`
          XLSX.writeFile(wb, fileName)
        }).catch(error => {
          console.log('导入xlsx库失败:', error)
        })
      } catch (error) {
        console.log('导出失败:', error)
      }
    },

    prepareExportData() {
      const students = this.studentsData.map(student => ({
        '学生姓名': student.name,
        '完成状态': student.status,
        '答题数': student.totalQuestions,
        '答对题数': student.correctAnswers === '-' ? '未完成' : student.correctAnswers,
        '正确率': student.correctRate,
        '用时': student.timeSpent,
        '得分': student.score
      }))

      const statistics = [
        { '统计项目': '班级平均分', '数值': this.statisticsData.averageScore, '说明': '学生总平均分' },
        { '统计项目': '学生完成率', '数值': `${this.statisticsData.completionRate}%`, '说明': `${this.statisticsData.completedCount}/${this.statisticsData.totalCount} 学生完成` },
        { '统计项目': '总体正确率', '数值': `${this.statisticsData.correctRate}%`, '说明': '正确答题比例' },
        { '统计项目': '平均用时', '数值': this.statisticsData.averageTime === 0 ? '--' : `${this.statisticsData.averageTime}分钟`, '说明': '平均完成时间' }
      ]

      return { students, statistics }
    },

    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      return `${year}${month}${day}_${hours}${minutes}`
    },
    open(params = {}) {
      this.dialogShow = true

      if (params.digital_homework_id) {
        this.digitalHomeworkId = parseInt(params.digital_homework_id)
      }
      if (params.type) {
        this.examType = params.type
      }

      if (this.digitalHomeworkId) {
        this.loadAllData()
      }
    },

    close() {
      this.dialogShow = false
      this.resetData()
    },

    resetData() {
      // this.loading = false // Loading功能已注释
      this.digitalHomeworkId = null
      this.currentClass = '--'
      this.examTitle = '--'
      this.examType = '--'
      this.statisticsData = {
        averageScore: 0,
        completionRate: 0,
        completedCount: 0,
        totalCount: 0,
        correctRate: 0,
        averageTime: 0
      }
      this.scoreDistribution = {}
      this.knowledgePoints = []
      this.suggestions = []
      this.studentsData = []
    },

    goBack() {
      if (this.dialogShow) {
        this.close()
      } else {
        this.$router.push('/classpro/myDigitalbooks')
      }
    },

    formatTime(seconds) {
      if (seconds === null || seconds === undefined || seconds === 0) {
        return '-'
      }

      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60

      if (minutes === 0) {
        return `${remainingSeconds}秒`
      } else if (remainingSeconds === 0) {
        return `${minutes}分`
      } else {
        return `${minutes}分${remainingSeconds}秒`
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.grade-statistics-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #f5f7fa;
  z-index: 100;
  overflow: auto;
  background-color: #F0F2F5;
  min-height: 100vh;
  width: 100%;
}

.header-bar {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  background: transparent;
  border-bottom: none;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1001;

  .back-btn {
    width: 24px;
    height: 24px;
    cursor: pointer;
    margin-right: 16px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    &:hover {
      background: rgba(255, 255, 255, 1);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }

  .header-title {
    font-size: 16px;
    font-weight: 600;
    color: #2D3748;
  }
}

.page-content {
  width: calc(100vw - 258px);
  max-width: 1022px;
  margin: 0 auto;
  padding: 20px 20px 20px 20px;
  box-sizing: border-box;

  @media (max-width: 1280px) {
    width: calc(100vw - 40px);
    padding: 15px 15px 15px 15px;
  }

  @media (max-width: 768px) {
    width: calc(100vw - 20px);
    padding: 10px 10px 10px 10px;
  }
}

.page-header {
  margin-bottom: 21px;
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  padding-top: 4px;

  .page-title {
    font-size: 18px;
    font-weight: 700;
    color: #2D3748;
    margin: 0;
  }

  .class-info {
    display: flex;
    align-items: center;
    gap: 2px;

    .info-text {
      font-size: 12px;
      color: #718096;
    }

    .info-value {
      font-size: 12px;
      font-weight: 500;
      color: #5A67D8;
    }
  }
}

.overview-section {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 21px;

  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
    gap: 10px;
  }
}

.stats-card {
  background: #FFFFFF;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.05), 0px 1px 2px 0px rgba(0, 0, 0, 0.1);

  .card-title {
    font-size: 10px;
    font-weight: 500;
    color: #718096;
    margin-bottom: 12px;
  }

  .card-content {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .main-value {
      font-size: 24px;
      font-weight: 700;
      color: #2D3748;
      line-height: 1.2;
    }
  }

  .card-subtitle {
    font-size: 12px;
    color: #2D3748;
  }
}

.chart-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 10px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.chart-card {
  background: #FFFFFF;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.05), 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
  min-width: 0;

  width: 100%;
  height: 320px;

  @media (max-width: 768px) {
    height: auto;
  }

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .chart-title {
      font-size: 12px;
      font-weight: 600;
      color: #2D3748;
    }
  }

  .chart-container {
    width: 100%;
    overflow: hidden;

    .chart {
      width: 100%;
      height: 240px;
      min-height: 240px;

      @media (max-width: 768px) {
        height: 450px;
        min-height: 450px;
      }

      @media (max-width: 480px) {
        height: 430px;
        min-height: 430px;
      }

      @media (max-width: 320px) {
        height: 420px;
        min-height: 420px;
      }
    }

    .chart-empty {
      width: 100%;
      height: 240px;
      display: flex;
      align-items: center;
      justify-content: center;

      :deep(.empty) {
        width: 32px !important;
        height: 32px !important;
      }

      :deep(.empty-text) {
        font-size: 12px;
        margin-top: 8px;
      }

      @media (max-width: 768px) {
        height: 450px;
      }

      @media (max-width: 480px) {
        height: 430px;
      }

      @media (max-width: 320px) {
        height: 420px;
      }
    }
  }
}

.suggestions-card {
  background: #FFFFFF;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 10px;
  border-left: 4px solid #5A67D8;

  .suggestions-title {
    font-size: 12px;
    font-weight: 600;
    color: #2D3748;
    margin-bottom: 16px;
  }

  .suggestions-list {
    .suggestion-item {
      font-size: 12px;
      color: #718096;
      line-height: 1.5;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.students-section {
  background: #FFFFFF;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.05), 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
  width: 100%;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 16px;
    border-bottom: 1px solid #E2E8F0;
    margin-bottom: 16px;

    .section-title {
      font-size: 13px;
      font-weight: 600;
      color: #2D3748;
    }

    .actions {
      .export-btn {
        background: #F8F9FA;
        border: 1px solid #E2E8F0;
        color: #718096;
        font-size: 10px;
        padding: 6px 11px;
        border-radius: 4px;

        &:hover {
          background: #E2E8F0;
        }
      }
    }
  }

  .students-table {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;

    .grade-table {
      width: 100%;
      min-width: 750px;

      .el-table__header-wrapper,
      .el-table__body-wrapper {
        width: 100% !important;
      }
    }
  }
}

.student-name {
  display: flex;
  align-items: center;
  gap: 8px;

  .avatar {
    width: 26px;
    height: 26px;
    border-radius: 50%;
    background: #E2E8F0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    color: #5A67D8;
  }
}

.status-badge {
  padding: 2px 8px;
  border-radius: 15px;
  font-size: 9px;
  font-weight: 400;
  display: inline-block;

  &.completed {
    background: rgba(56, 161, 105, 0.1) !important;
    color: #38A169 !important;
  }

  &.incomplete {
    background: rgba(229, 62, 62, 0.1) !important;
    color: #E53E3E !important;
  }
}

.score-badge {
  background: #5A67D8 !important;
  color: #FFFFFF !important;
  padding: 2px 6px;
  border-radius: 9px;
  font-size: 10px;
  font-weight: 500;
  display: inline-block;
}

.students-section :deep(.el-table) {
  width: 100% !important;

  .el-table__header {
    background: #F8F9FA;

    th {
      background: #F8F9FA !important;
      color: #718096;
      font-weight: 600;
      font-size: 10px;
      border-bottom: 1px solid #E2E8F0;
    }
  }

  .el-table__body {
    tr {
      &:hover {
        background: #F8F9FA;
      }

      td {
        border-bottom: 1px solid #E2E8F0;
        color: #2D3748;
        font-size: 12px;
      }
    }
  }
}

@media (max-width: 768px) {
  .students-section {
    padding: 15px;

    .students-table {
      margin: 0 -15px;
      padding: 0 15px;

      .grade-table {
        min-width: 650px;
      }
    }

    :deep(.el-table) {
      .el-table__header th {
        font-size: 9px !important;
        padding: 8px 4px !important;
      }

      .el-table__body td {
        font-size: 11px !important;
        padding: 8px 4px !important;
      }
    }
  }
}

@media (max-width: 480px) {
  .students-section {
    padding: 10px;

    .students-table {
      margin: 0 -10px;
      padding: 0 10px;

      .grade-table {
        min-width: 600px;
      }
    }

    :deep(.el-table) {
      .el-table__header th {
        font-size: 8px !important;
        padding: 6px 2px !important;
      }

      .el-table__body td {
        font-size: 10px !important;
        padding: 6px 2px !important;
      }
    }
  }
}

.suggestions-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60px;

  :deep(.empty) {
    width: 32px !important;
    height: 32px !important;
  }

  :deep(.empty-text) {
    font-size: 12px;
    margin-top: 8px;
  }
}

/* Loading样式已注释 */
/*
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  background: transparent;
}

.loading-box {
  width: 160px;
  height: 160px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

:deep(.custom-loading) {
  .el-loading-spinner {
    .circular {
      stroke: #909399 !important;
    }

    .path {
      stroke: #909399 !important;
    }

    svg {
      color: #909399 !important;
    }

    .el-icon-loading {
      color: #909399 !important;
    }
  }

  .el-loading-text {
    color: #909399 !important;
    font-size: 14px;
    margin-top: 10px;
  }
}
*/
</style>
