<template>
  <div class="choice-item-main">
    <div class="choice-item" :class="{'choice-item-select' : questionData.selected}">
      <div class="title" @click="showAll = !showAll">
        <div class="index-view">{{ indexString }}</div>
        <div class="icon-view">
          <i v-if='!showAll' class="el-icon-arrow-right"></i>
          <i v-else class="el-icon-arrow-down"></i>
        </div>
        <span>
          {{formatterFill(questionData.question)}}
          <span>{{choiceType(questionData)}}</span>
        </span>
      </div>
      <template v-if="showAll">
        <div class="answer-item" v-for="(item, index) in questionData.answerOptionList" :key="index">
          <div class="label">{{getOptionLabel(index)}}</div>
          {{ item.answer }}
        </div>
        <div class="bottom">
          <div class="flex align-center">
            <div class="bottom-tip answer-tip">题目答案</div>
            <div class="bottom-content" style="font-weight: 500">
              <span>{{ getAnswer(questionData) }}</span>
            </div>
          </div>
          <div class="flex align-center">
            <div class="bottom-tip analysis-tip">题目解析</div>
            <div class="bottom-content">
              <span>{{ questionData.analysis || '暂无解析' }}</span>
            </div>
          </div>
          <div class="flex align-center">
            <div class="bottom-tip point-tip">知识点</div>
            <div class="bottom-point">
              <div v-for="(item, index) in questionData.knowledges" :key='index' class="point-item">{{item}}</div>
            </div>
          </div>
        </div>
      </template>
    </div>
    <div class="select-view" @click="handleSelect">
      <i class="el-icon-success" v-show="questionData.selected"></i>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChoiceItem',
  props: {
    indexString: {
      type: String,
      default: '1.'
    },
    questionData: {
      type: Object,
      default: () => ({
        question: '',
        questionType: '',
        answer: '',
        analysis: '',
        answerOptionList: [],
        score: 0,
        knowledges: []
      })
    }
  },
  data() {
    return {
      showAll: false
    }
  },
  methods: {
    choiceType(question) {
      if (question.questionType === 'CHOICE') {
        if (question.answer.split(',').length > 1) {
          return '（多选题）'
        } else {
          return ''
        }
      } else {
        return ''
      }
    },
    formatterFill(item) {
      return item.replace(/\[.*?\]/g, '___')
    },
    getOptionLabel(index) {
      const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
      return letters[index] || ''
    },
    getAnswer(question) {
      if (question.questionType === 'CHOICE' || question.questionType === 'SIMPLE_CHOOSE') {
        const list = question.answer.split(',')
        return question.answerOptionList
          .filter((option, index) => {
            option.index = index
            return list.includes(String(option.id))
          })
          .map(option => this.getOptionLabel(option.index))
          .join(', ')
      } else {
        return question.answer || ''
      }
    },
    handleSelect() {
      this.$emit('select-question', this.questionData.id, this.questionData)
    }
  }
}
</script>

<style scoped lang='scss'>
.choice-item-main{
  display: flex;
  gap: 10px;
  .choice-item{
    width: calc(100% - 40px);
    min-height: 60px;
    background: rgba(246, 246, 246, 1);
    border-radius: 20px;
    padding: 20px 20px 10px 20px;
    box-sizing: border-box;
    .title{
      width: 100%;
      font-size: 18px;
      padding: 0 20px;
      position: relative;
      min-height: 30px;
      box-sizing: border-box;
      position: relative;
      white-space: pre-wrap;
      cursor: pointer;
      .index-view{
        position: absolute;
        left: 0;
        top: 0;
        width: 15px;
        height: 100%;
      }
      .icon-view{
        position: absolute;
        right: 0;
        top: 0;
        height: 100%;
        font-size: 20px;
        font-weight: bold;
      }
    }
    .answer-item{
      width: 100%;
      min-height: 30px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      font-size: 18px;
      margin-top: 8px;
      padding-left: 20px;
      .label{
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f1f1f1;
        border-radius: 50%;
        margin-right: 10px;
        flex-shrink: 0;
      }
      .label-right{
        background-color: rgba(47, 128, 237, 1);
        color: rgba(255, 255, 255, 1);
      }
    }
    .bottom{
      width: 100%;
      margin-top: 20px;
      display: flex;
      flex-direction: column;
      gap: 10px;
      padding: 10px 20px 10px 20px;
      font-size: 16px;
      background: #f1f1f1;
      border-radius: 10px;
      .bottom-tip{
        width: 90px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 5px;
        font-weight: 500;
        flex-shrink: 0;
      }
      .answer-tip{
        background: linear-gradient(90deg, #ABECD6 0%, #FBED96 100%);
      }
      .analysis-tip{
        background: linear-gradient(90deg, #FBC2EB 0%, #A6C1EE 100%);
      }
      .point-tip{
        background: linear-gradient(90deg, #84FAB0 0%, #8FD3F4 100%);
      }
      .bottom-content{
        font-size: 18px;
        margin-left: 10px;
        white-space: pre-wrap;
        line-height: 30px;
        width: 100%;
      }
      .bottom-point{
        font-size: 16px;
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        margin-left: 10px;
        align-items: center;
        .point-item{
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 5px 8px;
          background: rgba(47, 128, 237, 0.17);
          border: 1px solid rgba(47, 128, 237, 1);
          border-radius: 15px;
          color: rgba(47, 128, 237, 1);
          min-width: 50px;
        }
        .point-delete{
          color: rgba(47, 128, 237, 1);
          margin-left: 5px;
          cursor: pointer;
        }
        .point-add{
          color: rgba(47, 128, 237, 1);
          font-size: 16px;
          cursor: pointer;
        }
      }
    }
  }
  .choice-item-select{
    background: rgba(246, 252, 255, 1);
    border: 1px solid rgba(45, 156, 219, 1)
  }
  .select-view{
    width: 30px;
    height: 30px;
    border: 1px solid rgba(166, 166, 166, 1);
    border-radius: 50%;
    margin-top: 15px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    i{
      font-size: 35px;
      color: rgba(47, 128, 237, 1);
    }
  }
}
</style>
