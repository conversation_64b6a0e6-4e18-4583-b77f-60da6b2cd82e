<template>
  <div class="list-content">
    <div class="list-tab">
      <div class="tab-view" :class="{'tab-select' : activeName === 'MY_QUESTION_BANK'}" @click="handleTab('MY_QUESTION_BANK')">我的题库</div>
      <div class="tab-view" :class="{'tab-select' : activeName === 'DIGITAL_QUESTION_BANK'}" @click="handleTab('DIGITAL_QUESTION_BANK')">教材配套题库</div>
    </div>
    <div class='list-header'>
      <span>题库列表</span>
      <el-button type='primary' size='mini' @click="handleAdd">创建题库</el-button>
    </div>
    <div class="list-view">
      <div class="table-head">
        <div class="thead-box thead-box-title">题库名称</div>
        <div class="thead-box thead-box-time">创建时间</div>
        <div class="thead-box thead-box-operation">操作</div>
      </div>
      <div class="tbody-box">
        <div v-for="item in list" :key="item.id" class="table-body">
          <div class="tbody-box-c tbody-box-title">{{ item.title && item.title !== '' ? item.title : '-' }}</div>
          <div class="tbody-box-c tbody-box-time">{{ formatYYYYMMDDHHmm(item.createdAt) }}</div>
          <div class="tbody-box-c tbody-box-operation">
            <div class="option-btn" v-if="activeName !== 'DIGITAL_QUESTION_BANK'" @click="handlePublish(item)">
              发布
            </div>
            <div class="option-btn" @click='handleEdit(item)'>
              查看
            </div>
            <div class="option-btn" v-if="activeName !== 'DIGITAL_QUESTION_BANK'" @click="handleDelete(item)">
              删除
            </div>
          </div>
        </div>
        <Empty v-if="list && list.length === 0" :image-size='150'  :image="empty3" description="暂无数据" />
      </div>
    </div>
    <AddExercise ref='addRef' :book-id='bookId' :course-id='courseId' @closeAddExercise='getList'/>
    <CreateBank
      ref="createBankRef"
      :test-paper-name='selectItem.title'
      :book-id='bookId'
      :test-id='selectItem.id'
      :course-id='courseId'
      :total-score='totalScore'
      @publishSuccess='publishSuccess'
    />
  </div>
</template>

<script>
import CreateBank from '@/views/digitalbooks/questionBank/createBank'
import AddExercise from '@/views/digitalbooks/editor/components/addExercise/addExercise.vue'
import { formatYYYYMMDDHHmm } from '@/utils/time.js'
import empty3 from '@/assets/images/empty3.png'
import { Empty } from 'element-ui'
import { getQuestionBankList } from '@/api/digital-api'
import { createTest, getTestPaperQuestionList } from '@/api/test-api'
export default {
  name: 'ManageList',
  components: { Empty, AddExercise, CreateBank },
  props: {
    bookId: {
      type: Number,
      default: 0
    },
    courseId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      activeName: 'MY_QUESTION_BANK', // MY_QUESTION_BANK:我的题库，DIGITAL_QUESTION_BANK:教材配套题库
      formatYYYYMMDDHHmm,
      empty3,
      list: [],
      selectItem: {},
      totalScore: 0
    }
  },
  methods: {
    handleAdd() {
      this.$refs.addRef.open({}, 'create-bank')
    },
    handleTab(type) {
      this.activeName = type
      this.getList()
    },
    async getList() {
      const { data } = await getQuestionBankList({
        pageNo: 1,
        pageSize: 2000,
        referLinkSourceId: this.bookId,
        questionBankType: this.activeName
      })
      this.list = data.content
    },
    handleDelete(item) {
      this.$confirm('确定删除该题库？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        await createTest({
          testPaperId: item.id,
          apiType: 'update',
          collected: false
        })
        this.$message.success('删除成功')
        await this.getList()
      })
    },
    handleEdit(item) {
      this.$refs.addRef.setData(item, 'create-bank')
      this.$refs.addRef.open({}, 'create-bank', item.status !== 'ACTIVE', this.activeName === 'MY_QUESTION_BANK')
    },
    async handlePublish(item) {
      this.selectItem = item
      const { data } = await getTestPaperQuestionList({ testPaperId: Number(item.id) })
      this.totalScore = data.questionList.reduce((total, cur) => total + cur.score, 0)
      this.$nextTick(() => {
        this.$refs.createBankRef.open()
      })
    },
    publishSuccess() {
      this.$refs.createBankRef.close()
      this.getList()
    }
  }
}
</script>

<style scoped lang='scss'>
.list-content{
  width: 100%;
  height: 100%;
  padding: 10px 20px;
  .list-tab{
    width: 100%;
    height: 30px;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 80px;
    padding-left: 30%;
    padding-right: 40%;
    .tab-view{
      cursor: pointer;
      height: 25px;
      display: flex;
      align-items: center;
    }
    .tab-select{
      color: #2F80ED;
      border-bottom: 4px solid #2F80ED;
    }
  }
  .list-header{
    width: 100%;
    height: 25px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .list-view{
    width: 100%;
    height: calc(100% - 40px - 40px);
    margin-top: 15px;
    .table-head {
      background: #E3EFFF;
      height: 40px;
      width: 100%;
      display: flex;
      .thead-box{
        height: 100%;
        display: flex;
        align-items: center;
        color: #2F80ED;
        font-size: var(--font-size-L);
        padding: 0 5px;
        justify-content: center;
        width: 20%;
      }
      .thead-box-title {
        width: 40%;
        justify-content: flex-start;
      }
      .thead-box-time {
        width: 30%;
      }
      .thead-box-operation{
        width: 30%;
      }
    }
    .tbody-box {
      width: 100%;
      height: calc(100% - 40px);
      overflow-y: auto;
      @include scrollBar;
    }
    .table-body {
      min-height: 40px;
      width: 100%;
      display: flex;
      &:hover{
        background: #F5F8FF;
      }
      .tbody-box-c{
        display: flex;
        align-items: center;
        color: #333;
        font-size: var(--font-size-L);
        flex-wrap: wrap;
        line-height: 40px;
        padding: 0 5px;
        justify-content: center;
        width: 20%;
        .option-btn {
          color: #2F80ED;
          text-decoration-line: underline;
          cursor: pointer;
          margin-right: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .tbody-box-title {
        width: 40%;
        justify-content: flex-start;
      }
      .tbody-box-time{
        width: 30%;
      }
      .tbody-box-operation{
        width: 30%;
      }
    }
  }
}
</style>
