<template>
  <div class="list-content">
    <div class='list-header'>
      <span>任务列表</span>
    </div>
    <div class="list-view">
      <div class="table-head">
        <div class="thead-box thead-box-title">标题</div>
        <div class="thead-box" >发布时间</div>
        <div class="thead-box">截止时间</div>
        <div class="thead-box thead-box-small">
          <el-popover
            ref='popover1'
            placement='top'
            width='120'
            trigger='click'>
            <div class="popover-view">
              <div class="popover-item" :class="{'popover-item-select' : selectHomeworkType === 'ALL'}" @click="handleSelectHomeworkType('ALL')">全部</div>
              <div class="popover-item" :class="{'popover-item-select' : selectHomeworkType === 'TEACHING_EXAM'}" @click="handleSelectHomeworkType('TEACHING_EXAM')">考试</div>
              <div class="popover-item" :class="{'popover-item-select' : selectHomeworkType === 'TEACHING_PRACTICE'}" @click="handleSelectHomeworkType('TEACHING_PRACTICE')">练习</div>
            </div>
            <div slot='reference'>
              类型
              <i class="el-icon-caret-bottom" style="cursor: pointer"></i>
            </div>
          </el-popover>
        </div>
        <div class="thead-box thead-box-small">发布老师</div>
        <div class="thead-box thead-box-small">
          <el-popover
            ref='popover2'
            placement='top'
            width='120'
            trigger='click'>
            <div class="popover-view">
              <div class="popover-item" :class="{'popover-item-select' : selectStatus === 'ALL'}" @click="handleSelectStatus('ALL')">全部</div>
              <div class="popover-item" :class="{'popover-item-select' : selectStatus === 'FINISH'}" @click="handleSelectStatus('FINISH')">已完成</div>
              <div class="popover-item" :class="{'popover-item-select' : selectStatus === 'NOFINISH'}" @click="handleSelectStatus('NOFINISH')">未完成</div>
            </div>
            <div slot='reference'>
              状态
              <i class="el-icon-caret-bottom" style="cursor: pointer"></i>
            </div>
          </el-popover>
        </div>
        <div class="thead-box thead-box-small">操作</div>
      </div>
      <div class="tbody-box">
        <div v-for="item in list" :key="item.id" class="table-body">
          <div class="tbody-box-c tbody-box-title">{{ item.title && item.title !== '' ? item.title : '-' }}</div>
          <div class="tbody-box-c">{{ formatYYYYMMDDHHmm(item.createdAt) }}</div>
          <div class="tbody-box-c">{{ item.endTime ? formatYYYYMMDDHHmm(item.endTime) : '-' }}</div>
          <div class="tbody-box-c tbody-box-small">
            {{item.digitalHomeworkType === 'TEACHING_EXAM' ? '考试' : '练习'}}
          </div>
          <div class="tbody-box-c tbody-box-small">
            {{ item.teacher ? item.teacher.displayName : '-' }}
          </div>
          <div class="tbody-box-c tbody-box-small">
            <span v-if="item.userDigitalHomework" style='color:rgba(39, 174, 96, 1);'>已完成</span>
            <span v-else style='color:rgba(235, 87, 87, 1);'>未完成</span>
          </div>
          <div class="tbody-box-c tbody-box-small">
            <div v-if="item.userDigitalHomework"  class="option-btn" @click="handleCheck(item)">
              查看
            </div>
            <div v-else  class="option-btn" @click="handleStart(item)">
              开始答题
            </div>
          </div>
        </div>
        <Empty v-if="list && list.length === 0" :image-size='150'  :image="empty3" description="暂无数据" />
      </div>
    </div>
    <doExercise ref="doExerciseRef" :course-info='selectCourse' @close="getList"/>
  </div>
</template>

<script>
import DoExercise from '@/views/digitalbooks/read/doExercise/doExercise'
import { formatYYYYMMDDHHmm } from 'utils/time'
import empty3 from '@/assets/images/empty3.png'
import { Empty } from 'element-ui'
import { getDigitalHomeworkList } from '@/api/digital-api'
export default {
  name: 'TaskList',
  components: { Empty, DoExercise },
  props: {
    bookId: {
      type: Number,
      default: 0
    },
    courseId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      formatYYYYMMDDHHmm,
      empty3,
      list: [],
      originalList: [],
      selectItem: {},
      selectHomeworkType: 'ALL',
      selectStatus: 'ALL',
      selectCourse: null
    }
  },
  watch: {
    originalList: {
      handler() {
        // 获取未完成数量
        const unfinishedCount = this.originalList.filter(item => !item.userDigitalHomework).length
        this.$emit('updateUnfinishedCount', unfinishedCount)
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    async getList() {
      const { data } = await getDigitalHomeworkList({
        pageNo: 1,
        pageSize: 2000,
        studentCourseId: this.courseId,
        digitalHomeworkTypes: 'TEACHING_EXAM,TEACHING_PRACTICE',
        status: 'ACTIVE'
      })
      this.originalList = data.content
      this.list = data.content
    },
    handleSelectHomeworkType(type) {
      this.selectHomeworkType = type
      this.$refs.popover1.doClose()
      this.list = this.originalList.filter(item => {
        if (type === 'ALL') {
          return true
        } else {
          return item.digitalHomeworkType === type
        }
      })
    },
    handleSelectStatus(type) {
      this.selectStatus = type
      this.$refs.popover2.doClose()
      this.list = this.originalList.filter(item => {
        if (type === 'ALL') {
          return true
        } else {
          return type === 'FINISH' ? item.userDigitalHomework : !item.userDigitalHomework
        }
      })
    },
    handleStart(item) {
      // 判断当前时间是否超过截止时间
      if (item.endTime && new Date().getTime() > new Date(item.endTime).getTime()) {
        this.$message.warning('答题时间已过，不能再进行答题')
        return
      }
      this.selectCourse = item
      this.$refs.doExerciseRef.open(item.sourceId, null, item.studentCourseId, item, 'task')
    },
    handleCheck(item) {
      this.selectCourse = item
      this.$refs.doExerciseRef.open(item.sourceId, null, item.studentCourseId, item, 'task-check')
    }
  }
}
</script>

<style scoped lang='scss'>
.el-table .el-table__header-wrapper .el-table__header colgroup col.custom-column,
.el-table .el-table__body-wrapper .el-table__body colgroup col.custom-column {
  width: 200px !important;
  min-width: 200px !important;
}
.popover-view{
  display:flex;
  flex-direction:column;
  gap: 10px;
  width: 100%;
  padding: 2px;
  .popover-item{
    width: 100%;
    height: 25px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    &:hover{
      background: #E3EFFF;
      color: #2F80ED;
    }
  }
  .popover-item-select{
    background: #2F80ED;
    color: white;
  }
}
.list-content{
  width: 100%;
  height: 100%;
  padding: 10px 20px;
  .list-header{
    width: 100%;
    height: 25px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .list-view{
    width: 100%;
    height: calc(100% - 40px);
    margin-top: 10px;
    .table-head {
      background: #E3EFFF;
      height: 40px;
      width: 100%;
      display: flex;
      .thead-box{
        height: 100%;
        display: flex;
        align-items: center;
        color: #2F80ED;
        font-size: var(--font-size-L);
        padding: 0 5px;
        justify-content: center;
        width: 20%;
      }
      .thead-box-title {
        width: 20%;
        justify-content: flex-start;
      }
      .thead-box-middle{
        width: 15%;
      }
      .thead-box-small{
        width: 10%;
      }
      .thead-box-operation{
        width: 20%;
      }
    }
    .tbody-box {
      width: 100%;
      height: calc(100% - 40px);
      overflow-y: auto;
      @include scrollBar;
    }
    .table-body {
      min-height: 40px;
      width: 100%;
      display: flex;
      &:hover{
        background: #F5F8FF;
      }
      .tbody-box-c{
        display: flex;
        align-items: center;
        color: #333;
        font-size: var(--font-size-L);
        flex-wrap: wrap;
        line-height: 40px;
        padding: 0 5px;
        justify-content: center;
        width: 20%;
        .option-btn {
          color: #2F80ED;
          text-decoration-line: underline;
          cursor: pointer;
          margin-right: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .tbody-box-title {
        width: 20%;
        justify-content: flex-start;
      }
      .tbody-box-middle{
        width: 15%;
      }
      .tbody-box-small{
        width: 10%;
      }
      .tbody-box-operation{
        width: 20%;
      }
    }
  }
  ::v-deep .el-table__cell{
    height: 30px;
    border-bottom: 0 !important;
  }
  ::v-deep .el-table::before{
    height: 0;
  }
}
</style>
