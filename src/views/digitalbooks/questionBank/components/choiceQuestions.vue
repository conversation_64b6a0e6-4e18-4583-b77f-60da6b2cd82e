<template>
  <div class="no-conversion" v-show="show">
    <div class="choice-main">
      <div class="header">
        <div class="title">
          选择题库
        </div>
        <div class="tab">
          <div class="tab-view" :class="{'tab-select' : activeName === 'MY_QUESTION_BANK'}" @click="handleTab('MY_QUESTION_BANK')">我的题库</div>
          <div class="tab-view" :class="{'tab-select' : activeName === 'DIGITAL_QUESTION_BANK'}" @click="handleTab('DIGITAL_QUESTION_BANK')">教材配套题库</div>
        </div>
        <div class="operation">
          <span>共选择：{{selectList.length}}道题</span>
          <div class="btn-view" @click="handleChoice">选择</div>
          <i class="el-icon-close" @click='close'></i>
        </div>
      </div>
      <div class="main-content">
        <div class="left-content">
          <div v-for="item in paperList" :key="item.testPaperId" @click="handleSelectPaper(item)" class='menu-item' :class="{'menu-item-select' : item.selected}">
            {{ item.testPaperName && item.testPaperName !== '' ? item.testPaperName : '-' }}
            <div v-if="item.selectNum > 0" class="num-view">{{ item.selectNum }}</div>
          </div>
        </div>
        <div class="right-content">
          <div v-if="questionList.length === 0" class="flex justify-center align-center empty-view">
            <Empty :image="empty3" description="暂无数据" />
          </div>
          <div class="choice-all" @click="handleSelectAll">
            全选
            <div class="select-view">
              <i class="el-icon-success" v-show="selectAll"></i>
            </div>
          </div>
          <div class="content-title" v-if='choiceList.length > 0'>
            选择题
          </div>
          <ChoiceItem
            v-for='(item, index) in choiceList'
            :key="item.id"
            :question-data="item"
            :index-string="`${index + 1}.`"
            @select-question="handleSelectQuestion"
          />
          <div class="content-title" v-if='fillList.length > 0'>
            填空题
          </div>
          <ChoiceItem
            v-for='(item, index) in fillList'
            :key="item.id"
            :question-data="item"
            :index-string="`${index + 1}.`"
            @select-question="handleSelectQuestion"
          />
          <div class="content-title" v-if='simpleList.length > 0'>
            判断题
          </div>
          <ChoiceItem
            v-for='(item, index) in simpleList'
            :key="item.id"
            :question-data="item"
            :index-string="`${index + 1}.`"
            @select-question="handleSelectQuestion"
          />
          <div class="content-title" v-if='essayList.length > 0'>
            简答题
          </div>
          <ChoiceItem
            v-for='(item, index) in essayList'
            :key="item.id"
            :question-data="item"
            :index-string="`${index + 1}.`"
            @select-question="handleSelectQuestion"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import empty3 from '@/assets/images/empty3.png'
import { Empty } from 'element-ui'
import ChoiceItem from '@/views/digitalbooks/questionBank/components/choiceItem'
import { getTestPaperQuestionList } from '@/api/test-api'
import { batchQuestion, getQuestionBankList } from '@/api/digital-api'

export default {
  name: 'ChoiceQuestions',
  components: {
    ChoiceItem,
    Empty
  },
  props: {
    bookId: {
      type: Number,
      default: 0
    },
    testId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      empty3,
      show: false,
      selectItem: null,
      activeName: 'MY_QUESTION_BANK', // MY_QUESTION_BANK:我的题库，DIGITAL_QUESTION_BANK:教材配套题库
      questionList: [],
      choiceList: [],
      fillList: [],
      simpleList: [],
      essayList: [],
      selectList: [],
      selectQuestionList: [],
      paperList: [],
      selectPaper: null,
      selectAll: false
    }
  },
  watch: {
    selectList: {
      handler(val) {
        this.choiceList.forEach(item => {
          item.selected = val.includes(item.id)
        })
        this.fillList.forEach(item => {
          item.selected = val.includes(item.id)
        })
        this.simpleList.forEach(item => {
          item.selected = val.includes(item.id)
        })
        this.essayList.forEach(item => {
          item.selected = val.includes(item.id)
        })
        this.questionList.forEach(item => {
          item.selected = val.includes(item.id)
        })
        this.paperList.forEach(paper => {
          if (paper.testPaperId === this.selectPaper.testPaperId) {
            paper.selectNum = this.questionList.filter(item => item.selected).length
          }
        })
      },
      deep: true
    },
    questionList: {
      handler(val) {
        if (val.length === 0) {
          this.selectAll = false
        } else {
          this.selectAll = val.every(item => this.selectList.includes(item.id))
        }
      },
      deep: true
    }
  },
  methods: {
    open() {
      this.show = true
      this.getTestPaper()
      // this.getExerciseData()
    },
    close() {
      this.initData()
      this.show = false
    },
    initData() {
      this.selectItem = null
      this.activeName = 'MY_QUESTION_BANK'
      this.questionList = []
      this.choiceList = []
      this.fillList = []
      this.simpleList = []
      this.essayList = []
      this.selectList = []
      this.selectQuestionList = []
      this.paperList = []
      this.selectPaper = null
      this.selectAll = false
    },
    handleTab(type) {
      this.activeName = type
      this.getTestPaper()
    },
    async getTestPaper() {
      const { data } = await getQuestionBankList({
        pageNo: 1,
        pageSize: 2000,
        referLinkSourceId: this.bookId,
        questionBankType: this.activeName
      })
      this.paperList = data.content.map(item => ({
        ...item,
        testPaperId: item.id,
        testPaperName: item.title,
        selected: false,
        selectNum: 0
      }))
      if (this.paperList.length > 0) {
        this.paperList[0].selected = true
        this.selectPaper = this.paperList[0]
        await this.getExerciseData()
      }
    },
    handleSelectPaper(item) {
      this.paperList.forEach(paper => {
        paper.selected = false
      })
      item.selected = true
      this.selectPaper = item
      this.getExerciseData()
    },
    async getExerciseData() {
      try {
        const { data } = await getTestPaperQuestionList({ testPaperId: this.selectPaper.testPaperId })
        // debugger
        this.userTestpaper = data.userTestpaper
        if (this.userTestpaper && this.userTestpaper.progressStatus === 'FINISHED') {
          this.testPaperType = 1
        }
        this.questionList = data.questionList.map((item) => {
          item.selected = this.selectList.includes(item.id)
          return item
        })
        this.paperList.forEach(paper => {
          if (paper.testPaperId === this.selectPaper.testPaperId) {
            paper.selectNum = this.questionList.filter(item => item.selected).length
          }
        })
        this.choiceList = this.questionList.filter((item) => { return item.questionType === 'CHOICE' })
        this.fillList = this.questionList.filter((item) => { return item.questionType === 'FILL_IN_THE_BLANK_INPUT' })
        this.simpleList = this.questionList.filter((item) => { return item.questionType === 'SIMPLE_CHOOSE' })
        this.essayList = this.questionList.filter((item) => { return item.questionType === 'ESSAY_QUESTION' })
      } catch (e) {
        this.$message.error('获取试题数据失败')
      }
    },
    handleSelectQuestion(id, info) {
      if (this.selectList.includes(id)) {
        this.selectList = this.selectList.filter(item => item !== id)
        this.selectQuestionList = this.selectQuestionList.filter(item => item.id !== id)
      } else {
        this.selectList.push(id)
        this.selectQuestionList.push(info)
      }
    },
    handleSelectAll() {
      if (this.selectAll) {
        this.selectList = this.selectList.filter(id => !this.questionList.some(item => item.id === id))
        this.selectQuestionList = this.selectQuestionList.filter(item => !this.questionList.some(q => q.id === item.id))
        this.selectAll = false
      } else {
        const newSelections = this.questionList.map(item => item.id).filter(id => !this.selectList.includes(id))
        const newSelectionDetails = this.questionList.filter(item => newSelections.includes(item.id))
        this.selectList = this.selectList.concat(newSelections)
        this.selectQuestionList = this.selectQuestionList.concat(newSelectionDetails)
        this.selectAll = true
      }
    },
    async handleChoice() {
      if (this.selectList.length === 0) {
        this.$message.warning('请至少选择一道题')
        return
      }
      await batchQuestion({
        id: this.testId,
        questionList: this.selectQuestionList.map(item => {
          delete item.id
          if (item.answerOptionList) {
            item.answerOptionList.map(option => { delete option.id; return option })
          }
          return item
        })
      }, {
        overwrite: false
      })
      this.$emit('choice-success')
      await this.close()
    }
  }
}
</script>

<style scoped lang='scss'>
.no-conversion{
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 101;
  display: flex;
  align-items: end;
  justify-content: center;
  .choice-main{
    width: 100vw;
    height: 95%;
    background: white;
    border-radius: 10px 10px 0 0 ;
    padding: 20px 10px;
    .header{
      width: 100%;
      height: 40px;
      display: flex;
      align-items: center;
      .title{
        font-size: 20px;
        font-weight: bold;
        width: 100px;
        height: 100%;
        display: flex;
        align-items: center;
        padding-left: 10px;
      }
      .tab{
        font-size: 14px;
        height: 100%;
        width: calc(100% - 400px);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        gap: 100px;
        .tab-view{
          cursor: pointer;
          height: 40px;
          display: flex;
          align-items: center;
          flex-shrink: 0;
        }
        .tab-select{
          color: #2F80ED;
          border-bottom: 4px solid #2F80ED;
        }
      }
      .operation{
        color: #2F80ED;
        width: 300px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: rgba(79, 79, 79, 1);
        font-size: 16px;
        padding-right: 10px;
        .btn-view{
          height: 40px;
          padding: 0 20px;
          border-radius: 5px;
          background: #2F80ED;
          color: white;
          cursor: pointer;
          display: flex;
          align-items: center;
          &:hover{
            background: #1565C0;
        }
        }
        i{
          color: black;
          font-size: 20px;
          cursor: pointer;
        }
      }
    }
    .main-content{
      width: 100%;
      height: calc(100% - 60px);
      margin-top: 20px;
      display: flex;
      .left-content{
        width: 300px;
        flex-shrink: 0;
        height: 100%;
        overflow-y: auto;
        border-right: 1px solid rgba(224, 224, 224, 1);
        padding-left: 10px;
        padding-right: 20px;
        padding-top: 20px;
        display: flex;
        flex-direction: column;
        gap: 20px;
        .menu-item{
          width: 100%;
          height: 50px;
          flex-shrink: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 18px;
          cursor: pointer;
          position: relative;
          &:hover{
            background: rgba(242, 242, 242, 1);
          }
          .num-view{
            position: absolute;
            right: -15px;
            top: -15px;
            width: 30px;
            height: 30px;
            background: rgba(47, 128, 237, 1);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            flex-shrink: 0;
          }
        }
        .menu-item-select{
          background: rgba(47, 128, 237, 1);
          color: white;
          &:hover{
            background: rgba(47, 128, 237, 1);
          }
        }
      }
      .right-content{
        height: 100%;
        width: calc(100% - 300px);
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        gap: 15px;
        padding: 10px 25px;
        position: relative;
        .choice-all{
          position: absolute;
          right: 20px;
          font-size: 18px;
          cursor: pointer;
          height: 40px;
          display: flex;
          align-items: center;
          padding: 0 5px;
          background: white;
          .select-view{
            width: 30px;
            height: 30px;
            border: 1px solid rgba(166, 166, 166, 1);
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
            margin-left: 10px;
            i{
              font-size: 35px;
              color: rgba(47, 128, 237, 1);
            }
          }
        }
        .empty-view{
          width: 100%;
          height: 100%;
        }
        .content-title{
          font-size: 18px;
          font-weight: 500;
          height: 40px;
          width: 80px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: rgba(47, 128, 237, 1);
          background: rgba(230, 240, 255, 1);
          border-radius: 20px;
          flex-shrink: 0;
        }
      }
    }
  }
}
</style>
