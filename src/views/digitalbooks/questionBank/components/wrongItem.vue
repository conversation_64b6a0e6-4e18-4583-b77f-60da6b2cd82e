<template>
  <div class="wrong-item" v-show='show'>
    <div class='wrong-item-main' v-if='questionData'>
      <div class="header">
        <div class="type-name" v-if="questionData">
          {{ questionData.questionType === 'CHOICE' ? '选择题' :
          questionData.questionType === 'FILL_IN_THE_BLANK_INPUT' ? '填空题' :
            questionData.questionType === 'SIMPLE_CHOOSE' ? '判断题' :
              questionData.questionType === 'ESSAY_QUESTION' ? '简答题' :
                questionData.questionType || '未知类型'}}
        </div>
      </div>
      <div class="content">
        <div class="title">
          <Fill
            v-if="sourceType === 'do' && questionData.questionType === 'FILL_IN_THE_BLANK_INPUT'"
            :test-paper-type="testPaperType"
            :original-string="questionData"
            :show-index="false"
            :show-score="false"
            @inputDown="submitFill"
          />
          <span v-else>
            {{formatterFill(questionData.question)}}
            <span>{{choiceType(questionData)}}</span>
          </span>
        </div>
        <template v-if="sourceType === 'check'">
          <div class="answer-item" v-for="(item, index) in questionData.answerOptionList" :key="index">
            <div class="label">{{getOptionLabel(index)}}</div>
            {{ item.answer }}
          </div>
        </template>
        <template v-if="sourceType === 'do'">
          <div class="answer-item" style="cursor: pointer" v-for="(item, index) in questionData.answerOptionList" :key="index" @click='choiceAnswer(questionData,item)'>
            <div class="label" :class="{'label-select' : isSelected(item), 'label-wrong' : testPaperType === 1 && isWrong(item), 'label-right' : testPaperType === 1 && isRight(item)}">{{getOptionLabel(index)}}</div>
            <span :class="{'text-wrong' : testPaperType === 1 && isWrong(item)}">{{ item.answer }}</span>
          </div>
        </template>
        <el-input
          v-if="testPaperType===0 && questionData.questionType === 'ESSAY_QUESTION' && sourceType === 'do'"
          v-model="essayAnswer"
          class="essay_input"
          type="textarea"
          maxlength="500"
          :rows="5"
          show-word-limit
          @blur="submitEssay(questionData)"
        />
        <div class="essay_answer" :class="{'essay_answer_wrong' : isWrong(null)}" v-if="testPaperType===1 && questionData.questionType === 'ESSAY_QUESTION' && questionData.answerUser">
          {{ questionData.answerUser && questionData.answerUser.answerIds }}
        </div>
      </div>
      <div class="bottom" v-if="sourceType === 'check' || testPaperType === 1">
        <div class="flex align-center">
          <div class="bottom-tip answer-tip">{{ questionData.questionType !== 'ESSAY_QUESTION'?`题目答案`:'参考答案'}}</div>
          <div class="bottom-content" style="font-weight: 500">
            <span v-if="questionData.questionType !== 'ESSAY_QUESTION'">{{ getAnswer(questionData) }}</span>
            <span v-else>{{ questionData.answer }}</span>
          </div>
        </div>
        <div class="flex align-center">
          <div class="bottom-tip analysis-tip">题目解析</div>
          <div class="bottom-content">
            <span>{{ questionData.analysis || '暂无解析' }}</span>
          </div>
        </div>
        <div class="flex align-center">
          <div class="bottom-tip point-tip">知识点</div>
          <div class="bottom-point">
            <div v-for="(item, index) in questionData.knowledges" :key='index' class="point-item">{{item}}</div>
          </div>
        </div>
      </div>
      <div class="operation">
        <div class="save-btn" @click="handleNext">下一道</div>
        <div class="save-btn" v-if="sourceType === 'do'" @click="handleConfirm">提交</div>
        <div class="cancel-btn" @click='close'>取消</div>
      </div>
    </div>
  </div>
</template>

<script>
import Fill from '@/views/digitalbooks/editor/components/fill'
import { getUserErrorQuestionList, userErrorQuestion } from '@/api/digital-api'
import { answerQuestion } from '@/api/test-api'
import router from '@/router'

export default {
  name: 'WrongItem',
  components: { Fill },
  props: {
    questionDataProps: {
      type: Object,
      default: () => ({
        question: '',
        questionType: '',
        answer: '',
        analysis: '',
        answerOptionList: [],
        score: 0,
        knowledges: []
      })
    },
    courseId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      show: false,
      sourceType: 'check',
      tempList: [],
      pageNo: 1,
      pageSize: 20,
      questionList: [],
      hasMore: true,
      questionData: null,
      testPaperType: 0,
      essayAnswer: ''
    }
  },
  watch: {
    questionDataProps: {
      handler(newVal) {
        if (newVal) {
          this.questionData = JSON.parse(JSON.stringify(newVal))
        }
      },
      immediate: true,
      deep: true
    },
    questionData: {
      handler(newVal) {
        if (newVal) {
          this.essayAnswer = newVal.answerUser ? newVal.answerUser.answerIds || '' : ''
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    choiceType(question) {
      if (question && question.questionType === 'CHOICE') {
        if (question.answer.split(',').length > 1) {
          return '（多选题）'
        } else {
          return ''
        }
      } else {
        return ''
      }
    },
    formatterFill(item) {
      return item.replace(/\[.*?\]/g, '___')
    },
    getOptionLabel(index) {
      const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
      return letters[index] || ''
    },
    getAnswer(question) {
      if (question) {
        if (question.questionType === 'CHOICE' || question.questionType === 'SIMPLE_CHOOSE') {
          const list = question.answer.split(',')
          return question.answerOptionList
            .filter((option, index) => {
              option.index = index
              return list.includes(String(option.id))
            })
            .map(option => this.getOptionLabel(option.index))
            .join(', ')
        } else {
          return question.answer || ''
        }
      } else {
        return ''
      }
    },
    open(type = 'check') {
      this.sourceType = type
      if (type === 'do') {
        this.getList()
      } else {
        this.questionData = JSON.parse(JSON.stringify(this.questionDataProps))
      }
      this.show = true
    },
    close() {
      this.initData()
      this.show = false
      this.$emit('close')
    },
    initData() {
      this.tempList = []
      this.pageNo = 1
      this.pageSize = 20
      this.questionList = []
      this.hasMore = true
      this.questionData = null
      this.testPaperType = 0
      this.essayAnswer = ''
    },
    async handleConfirm(refresh = true) {
      if (!this.questionData.answerUser) {
        if (refresh) {
          // 延迟两秒提交
          const timer = setTimeout(() => {
            this.handleConfirm(false)
            clearTimeout(timer)
          }, 100)
          // this.handleConfirm(false)
        } else {
          this.$message.warning('请先作答')
        }
      } else {
        if (this.questionData.answerUser.result === 'CORRECT') {
          await userErrorQuestion({
            questionId: this.questionData.id,
            courseId: this.courseId,
            status: 'INACTIVE'
          })
        }
        this.testPaperType = 1
      }
    },
    handleNext() {
      if (this.sourceType === 'check') {
        this.$emit('next')
      } else {
        if (this.questionList.length > this.tempList.length) {
          this.testPaperType = 0
          this.essayAnswer = ''
          this.questionData = this.getRandomQuestion()
        } else if (this.hasMore) {
          this.pageNo += 1
          this.getList()
        } else {
          this.$message.warning('没有更多题目了')
        }
      }
    },
    // 随机从题目列表中抽取一道题，并加入临时列表保证不重复
    getRandomQuestion() {
      if (this.questionList.length === 0) {
        return null
      }
      let randomIndex
      let randomQuestion
      do {
        randomIndex = Math.floor(Math.random() * this.questionList.length)
        randomQuestion = this.questionList[randomIndex]
      } while (this.tempList.includes(randomQuestion))
      this.tempList.push(randomQuestion)
      return randomQuestion
    },
    async getList() {
      const { data } = await getUserErrorQuestionList({
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        status: 'ACTIVE',
        courseId: this.courseId
      })
      const questionList = data.content.map(item => {
        return {
          wrongTestPaperId: item.id,
          courseId: item.courseId,
          ...item.question
        }
      })
      if (data && data.content && data.content.length > 0) {
        this.questionList = [...this.questionList, ...questionList]
        this.testPaperType = 0
        this.essayAnswer = ''
        this.questionData = this.getRandomQuestion()
      } else {
        this.hasMore = false
        this.$message.warning('没有更多题目了')
      }
    },
    isSelected(option) {
      let selected = false
      if (this.testPaperType === 0) {
        if (this.questionData.answerUser && this.questionData.answerUser.answerIds.split(',').indexOf(String(option.id)) !== -1) {
          selected = true
        }
      }
      return selected
    },
    isWrong(option) {
      let isWrong = false
      if (!option) {
        if (this.questionData.answerUser && this.questionData.answerUser.result === 'WRONG') {
          isWrong = true
        }
      } else {
        if (this.questionData.answerUser && this.questionData.answerUser.answerIds.split(',').indexOf(String(option.id)) !== -1) {
          if (this.questionData.answerUser && this.questionData.answerUser.result === 'WRONG') {
            isWrong = true
          }
        }
      }
      return isWrong
    },
    isRight(option) {
      let isRight = false
      if (!option) {
        if (this.questionData.answerUser && this.questionData.answerUser.result !== 'WRONG') {
          isRight = true
        }
      } else {
        if (this.questionData.answerUser && this.questionData.answerUser.answerIds.split(',').indexOf(String(option.id)) !== -1) {
          if (this.questionData.answerUser && this.questionData.answerUser.result !== 'WRONG') {
            isRight = true
          }
        }
      }
      return isRight
    },
    choiceAnswer(item, option) {
      if (this.testPaperType !== 0) return
      let arr = item.answerUser && item.answerUser.answerIds ? item.answerUser.answerIds.split(',') : []
      if (arr.indexOf(String(option.id)) !== -1) {
        arr = arr.filter(function(item) {
          return item !== String(option.id)
        })
      } else {
        if (item.questionType === 'SIMPLE_CHOOSE' && arr.length > 0) {
          arr = [] // 判断题只能选择一个答案，清空之前的答案
        }
        arr.push(String(option.id))
      }
      arr = arr.sort((a, b) => {
        const indexA = item.answerOptionList.findIndex(obj => String(obj.id) === a)
        const indexB = item.answerOptionList.findIndex(obj => String(obj.id) === b)
        return indexA - indexB
      })
      let answer = ''
      if (arr.length === 0) {
        answer = ''
      } else if (arr.length === 1) {
        answer = arr[0]
      } else {
        answer = arr.join(',')
      }
      answerQuestion({ questionId: item.id, testPaperId: item.wrongTestPaperId, studentCourseId: item.courseId, answer: answer }, {
        authorization: router.currentRoute.query.token && 'Bearer ' + router.currentRoute.query.token
      }).then(res => {
        if (res && res.data) {
          // this.testPaperType = 1
          this.questionData.answerUser = res.data
        }
      })
    },
    submitFill(data) {
      if (this.testPaperType !== 0) return
      const item = data.item
      const answer = data.arr.join(',')
      answerQuestion({ questionId: item.id, testPaperId: item.wrongTestPaperId, studentCourseId: item.courseId, answer: answer }, {
        authorization: router.currentRoute.query.token && 'Bearer ' + router.currentRoute.query.token
      }).then(res => {
        if (res && res.data) {
          // this.testPaperType = 1
          this.questionData.answerUser = res.data
        }
      })
    },
    submitEssay(item) {
      if (this.testPaperType !== 0) return
      answerQuestion({ questionId: item.id, testPaperId: item.wrongTestPaperId, studentCourseId: item.courseId, answer: this.essayAnswer }, {
        authorization: router.currentRoute.query.token && 'Bearer ' + router.currentRoute.query.token
      }).then(res => {
        if (res && res.data) {
          // this.testPaperType = 1
          this.questionData.answerUser = res.data
        }
      })
    }
  }
}
</script>

<style scoped lang='scss'>
.wrong-item{
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 102;
  display: flex;
  align-items: center;
  justify-content: center;
  .wrong-item-main{
    width: 900px;
    min-height: 100px;
    padding: 20px 20px 10px 20px;
    border-radius: 12px;
    background: white;
    .header{
      width: 100%;
      height: 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .type-name{
        font-size: 16px;
        font-weight: 500;
        color: rgba(47, 128, 237, 1);
        background-color: rgba(230, 240, 255, 1);
        padding: 0 15px;
        border-radius: 15px;
        height: 30px;
        line-height: 30px;
      }
    }
    .content{
      width: 100%;
      margin-top: 10px;
      .title{
        width: 100%;
        font-size: 16px;
        padding: 10px 10px 0 10px;
        position: relative;
        min-height: 25px;
      }
      .essay_answer{
        width: 100%;
        font-size: 14px;
        padding: 15px 20px;
        margin-top: 10px;
        color: rgba(47, 128, 237, 1);
        background-color: rgba(230, 240, 255, 1);
        border-radius: 5px;
        min-height: 50px;
      }
      .essay_answer_wrong{
        color: rgba(235, 87, 87, 1);
        background-color: rgba(255, 235, 235, 1);
      }
      .answer-item{
        width: 100%;
        min-height: 30px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding-left: 10px;
        font-size: 16px;
        margin-top: 10px;
        ::v-deep .el-input__inner{
          padding-right: 50px !important;
        }
        .label{
          width: 30px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: rgba(243, 243, 243, 1);
          border-radius: 50%;
          margin-right: 10px;
          flex-shrink: 0;
        }
        .label-select{
          background-color: rgba(47, 128, 237, 1);
          color: rgba(255, 255, 255, 1);
        }
        .label-right{
          background-color: rgba(39, 174, 96, 1);
          color: rgba(255, 255, 255, 1);
        }
        .label-wrong{
          background-color: rgba(235, 87, 87, 1);
          color: rgba(255, 255, 255, 1);
        }
        .text-wrong{
          color: rgba(235, 87, 87, 1);
        }
        .text-right{
          color: rgba(39, 174, 96, 1);
        }
      }
    }
    .bottom{
      width: 100%;
      margin-top: 20px;
      display: flex;
      flex-direction: column;
      gap: 10px;
      padding: 10px 20px 10px 20px;
      font-size: 12px;
      background: rgba(251, 251, 251, 1);
      border-radius: 10px;
      .bottom-tip{
        width: 70px;
        height: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 5px;
        font-weight: 500;
        flex-shrink: 0;
      }
      .answer-tip{
        background: linear-gradient(90deg, #ABECD6 0%, #FBED96 100%);
      }
      .analysis-tip{
        background: linear-gradient(90deg, #FBC2EB 0%, #A6C1EE 100%);
      }
      .point-tip{
        background: linear-gradient(90deg, #84FAB0 0%, #8FD3F4 100%);
      }
      .bottom-content{
        font-size: 16px;
        margin-left: 10px;
        white-space: pre-wrap;
        line-height: 25px;
        width: 100%;
      }
      .bottom-point{
        font-size: 16px;
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        margin-left: 10px;
        align-items: center;
        .point-item{
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 3px 10px;
          background: rgba(47, 128, 237, 0.17);
          border: 1px solid rgba(47, 128, 237, 1);
          border-radius: 15px;
          color: rgba(47, 128, 237, 1);
          min-width: 50px;
        }
        .point-delete{
          color: rgba(47, 128, 237, 1);
          margin-left: 5px;
          cursor: pointer;
        }
        .point-add{
          color: rgba(47, 128, 237, 1);
          font-size: 16px;
          cursor: pointer;
        }
      }
    }
    .operation{
      width: 100%;
      height: 50px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: 20px;
      margin-top: 20px;
      .save-btn, .cancel-btn{
        width: 100px;
        height: 35px;
        border-radius: 5px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
      }
      .save-btn{
        background: rgba(47, 128, 237, 1);
        color: white;
      }
      .cancel-btn{
        border: 1px solid rgba(47, 128, 237, 1);
        color: rgba(47, 128, 237, 1);
      }
    }
  }
  .essay_input{
    margin-top: 20px;
  }
}
</style>
