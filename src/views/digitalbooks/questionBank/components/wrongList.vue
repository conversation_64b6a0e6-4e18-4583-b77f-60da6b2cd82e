<template>
  <div class="list-content">
    <div class="list-tab">
      <div class="tab-view" :class="{'tab-select' : activeName === 'wrong'}" @click="handleTab('wrong')">未答对</div>
      <div class="tab-view" :class="{'tab-select' : activeName === 'right'}" @click="handleTab('right')">已答对</div>
      <el-button class='btn-view' type='primary' size='mini' @click="handlePractice">错题练习</el-button>
    </div>
    <div class="list-view" @scroll="handleScroll">
      <div v-for="item in list" :key='item.id' class="list-item">
        {{formatterFill(item.question)}}
        <div class="answer-item" v-for="(option, index) in item.answerOptionList" :key="index">
          <div class="label">{{getOptionLabel(index)}}</div>
          {{ option.answer }}
        </div>
        <div class="operation-view">
          <el-button type='text' size='mini' @click="handleCheck(item)">查看</el-button>
          <el-button type='text' size='mini' @click="handleDelete(item)">删除</el-button>
        </div>
      </div>
      <Empty v-if="list && list.length === 0" :image-size='150'  :image="empty3" description="暂无数据" />
    </div>
    <PromptView ref="promptRef" confirm-title='确定' title='确定要删除该题？' @confirm="handleConfirm"/>
    <WrongItem
      v-if='selectItem'
      ref="wrongItemRef"
      :question-data-props="selectItem"
      :course-id="courseId"
      @next="handleNext"
      @close="getList"
    />
  </div>
</template>

<script>
import WrongItem from '@/views/digitalbooks/questionBank/components/wrongItem'
import PromptView from '@/components/classPro/PromptView/PromptView'
import { getUserErrorQuestionList, userErrorQuestion } from '@/api/digital-api'
import empty3 from '@/assets/images/empty3.png'
import { Empty } from 'element-ui'
export default {
  name: 'ManageList',
  components: { PromptView, Empty, WrongItem },
  props: {
    bookId: {
      type: Number,
      default: 0
    },
    courseId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      empty3,
      activeName: 'wrong', // wrong:未答对，right:已答对
      pageNo: 1,
      pageSize: 10,
      list: [],
      loading: false,
      hasMore: true,
      selectItem: null
    }
  },
  methods: {
    formatterFill(item) {
      return item.replace(/\[.*?\]/g, '___')
    },
    getOptionLabel(index) {
      const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
      return letters[index] || ''
    },
    handleTab(type) {
      this.activeName = type
      this.getList()
    },
    initData() {
      this.list = []
      this.pageNo = 1
      this.pageSize = 10
      this.hasMore = true
      this.selectItem = null
    },
    async getList(first = true) {
      if (first) {
        this.initData()
      }
      const { data } = await getUserErrorQuestionList({
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        status: this.activeName === 'wrong' ? 'ACTIVE' : 'INACTIVE',
        courseId: this.courseId
      })
      const questionList = data.content.map(item => {
        return {
          wrongTestPaperId: item.id,
          courseId: item.courseId,
          ...item.question
        }
      })
      if (data && data.content && data.content.length > 0) {
        this.list = [...this.list, ...questionList]
        if (this.pageSize > 10) {
          this.pageNo = this.pageSize / 10
          this.pageSize = 10
        }
      } else {
        this.hasMore = false
      }
    },
    handleScroll(e) {
      const container = e.target
      // 容器可滚动高度
      const scrollHeight = container.scrollHeight
      // 容器可见高度
      const clientHeight = container.clientHeight
      // 已滚动距离
      const scrollTop = container.scrollTop
      // 判断是否滚动到了底部（允许1px的误差）
      const isBottom = scrollTop + clientHeight >= scrollHeight - 1
      // 如果滚动到了底部，并且不在加载中，且还有更多数据
      if (isBottom && !this.loading && this.hasMore) {
        this.pageNo += 1
        this.getList(false)
      }
    },
    handleDelete(item) {
      this.selectItem = item
      this.$refs.promptRef.open()
    },
    async handleConfirm() {
      // 删除逻辑
      await userErrorQuestion({
        id: this.selectItem.wrongTestPaperId,
        status: 'DELETED'
      })
      this.$message.success('删除成功')
      if (this.list.length > 10) {
        this.pageSize = this.list.length
        this.pageNo = 1
      }
      this.list = []
      await this.getList(false)
    },
    handleCheck(item) {
      this.selectItem = item
      this.$nextTick(() => {
        this.$refs.wrongItemRef.open()
      })
    },
    handleNext() {
      const currentIndex = this.list.findIndex(i => i.id === this.selectItem.id)
      if (currentIndex !== -1 && currentIndex < this.list.length - 1) {
        this.selectItem = this.list[currentIndex + 1]
      } else {
        this.$message.info('没有更多题目了')
      }
    },
    handlePractice() {
      if (this.list.length === 0) {
        this.$message.info('暂无错题可练习')
        return
      }
      this.selectItem = {
        question: '',
        questionType: '',
        answer: '',
        analysis: '',
        answerOptionList: [],
        score: 0,
        knowledges: []
      }
      this.$nextTick(() => {
        this.$refs.wrongItemRef.open('do')
      })
    }
  }
}
</script>

<style scoped lang='scss'>
.list-content{
  width: 100%;
  height: 100%;
  padding: 10px 20px;
  .list-tab{
    width: 100%;
    height: 40px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 80px;
    padding-left: 30%;
    padding-right: 40%;
    border-bottom: 1px solid rgba(242, 242, 242, 1);
    position: relative;
    .tab-view{
      cursor: pointer;
      height: 25px;
      display: flex;
      align-items: center;
    }
    .tab-select{
      color: #2F80ED;
      border-bottom: 4px solid #2F80ED;
    }
    .btn-view{
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  .list-view{
    width: 100%;
    height: calc(100% - 50px);
    margin-top: 15px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 15px;
    .list-item{
      flex-shrink: 0;
      width: 100%;
      min-height: 25px;
      background: white;
      border: 1px solid rgba(242, 242, 242, 1);
      border-radius: 10px;
      box-sizing: border-box;
      padding: 15px;
      font-size: 14px;
      position: relative;
      .title{
        width: 100%;
        min-height: 25px;
      }
      .answer-item{
        width: 100%;
        min-height: 25px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding-left: 5px;
        font-size: 14px;
        margin-top: 8px;
      }
      .operation-view{
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
}
</style>
