<template>
  <div class="list-content">
    <div class='list-header'>
      <span>发布列表</span>
      <el-button type='primary' size='mini' @click="handleAdd">发布题任务</el-button>
    </div>
    <div class="list-view">
      <div class="table-head">
        <div class="thead-box thead-box-title">标题</div>
        <div class="thead-box">发布时间</div>
        <div class="thead-box">提交情况</div>
        <div class="thead-box thead-box-small">类型</div>
        <div class="thead-box thead-box-middle">状态</div>
        <div class="thead-box thead-box-operation">操作</div>
      </div>
      <div class="tbody-box">
        <div v-for="item in list" :key="item.id" class="table-body">
          <div class="tbody-box-c tbody-box-title">{{ item.title && item.title !== '' ? item.title : '-' }}</div>
          <div class="tbody-box-c">{{ formatYYYYMMDDHHmm(item.createdAt) }}</div>
          <div class="tbody-box-c submission-status" @click="handleViewStatistics(item)">
            未提交 <span class="count-number">{{ item.unCompleteCount }}</span>人 已提交 <span class="count-number">{{ item.completeCount }}</span>人
          </div>
          <div class="tbody-box-c tbody-box-small">
            {{item.digitalHomeworkType === 'TEACHING_EXAM' ? '考试' : '练习'}}
          </div>
          <div class="tbody-box-c tbody-box-middle">
            <span v-if="item.status == 'ACTIVE'" style='color:rgba(39, 174, 96, 1);'>已发布</span>
            <span v-else style='color:rgba(235, 87, 87, 1);'>存草稿未发布</span>
          </div>
          <div class="tbody-box-c tbody-box-operation">
            <div class="option-btn" @click='handleEdit(item)'>
              查看
            </div>
            <div class="option-btn" v-if="item.status == 'INACTIVE'" @click='handlePublish(item)'>
              发布
            </div>
            <div class="option-btn" v-else @click='handleRevoke(item)'>
              撤回
            </div>
          </div>
        </div>
        <Empty v-if="list && list.length === 0" :image-size='150'  :image="empty3" description="暂无数据" />
      </div>
    </div>
    <AddExercise ref='addRef' :book-id='bookId' :course-id='courseId' @closeAddExercise='getList'/>
    <CreateBank
      ref="createBankRef"
      :test-paper-name='selectItem.title'
      :book-id='bookId'
      :task-id='selectItem.id'
      :test-id='selectItem.sourceId'
      :course-id='courseId'
      :total-score='totalScore'
      @publishSuccess='publishSuccess'
    />
  </div>
</template>

<script>
import CreateBank from '@/views/digitalbooks/questionBank/createBank'
import { formatYYYYMMDDHHmm } from '@/utils/time.js'
import empty3 from '@/assets/images/empty3.png'
import { Empty } from 'element-ui'
import AddExercise from '@/views/digitalbooks/editor/components/addExercise/addExercise.vue'
import { digitalHomework, getDigitalHomeworkList } from '@/api/digital-api'
import { getTestPaperQuestionList } from '@/api/test-api'
export default {
  name: 'PublishList',
  components: { AddExercise, Empty, CreateBank },
  props: {
    bookId: {
      type: Number,
      default: 0
    },
    courseId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      formatYYYYMMDDHHmm,
      empty3,
      list: [],
      selectItem: {},
      totalScore: 0
    }
  },
  methods: {
    handleAdd() {
      this.$refs.addRef.open({}, 'bank')
    },
    handleEdit(item) {
      this.$refs.addRef.setData(item, 'bank')
      this.$refs.addRef.open({}, 'bank', item.status !== 'ACTIVE')
    },
    handleViewStatistics(item) {
      if (item.status !== 'ACTIVE') {
        this.$message.warning('只有已发布的任务才能查看成绩统计')
        return
      }

      this.$emit('openGradeStatistics', {
        digital_homework_id: item.id,
        type: item.digitalHomeworkType === 'TEACHING_EXAM' ? '考试' : '练习'
      })
    },
    async getList() {
      const { data } = await getDigitalHomeworkList({
        pageNo: 1,
        pageSize: 2000,
        studentCourseId: this.courseId,
        digitalHomeworkTypes: 'TEACHING_EXAM,TEACHING_PRACTICE'
      })
      this.list = data.content
    },
    async handlePublish(item) {
      this.selectItem = item
      const { data } = await getTestPaperQuestionList({ testPaperId: Number(item.sourceId) })
      this.totalScore = data.questionList.reduce((total, cur) => total + cur.score, 0)
      this.$nextTick(() => {
        this.$refs.createBankRef.open()
      })
    },
    publishSuccess() {
      this.$refs.createBankRef.close()
      this.getList()
    },
    handleRevoke(item) { // 撤销
      this.$confirm('确定要撤回该任务吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        // 撤销接口
        const params = {
          apiType: 'delete',
          id: item.id
        }
        await digitalHomework(params)
        this.$message.success('撤回成功')
        await this.getList()
      })
    }
  }
}
</script>

<style scoped lang='scss'>
.el-table .el-table__header-wrapper .el-table__header colgroup col.custom-column,
.el-table .el-table__body-wrapper .el-table__body colgroup col.custom-column {
  width: 200px !important;
  min-width: 200px !important;
}
.list-content{
  width: 100%;
  height: 100%;
  padding: 10px 20px;
  .list-header{
    width: 100%;
    height: 25px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .list-view{
    width: 100%;
    height: calc(100% - 40px);
    margin-top: 10px;
    .table-head {
      background: #E3EFFF;
      height: 40px;
      width: 100%;
      display: flex;
      .thead-box{
        height: 100%;
        display: flex;
        align-items: center;
        color: #2F80ED;
        font-size: var(--font-size-L);
        padding: 0 5px;
        justify-content: center;
        width: 20%;
      }
      .thead-box-title {
        width: 20%;
        justify-content: flex-start;
      }
      .thead-box-middle{
        width: 15%;
      }
      .thead-box-small{
        width: 10%;
      }
      .thead-box-operation{
        width: 20%;
      }
    }
    .tbody-box {
      width: 100%;
      height: calc(100% - 40px);
      overflow-y: auto;
      @include scrollBar;
    }
    .table-body {
      min-height: 40px;
      width: 100%;
      display: flex;
      &:hover{
        background: #F5F8FF;
      }
      .tbody-box-c{
        display: flex;
        align-items: center;
        color: #333;
        font-size: var(--font-size-L);
        flex-wrap: wrap;
        line-height: 40px;
        padding: 0 5px;
        justify-content: center;
        width: 20%;
        .option-btn {
          color: #2F80ED;
          text-decoration-line: underline;
          cursor: pointer;
          margin-right: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .tbody-box-title {
        width: 20%;
        justify-content: flex-start;
      }
      .tbody-box-middle{
        width: 15%;
      }
      .tbody-box-small{
        width: 10%;
      }
      .tbody-box-operation{
        width: 20%;
      }
    }
  }

  .submission-status {
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 4px;
    padding: 4px 8px;

    .count-number {
      color: #2F80ED;
      text-decoration: underline;
      font-weight: 500;
    }

    &:hover {
      background-color: #f5f7fa;
      color: #409eff;

      .count-number {
        color: #409eff;
      }
    }

    &:active {
      background-color: #ecf5ff;
    }
  }
  ::v-deep .el-table__cell{
    height: 30px;
    border-bottom: 0 !important;
  }
  ::v-deep .el-table::before{
    height: 0;
  }
}
</style>
