<template>
  <div class="no-conversion" v-if="show">
    <div class="config-main">
      <i class="close-view el-icon-close" @click="close"></i>
      <div class="config-content">
        <div class="config-title">
          发布信息
        </div>
        <el-form ref='form' :model='formData' :rules="rules" label-position='top'>
          <el-row :gutter='10'>
           <el-col :span='16'>
             <el-form-item label="试卷名称">
               <el-input v-model='formData.testPaperName' placeholder='请输入试卷名称' maxlength='30' show-word-limit @blur='updateName'/>
             </el-form-item>
           </el-col>
          </el-row>
          <el-row :gutter='10'>
            <el-col :span='8'>
              <el-form-item label="班级">
                <el-input v-model='formData.className' disabled />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter='10'>
            <el-col :span='8'>
              <el-form-item label="试卷类型">
                <el-radio-group v-model="formData.testPaperType">
                  <el-radio :label="'TEACHING_PRACTICE'">普通练习</el-radio>
                  <el-radio :label="'TEACHING_EXAM'">考试</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <template v-if='formData.testPaperType === "TEACHING_EXAM"'>
            <el-row :gutter='10'>
              <el-col :span='8'>
                <el-form-item label="总分" prop='totalScore'>
                  <el-input v-model='formData.totalScore' />
                </el-form-item>
              </el-col>
              <el-col :span='8'>
                <el-form-item label="考试时长" prop='duration'>
                  <el-input v-model='formData.duration'>
                    <template slot="append">分钟</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter='10'>
              <el-col :span='16'>
                <el-form-item label="考试时间段" prop='time'>
                  <el-date-picker
                    v-model="formData.time"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :value-format="returnFormat"
                    style='width: 100%'
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </template>
          <el-row :gutter='10'>
            <el-col :span='8'>
              <el-form-item label="是否可以重做">
                <el-select v-model='formData.canRedo' style='width: 100%'>
                  <el-option
                    v-for='item in optionsList'
                    :key='item.value'
                    :label='item.label'
                    :value='item.value' />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span='12' v-if='formData.testPaperType === "TEACHING_EXAM"'>
              <el-form-item label="开启防作弊">
                <div class='flex'>
                  <el-select v-model='formData.antiCheating' style='width: 30%'>
                    <el-option
                      v-for='item in optionsList'
                      :key='item.value'
                      :label='item.label'
                      :value='item.value' />
                  </el-select>
                  <div style='width: 70%;height: 100%; font-size: 12px;margin-left: 5px;color: rgba(130, 130, 130, 1)'>
                    <div style='height: 25px;width: 100%;line-height: 25px;display: flex;align-items: center'>
                      <div style='margin-right: 3px;height: 5px;width: 5px;background-color: rgba(130, 130, 130, 1);border-radius: 50%'></div>仅支持切换界面3次，超过将自动交卷
                    </div>
                    <div style='height: 25px;width: 100%;line-height: 25px;display: flex;align-items: center'>
                      <div style='margin-right: 3px;height: 5px;width: 5px;background-color: rgba(130, 130, 130, 1);border-radius: 50%'></div>禁止粘贴考试内容
                    </div>
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="config-bottom">
        <div class="bottom-btn save-btn" @click='handlePublish'>
          <i class="el-icon-s-promotion btn-icon"></i>
          发布
        </div>
      </div>
    </div>
    <PromptView ref="promptRef" confirm-title='立即发布' title='确定要发布题库作业？' @confirm="handleConfirm"/>
  </div>
</template>

<script>
import PromptView from '@/components/classPro/PromptView/PromptView'
import { digitalHomework } from '@/api/digital-api'
import { createTest } from '@/api/test-api'
export default {
  name: 'CreateBank',
  components: { PromptView },
  props: {
    testPaperName: {
      type: String,
      default: ''
    },
    classId: {
      type: Number,
      default: 0
    },
    totalScore: {
      type: Number,
      default: 0
    },
    taskId: {
      type: Number,
      default: 0
    },
    testId: {
      type: Number,
      default: 0
    },
    bookId: {
      type: Number,
      default: 0
    },
    courseId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      show: false,
      returnFormat: 'yyyy-MM-dd HH:mm:ss',
      formData: {
        testPaperName: this.testPaperName,
        className: '',
        testPaperType: 'TEACHING_PRACTICE',
        totalScore: this.totalScore === 0 ? '-' : String(this.totalScore),
        duration: '',
        time: [],
        canRedo: true,
        antiCheating: true
      },
      optionsList: [
        {
          label: '是',
          value: true
        },
        {
          label: '否',
          value: false
        }
      ],
      rules: {
        totalScore: [
          { required: true, message: '请输入总分', trigger: 'change' },
          { pattern: /^[1-9]\d*$/, message: '请输入正整数', trigger: 'change' }
        ],
        duration: [
          { required: true, message: '请输入考试时长', trigger: 'change' },
          { pattern: /^[1-9]\d*$/, message: '请输入正整数', trigger: 'change' }
        ],
        time: [
          { type: 'array', required: true, message: '请选择考试时间段', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    testPaperName(val) {
      this.formData.testPaperName = val
    },
    totalScore(val) {
      this.formData.totalScore = val === 0 ? '-' : String(val)
    }
  },
  methods: {
    open() {
      this.show = true
      const selectCurrUser = JSON.parse(localStorage.getItem('selectCurrUser') || '{}')
      this.formData.className = selectCurrUser.name
    },
    close() {
      this.show = false
    },
    async handlePublish() {
      await this.$refs.form.validate()
      this.$refs.promptRef.open()
    },
    updateName() {
      this.$emit('updateName', this.formData.testPaperName)
    },
    async handleConfirm() {
      try {
        await createTest({
          testPaperId: this.testId,
          testPaperLinkType: 'DIGITAL_HOMEWORK_TEACHING',
          testPaperTitle: this.formData.testPaperName,
          referLinkSourceId: this.bookId
        })
        const params = {
          apiType: this.taskId === 0 ? 'create' : 'update',
          title: this.formData.testPaperName,
          status: 'ACTIVE',
          sourceId: this.testId,
          digitalBookId: this.bookId,
          studentCourseId: this.courseId,
          digitalHomeworkType: this.formData.testPaperType,
          timeLimit: this.formData.duration === '' ? 0 : Number(this.formData.duration) * 60,
          startTime: this.formData.time.length > 1 ? this.formData.time[0] : null,
          endTime: this.formData.time.length > 1 ? this.formData.time[1] : null,
          canRedo: this.formData.canRedo,
          antiCheating: this.formData.antiCheating
        }
        if (this.taskId !== 0) {
          params.id = this.taskId
        }
        await digitalHomework(params)
        this.$message.success('发布成功')
        this.$emit('publishSuccess')
      } catch (e) {
        console.log(e)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.no-conversion{
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 102;
  display: flex;
  align-items: center;
  justify-content: center;
  ::v-deep .el-form-item{
    margin-bottom: 10px;
  }
  ::v-deep .el-form-item__label{
    height: 50px;
    line-height: 50px;
    font-size: 20px;
    padding: 0;
  }
  ::v-deep .el-form-item__content{
    height: 50px;
    line-height: 50px;
    font-size: 20px;
  }
  ::v-deep .el-input{
    height: 50px;
    line-height: 50px;
    font-size: 20px;
  }
  ::v-deep .el-input__inner{
    height: 50px;
    line-height: 50px;
    font-size: 20px;
    padding: 0 10px;
  }
  ::v-deep .el-input-group__append{
    height: 48px;
    line-height: 48px;
    font-size: 20px;
    box-sizing: border-box;
    padding: 0 20px;
    border-radius: 3px;
  }
  ::v-deep .el-radio{
    font-size: 20px;
    margin-right: 20px;
  }
  ::v-deep .el-radio__label{
    font-size: 20px;
    padding-left: 10px;
  }
  ::v-deep .el-radio__inner{
    width: 20px;
    height: 20px;
  }
  ::v-deep .el-range__icon{
    font-size: 20px;
    line-height: 48px;
  }
  ::v-deep .el-range-input{
    font-size: 20px;
  }
  ::v-deep .el-range-separator{
    line-height: 48px;
    font-size: 20px;
    padding: 0 10px;
  }
  ::v-deep .el-input__suffix{
    right: 10px;
  }
  ::v-deep .el-select__caret{
    font-size: 20px;
  }
  ::v-deep .el-input__icon{
    line-height: 48px;
  }
  ::v-deep .el-select-dropdown__item{
    font-size: 20px !important;
    height: 30px !important;
    line-height: 50px !important;
  }
  .config-main{
    width: 1000px;
    padding: 40px 30px 10px 30px;
    box-sizing: border-box;
    position: relative;
    background: #f0f0f0;
    border-radius: 10px;
    max-height: 95vh;
    overflow-y: auto;
    .close-view{
      position: absolute;
      top: 10px;
      right: 20px;
      font-size: 20px;
      cursor: pointer;
    }
    .config-bottom{
      width: 100%;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 10px;
      .bottom-btn{
        height: 40px;
        padding: 0 20px;
        border-radius: 5px;
        cursor: pointer;
        color: white;
        font-size: 18px;
        display: flex;
        align-items: center;
        background: rgba(47, 128, 237, 1);
        .btn-icon{
          font-size: 18px;
          margin-right: 5px;
        }
        &:hover{
          opacity: 0.8;
        }
      }
    }
    .config-content{
      font-size: 20px;
      padding: 20px;
      background: white;
      border-radius: 10px;
      .config-title{
        font-weight: 500;
        margin-bottom: 5px;
      }
    }
  }
}
</style>
