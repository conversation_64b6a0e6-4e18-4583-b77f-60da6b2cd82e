<template>
  <div v-if="show" class="list-body">
    <div class="header_view">
      <img class="back" src="@/assets/digitalbooks/arrow-left.svg" @click="back" />
      <div class="head-title article-singer-container">
        题库
      </div>
    </div>
    <div class="content_view">
      <div class="content_left">
        <template v-if='!isStudent'>
          <div class="left-item" :class="{'item-selected':selectName === 'publish' }" @click="handleSelect('publish')">试卷发布</div>
          <div class="left-item" :class="{'item-selected':selectName === 'manage' }" @click="handleSelect('manage')">题库管理</div>
        </template>
        <template v-else>
          <el-badge class="left-item" :class="{'item-selected':selectName === 'task' }" :hidden='count === 0'  :value='count' :max='99'>
            <div @click="handleSelect('task')">
              试题任务
            </div>
          </el-badge>
          <div class="left-item" :class="{'item-selected':selectName === 'wrong' }" @click="handleSelect('wrong')">错题集</div>
        </template>
      </div>
      <div class="content_right">
        <PublishList v-if="selectName === 'publish'" ref="publishRef" :book-id="bookId" :course-id='courseId' @openGradeStatistics="handleOpenGradeStatistics"/>
        <ManageList v-if="selectName === 'manage'" ref="manageRef" :book-id="bookId" :course-id='courseId'/>
        <TaskList v-if="selectName === 'task'" ref="taskRef" :book-id="bookId" :course-id='courseId' @updateUnfinishedCount='updateUnfinishedCount'/>
        <WrongList v-if="selectName === 'wrong'" ref="wrongRef" :book-id="bookId" :course-id='courseId' />
      </div>
    </div>
  </div>
</template>

<script>
import TaskList from '@/views/digitalbooks/questionBank/components/taskList'
import WrongList from '@/views/digitalbooks/questionBank/components/wrongList'
import ManageList from '@/views/digitalbooks/questionBank/components/manageList'
import PublishList from '@/views/digitalbooks/questionBank/components/publishList'
export default {
  name: 'List',
  components: { PublishList, ManageList, WrongList, TaskList },
  data() {
    return {
      show: false,
      selectName: 'publish',
      bookId: 0,
      courseId: 0,
      courseItem: null,
      count: 0
    }
  },
  computed: {
    isStudent() {
      return this.courseItem.userClass.userType === 'SCHOOL_STUDENT'
    }
  },
  methods: {
    back() {
      this.count = 0
      this.show = false
    },
    open(data) {
      this.selectName = 'publish'
      this.courseId = data.id
      this.courseItem = data
      this.bookId = data.digitalBook.id
      this.show = true
      this.selectName = this.isStudent ? 'task' : 'publish'
      this.getList()
    },
    handleSelect(type) { // 发布：publish，题库管理：manage，错题集：wrong
      this.selectName = type
      this.getList()
    },
    getList() {
      if (this.selectName === 'publish') {
        this.$nextTick(() => {
          this.$refs.publishRef.getList()
        })
      } else if (this.selectName === 'manage') {
        this.$nextTick(() => {
          this.$refs.manageRef.getList()
        })
      } else if (this.selectName === 'wrong') {
        this.$nextTick(() => {
          this.$refs.wrongRef.getList()
        })
      } else if (this.selectName === 'task') {
        this.$nextTick(() => {
          this.$refs.taskRef.getList()
        })
      }
    },
    updateUnfinishedCount(count) {
      this.count = count
    },
    handleOpenGradeStatistics(params) {
      this.$emit('openGradeStatistics', params)
    }
  }
}
</script>

<style scoped lang='scss'>
.list-body{
  width: 100%;
  height: 100%;
  padding: 10px 20px;
  background-color: #e5f0ff;
  position: fixed;
  top: 0;
  left: 0;
  .header_view{
    height: 35px;
    display: flex;
    width: 100%;
    align-items: center;
    .back {
      width: 20px;
      height: 20px;
      object-fit: cover;
      cursor: pointer;
    }
    .head-title {
      color: #000;
      font-size: var(--font-size-XXL);
      font-weight: 500;
      margin-left: 10px;
      width: 250px;
    }
  }
  .content_view{
    width: 100%;
    height: calc(100% - 35px);
    display: flex;
    gap: 10px;
    .content_left{
      width: 150px;
      height: 100%;
      box-sizing: border-box;
      background: white;
      border-radius: 10px;
      display: flex;
      flex-direction: column;
      padding: 20px;
      color: rgba(0, 0, 0, 1);
      font-weight: 500;
      font-size: 14px;
      gap: 30px;
      align-items: center;
      .left-item{
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        padding: 10px 0;
        border-radius: 8px;
        cursor: pointer;
        &:hover{
          color: rgba(47, 128, 237, 1);
          //background: rgba(227, 239, 255, 1);
        }
      }
      .item-selected{
        color: rgba(47, 128, 237, 1);
        background: rgba(227, 239, 255, 1);

      }
    }
    .content_right{
      width: calc(100% - 210px);
      height: 100%;
      box-sizing: border-box;
      background: white;
      border-radius: 10px;
    }
  }
}
</style>
