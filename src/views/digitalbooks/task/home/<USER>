<template>
  <!-- 课时选择 -->
  <div class="task-class">
    <div class="head-box">
      <img class="back" src="@/assets/digitalbooks/arrow-left.svg" @click="back" />
      <div class="head-title article-singer-container">
        任务阅读模式
      </div>
    </div>
    <div v-if="isEmpty" class="task-content-empty" v-loading="loading">
      <div class='task-box'>
        <ReadEmpty v-if='showEmpty' msg='本书暂无任务' />
      </div>
    </div>
    <div v-else class="task-content">
      <div class="task-box">
        <div class="task-title">
          <div class="top_content">
            <div class="left">
              <p>当前任务：<span>{{ currentDigitalHomework?currentDigitalHomework.title:'' }}</span></p>
              <el-button class="button1" type="primary" plain @click="handleDetail(currentDigitalHomework)">继续学习</el-button>
            </div>
            <div class="right">
              <p class="p1">已完成：{{ completed }}</p>
              <p class="p2">总任务：{{ total }}</p>
            </div>
          </div>
        </div>
        <div class="tbody-box">
          <div class="catalogue-box">
            <div
              v-for="(item, index) in catalogueList"
              :key="index"
              class="card-title"
              :class="CatalogueId === item.id ? 'active' : ''"
              :title="item.title"
              @click="changeData(item.id)"
            >
              {{ item.title }}
            </div>
          </div>
          <div class="right_content">
            <el-timeline>
              <el-timeline-item
                v-for="(item, index) in list"
                :key="index"
                class="timeline-item"
                size="large"
                :icon="item.digitalHomeworkType==='TESTPAPER'&&item.userTestpaper&&item.userTestpaper.progressStatus === 'FINISHED'
                ||item.digitalHomeworkType==='STANDARD'&&item.userDigitalHomework
                ||item.digitalHomeworkType==='TRAINING'&&item.userDigitalHomework?'el-icon-success':''"
                :color="item.digitalHomeworkType==='TESTPAPER'&&item.userTestpaper&&item.userTestpaper.progressStatus === 'FINISHED'
                ||item.digitalHomeworkType==='STANDARD'&&item.userDigitalHomework
                ||item.digitalHomeworkType==='TRAINING'&&item.userDigitalHomework?'#FFFFFF':'#4FACFE'"
              >
                <div
                  class="item"
                  :class="item.digitalHomeworkType==='TESTPAPER'&&item.userTestpaper&&item.userTestpaper.progressStatus === 'FINISHED'
                  ||item.digitalHomeworkType==='STANDARD'&&item.userDigitalHomework
                  ||item.digitalHomeworkType==='TRAINING'&&item.userDigitalHomework?'done':''">
                  <div class="tab">{{ index+1 }}</div>
                  <div class="title">{{ item.title }}</div>
                  <el-button
                    v-if="bookInfo.studentCourseId!== 0 || catalogueList[0].id===CatalogueId || catalogueList[1].id===CatalogueId"
                    class="button1"
                    type="primary"
                    plain
                    @click="handleDetail(item)"
                  >
                    {{
                      item.digitalHomeworkType==='TESTPAPER'&&item.userTestpaper&&item.userTestpaper.progressStatus === 'FINISHED'
                      ||item.digitalHomeworkType==='STANDARD'&&item.userDigitalHomework
                      ||item.digitalHomeworkType==='TRAINING'&&item.userDigitalHomework?'已完成':'开始学习' }}
                  </el-button>
                  <el-button v-else type="primary" class="button1" icon="el-icon-lock" plain @click="pay">购买学习</el-button>
                </div>
              </el-timeline-item>
            </el-timeline>
            <Empty v-if="list && list.length === 0" :msg="'暂无数据'" />
          </div>

        </div>
      </div>
    </div>
    <div v-if="detailShow" class="task-pop">
      <taskDetail :task-id="taskId" :mode="'submit'" @close="detailShow = false;_getDigitalHomeworkList();getCurrntHomework()" />
    </div>
    <doTest ref="doTest" :test-id="testId" @refresh="_getDigitalHomeworkList();getCurrntHomework()" />
    <PayToast ref="pay" :good-info="bookInfo&&bookInfo.goodsComm" />
    <doExercise ref="doExerciseRef" @close="_getDigitalHomeworkList();getCurrntHomework()"/>
    <doExcelTraining ref="doExcelTrainingRef" source-type='do-task' :task-id='taskId' :student-course-id='studentCourseId' @submit='_getDigitalHomeworkList;getCurrntHomework()'/>
    <doAiTraining ref="doAiTrainingRef" source-type='do-task' :task-id='taskId' :student-course-id='studentCourseId'  @submit='_getDigitalHomeworkList;getCurrntHomework()'/>
  </div>
</template>

<script>
import doAiTraining from '@/views/digitalbooks/editor/components/doAiTraing'
import doExcelTraining from '@/views/digitalbooks/editor/components/doExcelTraing.vue'
import doExercise from '@/views/digitalbooks/read/doExercise/doExercise'
import ReadEmpty from '@/components/classPro/Empty/readEmpty'
import defaultCourse from '@/assets/images/default-cover.jpg'
import {
  getBook,
  getDigitalTaskListByCatalogueId,
  digitalTask,
  getDigitalHomeworkProgress,
  getBookCatalogueByVersion,
  getDigitalTaskList,
  userDigitalHomework
} from '@/api/digital-api.js'
import { formatYYYYMMDDHHmm } from '@/utils/time.js'
import taskDetail from '../detail.vue'
import Empty from '@/components/classPro/Empty/index.vue'
import doTest from '../doTest.vue'
import PayToast from '@/components/classPro/Pay/index.vue'
import { debounce } from 'utils'
import { getTrainingPresetFile } from '@/api/course-api'
import { mapGetters } from 'vuex'
import { getSqlPlatformToken } from '@/api/training-api'
export default {
  components: { taskDetail, Empty, doTest, PayToast, ReadEmpty, doExercise, doAiTraining, doExcelTraining },
  data () {
    return {
      defaultCourse,
      formatYYYYMMDDHHmm,
      bookTitle: '',
      bookId: 0,
      studentCourseId: 0,
      visible: false,
      list: [],
      progressShow: false,
      selectTaskInfo: null,
      detailShow: false,
      taskId: null,
      testId: 0,
      bookInfo: null,
      catalogueList: [],
      currentDigitalHomework: null,
      CatalogueId: 0,
      freeId: 0,
      total: 0,
      completed: 0,
      isEmpty: true,
      loading: false,
      showEmpty: false
    }
  },
  computed: {
    ...mapGetters([
      'id'
    ])
  },
  async mounted () {
    this.bookId = this.$route.query && this.$route.query.id
    this.studentCourseId = this.$route.query && Number(this.$route.query.studentCourseId || 0)
    await this._getBook()
    await this._getBookCatalogueByVersion()
    await this.getCurrntHomework()
    this._getDigitalHomeworkList()
  },
  methods: {
    async getCurrntHomework() {
      this.loading = true
      const { data } = await getDigitalTaskList({
        digitalBookId: this.bookId,
        studentCourseId: this.studentCourseId
      })
      this.currentDigitalHomework = data.currentDigitalHomework
      this.total = data.total
      this.completed = data.completed
      if (this.total === 0) {
        this.isEmpty = true
        this.showEmpty = true
      } else {
        this.isEmpty = false
        this.showEmpty = false
      }
      this.loading = false
    },
    changeData(id) {
      this.CatalogueId = id
      this._getDigitalHomeworkList()
    },
    async  _getBookCatalogueByVersion() {
      const { data } = await getBookCatalogueByVersion({
        bookId: this.bookId,
        type: 'CHAPTER'
        // approvedOnly: false
      })
      this.catalogueList = data
      if (this.catalogueList.length > 0) {
        this.CatalogueId = data[0].id
      }
    },
    pay() {
      if (!this.bookInfo.goodsComm || this.bookInfo.goodsComm.price === null) {
        this.$message.warning('本教材暂不支持购买，可使用兑换码进行兑换')
        return
      }
      this.$refs.pay.show()
    },
    back () {
      this.$router.go(-1)
    },
    async _getBook () {
      if (this.bookId) {
        const { data } = await getBook({
          bookId: this.bookId,
          scene: 'BOOK_CATALOGUE_OWN'
        })
        this.bookTitle = data.title
        this.bookInfo = data
      }
    },
    async _getDigitalHomeworkList () {
      const { data } = await getDigitalTaskListByCatalogueId({
        digitalCatalogueId: this.CatalogueId,
        studentCourseId: this.studentCourseId
      })
      this.list = data
      if (this.CatalogueId === this.catalogueList[0].id) {
        if (this.list.length > 0) {
          this.freeId = this.list[0].id
        }
      }
      // this.currentDigitalHomework = data.currentDigitalHomework
    },
    async handleProgress (item) {
      const { data } = await getDigitalHomeworkProgress({
        digitalHomeworkId: item.id
      })
      this.selectTaskInfo = {
        taskInfo: item,
        progress: data
      }
      this.progressShow = true
    },
    async deleteTask (item) {
      this.$confirm('确定撤回吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        await digitalTask({
          apiType: 'delete',
          id: item.id
        })
        await this._getDigitalHomeworkList()
        this.visible = false
        this.$message.success('撤回成功')
      }).catch(() => {

      })
    },
    handleDetail (item) {
      if ((this.studentCourseId === 0) && this.freeId !== item.id) {
        this.pay()
        return
      }
      this.taskId = item.id
      if (item.digitalHomeworkType === 'TRAINING'){
        let homeworkId = null
        if (item.userDigitalHomework){
          homeworkId = item.userDigitalHomework.id
        }
        if (item.training && item.training.trainingType === 'FINACE_PRACTICE') {
          this.$refs.doExcelTrainingRef.open(item.training.trainingId, homeworkId)
          return
        }
        if (item.training && item.training.trainingType === 'COMM_PRACTICE') {
          this.$refs.doAiTrainingRef.open(item.training.trainingId, homeworkId)
          return
        }
        if (item.training && item.training.trainingType === 'PYTHON_PRACTICE') {
          this.$message.success({ duration: 3000, message: '正在跳转请稍后...' })
          this.getTrainingPresetFile(item.training.trainingId, item.userDigitalHomework ? item.userDigitalHomework.id : null)
        }
        if (item.training && item.training.trainingType === 'SQL_PRACTICE') {
          this.$message.success({ duration: 3000, message: '正在跳转请稍后...' })
          this.doSql(item.userDigitalHomework ? item.userDigitalHomework.id : null)
        }
        return
      }
      if (item.sourceId) {
        this.testId = item.sourceId
        // this.$refs.doTest.open()
        this.$refs.doExerciseRef.open(this.testId, null, this.studentCourseId, null, 'read-task', item.title)
        return
      }
      this.detailShow = true
    },
    getTrainingPresetFile: debounce(async function (trainingId, homeworkId) {
      if (homeworkId) {
        await userDigitalHomework({
          apiType: 'update',
          digitalHomeworkId: this.taskId,
          userId: this.id,
          id: this.homeworkId,
          studentCourseId: this.studentCourseId
        }, { authorization: this.token })
      } else {
        await userDigitalHomework({
          apiType: 'create',
          digitalHomeworkId: this.taskId,
          userId: this.id,
          studentCourseId: this.studentCourseId
        }, { authorization: this.token })
      }
      await this.getCurrntHomework()
      await this._getDigitalHomeworkList()
      const params = {
        trainingId: trainingId,
        userId: this.id,
      }
      await getTrainingPresetFile(params)
        .then(response => {
          if (response.code === 200) {
            window.open(`https://binguoketang.com/jupyterhub/hub/logout`, '_blank')
          } else {
            this.$message.error(response.message || '获取文件失败')
          }
        })
        .catch(err => {
          console.log(err)
          this.$message.error('获取文件失败')
        })
    }, 2000, true),
    doSql: debounce(async function(homeworkId) {
      if (homeworkId) {
        await userDigitalHomework({
          apiType: 'update',
          digitalHomeworkId: this.taskId,
          userId: this.id,
          id: homeworkId,
          studentCourseId: this.studentCourseId
        }, { authorization: this.token })
      } else {
        await userDigitalHomework({
          apiType: 'create',
          digitalHomeworkId: this.taskId,
          userId: this.id,
          studentCourseId: this.studentCourseId
        }, { authorization: this.token })
      }
      await this.getCurrntHomework()
      await this._getDigitalHomeworkList()
      getSqlPlatformToken({}, { authorization: this.token }).then(res => {
        window.open(res.data)
      })
    }, 2000, true)
  }
}
</script>

  <style lang="scss" scoped>
  .task-class {
    width: 100%;
    height: 100%;
    padding: 10px;

    .head-box {
      height: 40px;
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      position: relative;

      .share {
        position: absolute;
        right: 40px;
        font-size: 12px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        img {
          width: 20px;
          height: 20px;
        }
      }

      .back {
        width: 20px;
        height: 20px;
        object-fit: cover;
        cursor: pointer;
      }

      .head-title {
        color: #000;
        font-size: var(--font-size-L);
        font-weight: 500;
        margin-left: 10px;
      }
    }
    .task-content-empty {
      height: calc(100% - 40px);
      width: 100%;
      padding: 5px 0;
      .task-box{
        border-radius: 10px;
        width: 100%;
        height: 100%;
        padding: 10px;
        box-sizing: border-box;
        background: #E1F1FF;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .task-content {
      padding: 5px 0;
      height: calc(100% - 40px);
      width: 100%;

      .task-title {
        height: 100px;
        color: #000;
        font-size: var(--font-size-L);
        font-weight: 500;
        display: flex;
        justify-content: space-between;
        .top_content{
            width: 100%;
            display: flex;
            justify-content: space-between;
            .left{
                width: 524px;
                height: 70px;
                border-radius: 10px;
                background: linear-gradient(90deg, #4FACFE 0%, #00F2FE 100%);
                position: relative;
                p{
                    font-size: var(--font-size-XXL);
                    font-weight: 400;
                    margin-left: 30px;
                    line-height: 40px;
                    span{
                        font-weight: 700;
                    }
                }
                .button1{
                    width: 70px;
                    height: 30px;
                    font-size: 10px;
                    line-height: 0px;
                    padding: 0;
                    position: absolute;
                    right: 20px;
                    top:20px
                }
            }
            .right{
                width: 524px;
                height: 70px;
                border-radius: 10px;
                background: linear-gradient(90deg, #8FD3F4 0%, #84FAB0 100%);
                display: flex;
                font-size: var(--font-size-XXL);
                .p1{
                    width: 50%;
                    text-align: center;
                    line-height: 40px;
                }
                .p2{
                    width: 50%;
                    line-height: 40px;
                }

            }
        }
      }

      .task-box {
        border-radius: 10px;
        width: 100%;
        height: 100%;
        padding: 10px;
        box-sizing: border-box;
        background: #E1F1FF;

        .tbody-box {
          width: 100%;
          height: 75%;
          display: flex;
          justify-content: space-between;
          .right_content{
            width: 78%;
            height: 100%;
            overflow-y: auto;
            @include scrollBar;
          }
          .catalogue-box{
        width: 20%;
        height: calc(100% );
        overflow-y: auto;
        @include scrollBar;
        background:#F9F9F9;
        border-radius: 10px;
        .card-title {
        width: 90%;
        height: 30px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 12px;
        text-align: left;
        margin: 0 auto;
        cursor: pointer;
        padding-left: 5px;
        line-height: 30px;
        margin-top: 10px;
      }
      .card-title:hover {
        background: gainsboro;
      }
      .active {
        background: #2F80ED;
        color: #ffffff;
      }
      }
          .timeline-item{
            margin-left: 10px;
            padding-bottom: 0px;
            margin-right: 10px;
          }
          ::v-deep .el-timeline-item__tail{
            border-color:#4FACFE!important;
            top:35px
          }
          ::v-deep .el-timeline-item__node{
            margin-top: 25px;
          }
          ::v-deep .el-icon-success{
            color:#4FACFE;
            transform: scale(1.7);
          }
          .item{
            height: 75px;
        width: 100%;
        display: flex;
        margin-top: 10px;
        background:#FFFFFF;
        border-radius: 10px;
        position: relative;
        .title{
            font-size: 14px;
            font-weight: 700;
            height: 75px;
            line-height: 75px;
            padding-left: 20px;
        }
      .tab{
        width: 30px;
        height: 30px;
        border-radius: 15px;
        background: linear-gradient(90deg, #4FACFE 0%, #00F2FE 100%);
        line-height: 30px;
        text-align: center;
        color: #ffffff;
        font-size: 14px;
        font-weight: 600;
        margin-left: 30px;
        margin-top: 22px;
      }
    .button{
        width: 50px;
        height: 30px;
        font-size: 12px;
        line-height: 0px;
        padding: 0;
        position: absolute;
        right: 50px;
        top:20px
    }
    .button1{
        width: 70px;
        height: 30px;
        font-size: 10px;
        line-height: 0px;
        padding: 0;
        position: absolute;
        right: 20px;
        top:20px
    }
          }
          .done{
            background: #CCE1FF;
          }
        }
      }
    }

    .task-pop {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: #E9F2FF;
    }
  }
  </style>
