<template>
  <!-- 课时选择 -->
  <div class="task-class">
    <div class="head-box">
      <img class="back" src="@/assets/digitalbooks/arrow-left.svg" @click="back" />
      <div class="head-title article-singer-container">
        {{ bookTitle }}
      </div>
    </div>

    <div class="task-content">
      <div class="task-box">
        <div class="task-title">
          任务列表
          <div class="classpro-btn" @click="$router.push(`/digitalbooks/push-task?id=${bookId}&studentCourseId=${studentCourseId}`)">发布任务</div>
        </div>

        <div class="table-head">
          <div class="thead-box-task">任务标题</div>
          <div class="thead-box">发布时间</div>
          <div class="thead-box">提交情况</div>
          <div class="thead-box">操作</div>
        </div>

        <div class="tbody-box">
          <div v-for="item in list" :key="item.id" class="table-body">
            <div class="tbody-box-task">{{ item.title }}</div>
            <div class="tbody-box-c">{{ formatYYYYMMDDHHmm(item.createdAt) }}</div>
            <div class="tbody-box-c">未提交 {{ item.unCompleteCount }}人 已提交 {{ item.completeCount }}人</div>
            <div class="tbody-box-c">
              <div class="option-btn" @click="handleDetail(item)">
                查看任务
              </div>
              <div class="option-btn" @click="handleProgress(item)">
                完成情况
              </div>
              <div class="option-btn" @click="deleteTask(item)">
                撤回任务
              </div>
            </div>
          </div>
          <Empty v-if="list && list.length === 0" :msg="'暂无数据'" />
        </div>
      </div>
    </div>
    <div v-if="detailShow" class="task-pop">
      <taskDetail :task-id="taskId" :mode="'edit'" @close="detailShow = false" />
    </div>
    <taskProgress v-if="progressShow" :task-info="selectTaskInfo" @close="progressShow = false" />
  </div>
</template>

<script>
import defaultCourse from '@/assets/images/default-cover.jpg'
import { getBook, getDigitalHomeworkList, digitalHomework, getDigitalHomeworkProgress } from '@/api/digital-api.js'
import { formatYYYYMMDDHHmm } from '@/utils/time.js'
import taskDetail from '../detail.vue'
import taskProgress from './progress.vue'
import Empty from '@/components/classPro/Empty/index.vue'

export default {
  components: { taskDetail, taskProgress, Empty },
  data () {
    return {
      defaultCourse,
      formatYYYYMMDDHHmm,
      bookTitle: '',
      bookId: 0,
      studentCourseId: 0,
      visible: false,
      list: [],
      progressShow: false,
      selectTaskInfo: null,
      detailShow: false,
      taskId: ''
    }
  },
  mounted () {
    this.bookId = this.$route.query && this.$route.query.id
    this.studentCourseId = this.$route.query && this.$route.query.studentCourseId
    this._getBook()
    this._getDigitalHomeworkList()
  },
  methods: {
    back () {
      this.$router.push('/classpro/myDigitalbooks')
    },
    async _getBook () {
      if (this.bookId) {
        const { data } = await getBook({
          bookId: this.bookId,
          scene: 'BOOK_CATALOGUE_OWN'
        })
        this.bookTitle = data.title
      }
    },
    async _getDigitalHomeworkList () {
      const { data } = await getDigitalHomeworkList({
        pageNo: 1,
        pageSize: 1000,
        studentCourseId: this.studentCourseId,
        digitalHomeworkTypes: 'STANDARD,TESTPAPER'
      })
      this.list = data.content
    },
    async handleProgress (item) {
      const { data } = await getDigitalHomeworkProgress({
        digitalHomeworkId: item.id
      })
      this.selectTaskInfo = {
        taskInfo: item,
        progress: data
      }
      this.progressShow = true
    },
    async deleteTask (item) {
      this.$confirm('确定撤回吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        await digitalHomework({
          apiType: 'delete',
          id: item.id
        })
        await this._getDigitalHomeworkList()
        this.visible = false
        this.$message.success('撤回成功')
      }).catch(() => {

      })
    },
    handleDetail (item) {
      this.taskId = item.id
      this.detailShow = true
    }
  }
}
</script>

<style lang="scss" scoped>
.task-class {
  width: 100%;
  height: 100%;
  padding: 10px;

  .head-box {
    height: 40px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    position: relative;

    .share {
      position: absolute;
      right: 40px;
      font-size: 12px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      img {
        width: 20px;
        height: 20px;
      }
    }

    .back {
      width: 20px;
      height: 20px;
      object-fit: cover;
      cursor: pointer;
    }

    .head-title {
      color: #000;
      font-size: var(--font-size-L);
      font-weight: 500;
      margin-left: 10px;
    }
  }

  .task-content {
    padding: 10px 0;
    height: calc(100% - 40px);
    width: 100%;

    .task-title {
      height: 40px;
      color: #000;
      font-size: var(--font-size-L);
      font-weight: 500;
      display: flex;
      justify-content: space-between;

      .classpro-btn {
        padding: 5px 10px;
        height: 30px;
        margin-top: -5px;
      }
    }

    .task-box {
      border-radius: 10px;
      background: #FFF;
      width: 100%;
      height: 100%;
      padding: 20px;
      box-sizing: border-box;

      .table-head {
        background: #E3EFFF;
        height: 40px;
        width: 100%;
        display: flex;
        .thead-box, .thead-box-task {
          width: 25%;
          height: 100%;
          display: flex;
          align-items: center;
          color: #2F80ED;
          font-size: var(--font-size-L);
          padding: 0 5px;
        }

        .thead-box {
          justify-content: center;
        }

        .thead-box-task {
          justify-content: flex-start;
        }
      }

      .tbody-box {
        width: 100%;
        height: calc(100% - 40px - 40px);
        overflow-y: auto;
        @include scrollBar;
      }

      .table-body {
        min-height: 40px;
        width: 100%;
        display: flex;
        .tbody-box-c {
          justify-content: center;
        }
        .tbody-box-task {
          justify-content: flex-start;
        }

        .tbody-box-c, .tbody-box-task {
          width: 25%;
          display: flex;
          align-items: center;
          color: #333;
          font-size: var(--font-size-L);
          flex-wrap: wrap;
          line-height: 40px;
          padding: 0 5px;

          .option-btn {
            color: #2F80ED;
            text-decoration-line: underline;
            cursor: pointer;
            margin-right: 10px;
          }
        }
      }
    }
  }

  .task-pop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #E9F2FF;
  }
}
</style>
