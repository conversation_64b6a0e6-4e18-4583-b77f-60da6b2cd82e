import { MessageBox } from 'element-ui'
import { addPythonTrainingModal, closePythonTraining, setData } from '../components/addPythonTraining/addPythonTraining'
export function pythonTraining(editor) {
  editor.ui.registry.addContextToolbar('pythonEditcontrol', {
    // 浮层的触发条件
    predicate: function (node) {
      return node.classList.contains('python_card')
    },
    items: 'pythonEditcontrol pythonRemovecontrol', // 显示的工具列表
    position: 'selection' // 工具栏放置位置  selection node line
  })
  editor.ui.registry.addButton('pythonEditcontrol', {
    icon: 'edit-block',
    title: '编辑实训',
    tooltip: '编辑实训',
    onAction: () => {
      const bookmark = editor.selection.getBookmark()
      const selectContent = editor.selection.getNode()
      const oldData = JSON.parse(selectContent.getElementsByClassName('info')[0].innerText)
      setData(oldData)
      addPythonTrainingModal({
        onSubmit (data) {
          editor.selection.moveToBookmark(bookmark)
          const media = `<div class='python_card mceNonEditable' data-id="${data.id}" style='width:100%;background: #E3F5FF;border-radius: 5px;box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);overflow:hidden;padding:30px;box-sizing: border-box;position:relative;margin-bottom:2px'><div class="info" style="display:none">${JSON.stringify(data)}</div><div style="color:#4F4F4F;font-size:20px;font-weight:700">${data.title}</div><div style="display:flex;margin-top:40px"><img style="width:30px;height: 30px;object-fit: cover" src='https://static.bingotalk.cn/bingodev/image/2024072615483836.svg' /><div style="line-height:30px;width:60%">${data.subTitle}</div></div><div class='to_pythonTrain' style="position: absolute; right: 10%; bottom: 26%;  padding-left:10px; padding-right:10px; padding-top:8px;padding-bottom:8px; border: 2px solid #2D9CDB; border-radius: 5px; text-align: center; color: #2d9cdb;cursor: pointer;">开始实训</div></div></div>`
          editor.selection.setContent(media)
          closePythonTraining()
        }
      })
    }
  })
  editor.ui.registry.addButton('pythonRemovecontrol', {
    icon: 'remove',
    title: '删除实训',
    tooltip: '删除实训',
    onAction: () => {
      const bookmark = editor.selection.getBookmark()
      MessageBox.confirm('确认删除实训？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        editor.selection.moveToBookmark(bookmark)
        editor.focus()
        editor.selection.setContent('')
        editor.selection.collapse()
      })
    }
  })
  editor.ui.registry.addButton('pythonTraining', {
    text: 'Python实训',
    title: 'Python实训',
    tooltip: 'Python实训',
    onAction: () => {
      let type = 'add'
      const selectContent = editor.selection.getNode()
      const bookmark = editor.selection.getBookmark()
      if (selectContent.classList.contains('python_card')) {
        const oldData = JSON.parse(selectContent.getElementsByClassName('info')[0].innerText)
        setData(oldData)
        type = 'edit'
      }
      addPythonTrainingModal({
        onSubmit (data) {
          if (type === 'add') {
            // editor.selection.moveToBookmark(bookmark)
            const media = `<div class='python_card mceNonEditable' data-id="${data.id}" style='width:100%;background: #E3F5FF;border-radius: 5px;box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);overflow:hidden;padding:30px;box-sizing: border-box;position:relative;margin-bottom:2px'><div class="info" style="display:none">${JSON.stringify(data)}</div><div style="color:#4F4F4F;font-size:20px;font-weight:700">${data.title}</div><div style="display:flex;margin-top:40px"><img style="width:30px;height: 30px;object-fit: cover" src='https://static.bingotalk.cn/bingodev/image/2024072615483836.svg' /><div style="line-height:30px;width:60%;">${data.subTitle}</div></div><div class='to_pythonTrain' style="position: absolute; right: 10%; bottom: 26%;  padding-left:10px; padding-right:10px; padding-top:8px;padding-bottom:8px; border: 2px solid #2D9CDB; border-radius: 5px; text-align: center; color: #2d9cdb;cursor: pointer;">开始实训</div></div></div>`
            editor.selection.setContent(media)
            const content = editor.getContent()
            editor.setContent(content)
          }
          if (type === 'edit') {
            editor.selection.moveToBookmark(bookmark)
            const media = `<div class='python_card mceNonEditable' data-id="${data.id}" style='width:100%;background: #E3F5FF;border-radius: 5px;box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);overflow:hidden;padding:30px;box-sizing: border-box;position:relative;margin-bottom:2px'><div class="info" style="display:none">${JSON.stringify(data)}</div><div style="color:#4F4F4F;font-size:20px;font-weight:700">${data.title}</div><div style="display:flex;margin-top:40px"><img style="width:30px;height: 30px;object-fit: cover" src='https://static.bingotalk.cn/bingodev/image/2024072615483836.svg' /><div style="line-height:30px;width:60%;">${data.subTitle}</div></div><div class='to_pythonTrain' style="position: absolute; right: 10%; bottom: 26%;  padding-left:10px; padding-right:10px; padding-top:8px;padding-bottom:8px; border: 2px solid #2D9CDB; border-radius: 5px; text-align: center; color: #2d9cdb;cursor: pointer;">开始实训</div></div></div>`
            editor.selection.setContent(media)
          }
          closePythonTraining()
        }
      })
    }
  })
}
