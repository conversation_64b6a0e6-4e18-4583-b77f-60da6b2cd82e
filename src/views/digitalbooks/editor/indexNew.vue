<template>
  <div class="editor-dig">
    <div class="dig-head">
      <div class="flex">
        <div class="pointer flex items-center" style="white-space: nowrap;" @click="goHome">
          <i class="el-icon-arrow-left"></i>
          返回
        </div>
        <div class="title">{{ bookTitle }}</div>
      </div>
      <div class="flex">
        <img class="word_button" src="@/assets/digitalbooks/read/aibutton.png" @click="showChat" />
        <div v-show="autoTips" class="dig-tips">自动保存中...</div>
        <div class="dig-btn" @click="preShowFn()">预览</div>
        <div class="dig-btn" @click="saveHtml(true)">保存</div>
        <div :style="!reviewData||isPublish?'background:#E0E0E0':''" class="dig-btn" @click="setReview()">{{ isPublish?'审核中':'提交审核' }}</div>
      </div>
    </div>
    <div class="dig-box">
      <div class="dig-left">
        <div class="chapter-title">
          <div class="icon1">
            <img src="../../../assets/digitalbooks/chapter.svg" />
            <span class="mulu">目录</span>
          </div>
          <div class="flex items-center">
            <div class="add-btn" @click="treeAppend(null , null)">
              +新建章节/任务
            </div>
            <!-- <i class="el-icon-s-fold pointer"></i> -->
            <!-- <i class="el-icon-s-unfold pointer"></i> -->
          </div>
        </div>
        <div v-loading="loading" element-loading-background="rgba(255, 255, 255, 0.3)" class="chapter-body">
          <el-tree
            ref="treeSelect"
            :data="bookTree"
            :props="treeProps"
            node-key="id"
            :current-node-key="currentKey"
            default-expand-all
            highlight-current
            :expand-on-click-node="false"
            :draggable="preNode!==null&&!isPublish"
            @node-drag-start="handleDragStart"
            @node-drop="handleDrop"
            @node-click="handleNodeClick"
          >
            <div slot-scope="{ node, data }" class="tree-body">
              <div class="chapter-name" :class="{'chapter-name-1' : data.depth === 0, 'chapter-name-3' : data.depth === 2}">
                <div :title="data.title" class="w article-singer-container">
                  {{ data.title }}
                </div>
              </div>
              <div class="chapter-option">
                <div v-if="data.bugQuantity !== 0" class="red_idot">
                  {{ data.bugQuantity }}
                </div>
                <el-tooltip class="item" effect="dark" content="新增" placement="top-start" v-if="data.depth !== 2">
                  <el-button
                    type="text"
                    size="mini"
                    :disabled="isPublish"
                    @click.stop="() => treeAppend(node, data)"
                  >
                    <svg-icon
                      class="svgIcon"
                      icon-class="add"
                      class-name="add"
                    />
                  </el-button>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="编辑" placement="top-start">
                  <el-button
                    type="text"
                    size="mini"
                    :disabled="isPublish"
                    @click.stop="() => treeEdit(node, data)"
                  >
                    <svg-icon
                      class="svgIcon"
                      icon-class="edit"
                      class-name="edit"
                    />
                  </el-button>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="删除" placement="top-start">
                  <el-button
                    type="text"
                    size="mini"
                    :disabled="isPublish"
                    @click.stop="() => treeRemove(node, data)"
                  >
                    <svg-icon
                      class="svgIcon"
                      icon-class="delete"
                      class-name="delete"
                    />
                  </el-button>
                </el-tooltip>
              </div>
            </div>
          </el-tree>
        </div>
      </div>
      <div v-loading="loading" element-loading-background="rgba(255, 255, 255, 0.3)" class="dig-center">
        <div v-show="otherUse" class="other-use">
          <div style="background-color: white;min-width: 1vw;position: absolute;left: 50%;top: 50%">其他作者正在编写中，无法进入编写</div>
        </div>
        <!-- <div class="toolbar">
          <Toolbar
            v-if="!loading"
            :editor="editor"
            :default-config="toolbarConfig"
            :mode="mode"
          />
        </div> -->
        <div class="eidtor">
          <tinymce-editor
            ref="tinyMceRef"
            :id="tinymceId"
            v-model="html"
            :init="init"
            :disabled="preNode===null||isPublish"
            @onFocus="onFocus"
            @input="onChange"
            @onBlur="onBlur"
          />
        </div>
      </div>
      <div class="right_content">
        <el-tabs v-model="activeName" stretch @tab-click="tabClick">
          <el-tab-pane label="审核意见" name="first">
            <div v-if="bugList.length === 0" class="w" style="height: 90%">
              <Empty :msg="'暂无数据'" style="transform: scale(0.6);" />
            </div>
            <div v-for="item in bugList" :key="item.id" class="bug_item">
              <div style='width: 100%;height: 100%;cursor: pointer' @click="toTop(item)">
                <div>
                  <el-tag class="tag" :type="formatType(item.bugStatus)">{{ formatTypeText(item.bugStatus) }}</el-tag>
                  <span style="margin-left: 10px;">{{ item.createdAt }} </span>
                  <el-button v-if="item.bugStatus=='OPEN'" style="margin-left: 10px;" type="text" :disabled="isPublish" @click.stop="fixed(item)">修改完成</el-button>
                </div>
                <div class="bug_text">引用：<div class='html_bug' v-html="item.text"></div>
                </div>
                <div class="bug_content">{{ item.bugDescription }}</div>
              </div>
              <el-collapse accordion>
                <el-collapse-item>
                  <template slot="title">
                    留言 <p class="add" @click="openMessage(item, false)">共有{{ item.commentQuantity }}条留言</p>
                  </template>
                  <div class="message_content">
                    <div v-for="(item1, index) in item.commentList" :key="index" class="message_item">
                      <div class="header">
                        <el-tag class="message_tag" size="mini" :type="item1.userRole=='作者'?'warning':''">{{ fomatterAuthor(item1.userRole) }}</el-tag>
                        <span style="margin-left: 5px;">{{ item1.createdAt }}</span>
                      </div>
                      <div class="message_content">{{ item1.content }}</div>
                    </div>
                  </div>
                  <div class="submit_message">
                    <el-input v-model="message" class="message_input" placeholder="请输入留言" />
                    <el-button class="message_button" type="primary" @click.stop="submit_message(item)">提交</el-button>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </el-tab-pane>
          <el-tab-pane label="编辑记录" name="second">
            <div v-if="CatalogueAuthorList.length === 0" class="w" style="height: 90%">
              <Empty :msg="'暂无数据'" style="transform: scale(0.6);" />
            </div>
            <div v-for="(item,index) in CatalogueAuthorList" :key="index" class="author_item">
              <p class="time">{{ formatDateTime(item.updatedAt) }}</p>
              <p class="author">{{ item.user.displayName }}</p>
              <el-button type="text" class="button button1" @click="showHistory(item)">查看内容</el-button>
              <el-button type="text" class="button button2" @click="setHistoryContent(item)">恢复</el-button>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <editChapter
      v-if="editChapterShow"
      :show="editChapterShow"
      :append-to-body="true"
      :node-info="currChapter"
      :showAIBtn="bookTree.length === 0"
      @close="editChapterShow = false"
      @handleAI="handleAI"
      @emitSucess="eidtDone"
    />
    <SetAiConfig ref="setAiRef" :book-title="bookTitle" :desc="bookInfo?bookInfo.intro:''" :ai-tip-props="aiTip" :pre-node="preNode" :tree-data="bookTree" @updateAITip="updateAITip" @confirm="aiConfigConfirm"/>
    <div v-if="preShow" class="pre">
      <Read :pre-mode="true" :node="preNode" :pre-publish-mode="true" @close="preShow = false" />
    </div>
    <tipsPop ref="tips" :position="tipsPositon" :info="tipsInfo" />
    <imgGroupPop ref="imgs" :info="imgListInfo" />
    <videoCardPop ref="videoCard" :info="videoInfo" />
    <chatDrawer ref="chatDrawer" :has-system="true" :menu-list="bookTree" :pre-node="preNode" @handleMenuClick="handleAI" @handleContentClick="handleAI('content')" @handleTrainingClick="handleTrainingClick"/>
    <showHistory ref="history" :content="historyContent" />
    <doTest ref="doTest" :test-id="testId" :ids="ids" />
    <officeView ref="officeView" :url="officeUrl" :ctoken="token" />
    <doExcelTraing ref="excelRraing" />
    <doAiTraing ref="aiTraining" />
    <CaseDetail ref='detailRef' />
  </div>
</template>

<script>
import CaseDetail from '@/views/digitalbooks/interactiveCase/caseDetail'
import SetAiConfig from '@/views/digitalbooks/editor/components/AiEditView/setAiConfig'
import Empty from '@/components/classPro/Empty/index.vue'
import tinymce from 'tinymce/tinymce'
import TinymceEditor from '@tinymce/tinymce-vue'
import 'tinymce/themes/silver/theme'
import chatDrawer from './components/aiChat.vue'
import defaultConfig from './utils/config'
import editChapter from './components/editChapter.vue'
import { getSqlPlatformToken } from '@/api/training-api'
import { getBook, deleteBookCatalogue, saveContent, getContentEditor, getBookCatalogueByVersion, dragCatalogue, takeBook, getDigitalContentChangeLog } from '@/api/digital-api.js'
import { getToken } from '@/utils/auth'
import Read from '@/views/digitalbooks/read/index.vue'
import { Notification } from 'element-ui'
import { throttle } from '@/utils/index'
import { saveAs } from 'file-saver'
import { uploadVideo } from './utils/video'
import { test } from './utils/test'
import { uploadAudio } from './utils/audio'
import { tips } from './utils/tips'
import { imgGroup } from './utils/imgGroup'
import { uploadVideoCard } from './utils/videoCard'
import { fileDownLoad } from './utils/fileDownload'
import { indent2em } from './utils/indent2em'
import { lineHeight } from './utils/lineHeight'
import tipsPop from './components/tipsPop.vue'
import { uploadImg } from './utils/uploadImg'
import { formateImg } from './utils/formateImg'
import { addIframe } from './utils/iframe'
import { excelTrain } from './utils/excelTrain'
import imgGroupPop from './components/imgGroupPop.vue'
import videoCardPop from './components/videoPop.vue'
import { mapGetters } from 'vuex'
import showHistory from './components/showHistory.vue'
import doTest from './components/doTest.vue'
import { trainingdPlugin } from './utils/addTraining'
import officeView from './components/officeView.vue'
import doExcelTraing from './components/doExcelTraing.vue'
import doAiTraing from './components/doAiTraing.vue'
import { aiTraining } from './utils/addAiTraining'
import { pythonTraining } from './utils/addPythonTraining'
import { addCase } from './utils/addCase'
// import { createTest } from '@/api/test-api'
// import { clear } from './utils/clearMark'
import { getDigitalBugList, changeDigitalBugStatus, getDigitalBookReview, submitDigitalBugComment, getDigitalBugCommentList, submitDigitalBookReview } from '@/api/publishing'
tinymce.PluginManager.add('uploadVideo', uploadVideo)
tinymce.PluginManager.add('uploadAudio', uploadAudio)
tinymce.PluginManager.add('formateImg', formateImg)
tinymce.PluginManager.add('imgGroup', imgGroup)
tinymce.PluginManager.add('lineHeight', lineHeight)
tinymce.PluginManager.add('uploadImg', uploadImg)
tinymce.PluginManager.add('indent2em', indent2em)
tinymce.PluginManager.add('tips', tips)
tinymce.PluginManager.add('uploadVideoCard', uploadVideoCard)
tinymce.PluginManager.add('fileDownLoad', fileDownLoad)
tinymce.PluginManager.add('test', test)
tinymce.PluginManager.add('customIframe', addIframe)
tinymce.PluginManager.add('trainingdPlugin', trainingdPlugin)
tinymce.PluginManager.add('pythonTraining', pythonTraining)
tinymce.PluginManager.add('excelTrain', excelTrain)
tinymce.PluginManager.add('aiTraining', aiTraining)
tinymce.PluginManager.add('addCase', addCase)

// tinymce.PluginManager.add('claerMark', clear)
import VueViewer from 'v-viewer'
import Vue from 'vue'
import { findParentByClass } from 'utils/dom'
import { addTestPopModal, setData } from '@/views/digitalbooks/editor/components/addTestPop'
import { createTest } from '@/api/test-api'
import store from '@/store'
import router from '@/router'
import { getTrainingPresetFile } from '@/api/course-api'
Vue.use(VueViewer)
VueViewer.setDefaults({
  title: (image) => image.alt || ''
})
export default {
  components: { CaseDetail, SetAiConfig, TinymceEditor, editChapter, Read, tipsPop, imgGroupPop, videoCardPop, chatDrawer, Empty, showHistory, doTest, officeView, doExcelTraing, doAiTraing },
  data () {
    return {
      tinymceId: '0',
      ids: '',
      testId: '0',
      contentDocument: null,
      editorBody: null,
      init: Object.assign(defaultConfig, {
        plugins: defaultConfig.plugins.replaceAll('autoresize', ''),
        setup: (editor) => {
          editor.on('init', () => {
            this.editorBody = editor.getBody()
            // this.renderEditorMath()
            this.contentDocument = editor.getDoc()
            this.contentDocument.addEventListener('keydown', this.copy)
          })
        }
      }),
      activeName: 'first',
      isPublish: false,
      message: '',
      tipsPositon: {
        top: 0,
        left: 0
      },
      videoInfo: {
        src: '',
        poster: '',
        text: ''
      },
      tipsInfo: {
        keyword: '',
        content: ''
      },
      preShow: false,
      editor: null,
      chapterShow: true,
      loading: false,
      html: '',
      newhtml: '',
      mode: 'default', // or 'simple'
      id: 1000,
      bookId: '',
      treeData: null,
      bookTitle: '',
      aiTip: false,
      bookTree: [],
      treeProps: {
        children: 'childCatalogue',
        label: 'title'
      },
      currChapter: null,
      editChapterShow: false,
      selectTreeId: 0,
      preNode: null,
      token: '',
      saveTimer: null,
      autoTips: false,
      otherUse: false,
      keepHeartTime: null,
      contentChanged: false,
      imgListInfo: null,
      bugList: [],
      CatalogueAuthorList: [],
      reviewData: null,
      bookInfo: null,
      historyContent: '',
      uuid: this.$route.query.uuid,
      openFlag: false,
      officeUrl: '',
      isLoading: false,
      currentKey: 0
    }
  },
  computed: {
    ...mapGetters({
      'userId': 'id'
    })
  },
  beforeUpdate() {
    if (this.$route.query.uuid) {
      this.uuid = this.$route.query.uuid
    } else {
      if (this.$route.query.path) {
        this.$router.push({ path: '/editor', query: { path: this.$route.query.path, id: this.$route.query.id, token: this.$route.query.token, uuid: this.getUUid() }})
      } else {
        this.$router.push({ path: '/editor', query: { id: this.$route.query.id, token: this.$route.query.token, uuid: this.getUUid() }})
      }
    }
  },
  async mounted () {
    this.token = getToken()
    this.token = `Bearer ${this.$route.query && this.$route.query.token}`
    this.bookId = this.$route.query && this.$route.query.id
    // this._takeBook()
    await this._getDigitalBookReview()
    this._getBook()
    this._getBookCatalogue()
    window.addEventListener('beforeunload', e => this.beforeunloadFn(e))

    tinymce.init({}).then(() => {
      setTimeout(() => {
        this.initFun()
      }, 1000)
    })
  },
  beforeDestroy () {
    // const editor = this.editor
    // if (editor == null) return
    this.saveHtml()
    if (this.keepHeartTime) {
      clearInterval(this.keepHeartTime)
    }
    if (this.saveTimer) {
      clearInterval(this.saveTimer)
    }
    this.keepHeartTime = null
    this.saveTimer = null
    this.contentDocument.removeEventListener('keydown', this.copy)
  },
  methods: {
    fomatterAuthor(role) {
      if (role === 'REVIEW_1') {
        return '一审'
      } else if (role === 'REVIEW_2') {
        return '二审'
      } else if (role === 'REVIEW_3') {
        return '三审'
      } else if (role === 'PROOFREAD') {
        return '校对'
      } else {
        return role
      }
    },
    async copy(event) {
      const isCtrlV = (event.ctrlKey || event.metaKey) && event.key === 'v'
      if (isCtrlV) {
        // 阻止默认粘贴行为（如果需要自定义处理）
        if (this.html === '') {
          event.preventDefault()
          if (navigator.clipboard) {
            const clipboardItems = await navigator.clipboard.read()
            // 遍历剪贴板项，寻找HTML格式内容
            for (const item of clipboardItems) {
              // 检查是否有HTML格式
              if (item.types.includes('text/html')) {
                const blob = await item.getType('text/html')
                const clipboardHtml = await blob.text()
                this.html = clipboardHtml
                return
              }
            }
            // 如果没有HTML格式，尝试获取纯文本
            const text = await navigator.clipboard.readText()
            this.html = text ? `<p>${text}</p>` : ''
          }
        }
      }
    },
    parseHtml(html) {
      const parser = new DOMParser()
      return parser.parseFromString(html, 'text/html')
    },
    hasElementRemovedTest(oldHtml, newHtml, className) {
      const oldDoc = this.parseHtml(oldHtml)
      const newDoc = this.parseHtml(newHtml)
      const oldElements = Array.from(oldDoc.querySelectorAll(`.${className}`))
        .map(el => el.getAttribute('data-id'))
      const newElements = Array.from(newDoc.querySelectorAll(`.${className}`))
        .map(el => el.getAttribute('data-id'))
        .filter(Boolean)
      const removedIds = oldElements.filter(id => !newElements.includes(id))
      return removedIds
    },
    hasElementRemovedTraing(oldHtml, newHtml, className) {
      const oldDoc = this.parseHtml(oldHtml)
      const newDoc = this.parseHtml(newHtml)
      const oldElements = Array.from(oldDoc.querySelectorAll(`.${className}`))
        .map(el => JSON.parse(el.getElementsByClassName('info')[0].innerHTML).id)
      const newElements = Array.from(newDoc.querySelectorAll(`.${className}`))
        .map(el => JSON.parse(el.getElementsByClassName('info')[0].innerHTML).id)
        .filter(Boolean)
      const removedIds = oldElements.filter(id => !newElements.includes(id))
      return removedIds
    },
    testElement(html) {
      const newDoc = this.parseHtml(html)
      const newElements = Array.from(newDoc.querySelectorAll('.test_card'))
        .map(el => el.getAttribute('data-id'))
        .filter(Boolean)
      console.log(newElements)
      return newElements.length === 0 ? '' : newElements.join(',')
    },
    traingElement(html) {
      const newDoc = this.parseHtml(html)
      const newElements = Array.from(newDoc.querySelectorAll('.excel_card')).concat(Array.from(newDoc.querySelectorAll('.ai_card')))
        .map(el => JSON.parse(el.getElementsByClassName('info')[0].innerHTML).id)
        .filter(Boolean)
      console.log(newElements)
      return newElements.length === 0 ? '' : newElements.join(',')
    },
    openExcelTraing(id) {
      this.$refs.excelRraing.open(id)
    },
    openAiTraing(id) {
      this.$refs.aiTraining.open(id)
    },
    async getTrainingPresetFile (trainingId) {
      const params = {
        trainingId: trainingId,
        userId: this.userId ? this.userId : null
      }
      await getTrainingPresetFile(params)
        .then(response => {
          if (response.code === 200) {
            window.open(`https://binguoketang.com/jupyterhub/hub/logout`, '_blank')
          } else {
            this.$message.error(response.message || '获取文件失败')
          }
        })
        .catch(err => {
          console.log(err)
          this.$message.error('获取文件失败')
        })
    },
    goHome() {
      if (this.$route.query.token.indexOf('Bearer') === -1) {
        window.close()
        return
      }
      if (this.$route.query.path) {
        this.$router.push({ path: this.$route.query.path, query: { path: '/author/home', bookId: this.bookId, token: this.token }})
      } else {
        this.$router.push({ path: '/author/home', query: { uuid: this.uuid }})
      }
    },
    getUUid() {
      let dt = new Date().getTime()
      const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = (dt + Math.random() * 16) % 16 | 0
        dt = Math.floor(dt / 16)
        return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16)
      })
      return uuid
    },
    tabClick(tab) {
      if (this.selectTreeId && tab.index === '1') {
        this._getDigitalContentChangeLog(this.selectTreeId)
      }
    },
    setHistoryContent(item) {
      this.$confirm('此操作将覆盖当前编辑的章节, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (!item.contentFileUrl || item.contentFileUrl === '') {
          this.html = ''
          this.saveHtml()
          return
        } else {
          fetch(item.contentFileUrl)
            .then(response => {
              if (!response.ok) {
                throw new Error('Network response was not ok ' + response.statusText)
              }
              return response.text()
            })
            .then(data => {
              this.html = data
              this.saveHtml()
            })
            .catch(() => {
            })
        }
      }).catch(() => {
      })
    },
    showHistory(item) {
      if (item.contentFileUrl && item.contentFileUrl !== '') {
        fetch(item.contentFileUrl)
          .then(response => {
            if (!response.ok) {
              throw new Error('Network response was not ok ' + response.statusText)
            }
            return response.text()
          })
          .then(data => {
            this.historyContent = data
            this.$refs.history.open()
          })
          .catch(() => {
          })
      } else {
        this.historyContent = ''
        this.$refs.history.open()
      }

    },
    formatDateTime(dateTimeString) {
      const dateTime = new Date(dateTimeString)
      const year = dateTime.getFullYear()
      const month = String(dateTime.getMonth() + 1).padStart(2, '0')
      const day = String(dateTime.getDate()).padStart(2, '0')
      const hours = String(dateTime.getHours()).padStart(2, '0')
      const minutes = String(dateTime.getMinutes()).padStart(2, '0')
      const seconds = String(dateTime.getSeconds()).padStart(2, '0')

      const formattedDateTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`

      return formattedDateTime
    },
    setReview() {
      const message = `<strong><p>作品名称：${this.bookInfo.title}</p><p style='margin-top:30px'>主编：${this.bookInfo.author}</p><p style='margin-top:30px'>出版社：<span style='color:#828282'>${this.bookInfo.publisher ? this.bookInfo.publisher : '提交后平台会给你安排已签约的出版社'}</span></p><p style='color:#EB5757;margin-top:30px'>*提交审核后不可修改教材内容</p></strong>`
      if (this.isPublish || !this.reviewData) { return }
      this.$confirm(message, '确定提交审核', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString: true
      }).then(() => {
        submitDigitalBookReview({ digitalBookId: this.bookId }, { authorization: this.token }).then(res => {
          if (res.code === 200) {
            this.$message.success('提交成功')
            this._getDigitalBookReview()
          }
        })
      })
    },
    /**
     * 打开消息
     *
     * @param item 消息项
     * @returns 无返回值
     */
    openMessage(item, refresh = true) {
      let canRefresh = true
      this.bugList.forEach((item1, index) => {
        if (item1.id === item.id) {
          if (this.bugList[index].commentList.length > 0){
            canRefresh = false
          }
        }
      })
      if (!canRefresh && !refresh) return
      getDigitalBugCommentList({ digitalBugId: item.id }, {
        authorization: this.token
      }).then(res => {
        const data = this.bugList
        data.forEach((item1, index) => {
          if (item1.id === item.id) {
            data[index].commentList = res.data
          }
        })
        this.bugList = data
      })
    },
    submit_message(item) {
      if (this.message === '') {
        this.$message.warning('请输入留言内容')
        return
      }
      submitDigitalBugComment({ digtalBugId: item.id, content: this.message }, {
        authorization: this.token
      }).then(res => {
        if (res.code === 200) {
          const data = this.bugList
          data.forEach((item1, index) => {
            if (item1.id === item.id) {
              data[index].commentQuantity += 1
            }
          })
          this.bugList = data
          this.bugList = JSON.parse(JSON.stringify(this.bugList))
          this.openMessage(item)
          this.message = ''
        }
      })
    },
    /**
     * 获取数字书籍的审核信息
     *
     * @returns Promise<void> 返回一个 Promise，用于异步获取数字书籍的审核信息
     */
    async _getDigitalBookReview() {
      getDigitalBookReview
      const { data } = await getDigitalBookReview({
        bookId: this.bookId
      }, {
        authorization: this.token
      })
      this.reviewData = data
      if (data && data.reviewStatusPublisher === 'UNDER_REVIEW' || data && data.reviewStatusExpert === 'UNDER_REVIEW') {
        this.isPublish = true
        this.$message.warning('当前书籍正在审核中，请耐心等待')
      } else {
        this.isPublish = false
      }
      // this.bookTitle = data.title
    },
    /**
     * 提交修改并固定数字缺陷
     *
     * @param item 数字缺陷对象
     * @returns 无返回值
     */
    fixed(item) {
      this.$confirm('是否要提交修改?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        changeDigitalBugStatus({ digitalBugId: item.id, bugStatus: 'FIXED' }, {
          authorization: this.token
        }).then(res => {
          if (res.code === 200) {
            this.$message.success('提交成功')
            this._getBookCatalogue()
            this.getBugList()
          }
        })
      })
    },
    flattenDOM(root) {
      const result = []
      const queue = [{ node: root, level: 0, index: 0, parentId: null }]
      let currentId = 0

      while (queue.length > 0) {
        const { node, level } = queue.shift()
        const nodeId = currentId++

        // 提取节点的关键信息
        const nodeInfo = {
          dataId: node.getAttribute('data-id'),
          node
        }

        result.push(nodeInfo)

        // 将子节点加入队列
        const children = Array.from(node.children)
        children.forEach((child, i) => {
          queue.push({
            node: child,
            level: level + 1,
            index: i,
            parentId: nodeId
          })
        })
      }
      return result
    },
    /**
     * 滚动到指定元素位置
     *
     * @param item 目标元素对象
     */
    toTop(item) {
      const elements = document.getElementsByClassName('tox-edit-area__iframe')[0].contentWindow.document.getElementsByClassName('set_review')
      for (let i = 0; i < elements.length; i++) {
        if (elements[i].getAttribute('data-id') === item.dataId) {
          const e = findParentByClass(elements[i], 'draggable-div')
          let top = elements[i].offsetTop
          if (e) {
            top = e.offsetTop + elements[i].offsetTop
          }
          document.getElementsByClassName('tox-edit-area__iframe')[0].contentWindow.scrollTo({
            top: top,
            behavior: 'smooth'
          })
          break
        }
        const list = this.flattenDOM(elements[i])
        for (let j = 0; j < list.length; j++) {
          if (list[j].dataId === item.dataId) {
            const e = findParentByClass(elements[i], 'draggable-div')
            let top = list[j].node.offsetTop
            if (e) {
              top = e.offsetTop + list[j].node.offsetTop
            }
            document.getElementsByClassName('tox-edit-area__iframe')[0].contentWindow.scrollTo({
              top: top,
              behavior: 'smooth'
            })
            break
          }
        }
      }
      if (item.postion.indexOf('mceNonEditable') !== -1) {
        const elements = document.getElementsByClassName('tox-edit-area__iframe')[0].contentWindow.document.getElementsByClassName('mceNonEditable')
        for (let i = 0; i < elements.length; i++) {
          if ((elements[i].outerHTML).indexOf('图片') !== -1 && (JSON.parse(item.postion).text.indexOf('图片')) !== -1) {
            const arr = this.getSrc(elements[i].outerHTML)
            const arr1 = this.getSrc(JSON.parse(item.postion).text)
            if (arr[0] === arr1[0]) {
              const top = elements[i].offsetTop
              document.getElementsByClassName('tox-edit-area__iframe')[0].contentWindow.scrollTo({
                top: top,
                behavior: 'smooth'
              })
              break
            }
          }
          if ((elements[i].outerHTML).indexOf('视频') !== -1 && (JSON.parse(item.postion).text.indexOf('视频')) !== -1) {
            const arr = this.getVideoSrc(elements[i].outerHTML)
            const arr1 = this.getVideoSrc(JSON.parse(item.postion).text)
            if (arr[0] === arr1[0]) {
              const top = elements[i].offsetTop
              document.getElementsByClassName('tox-edit-area__iframe')[0].contentWindow.scrollTo({
                top: top,
                behavior: 'smooth'
              })
              break
            }
          }
          if ((elements[i].outerHTML).indexOf('去做题') !== -1 && (JSON.parse(item.postion).text.indexOf('去做题')) !== -1) {
            const arr = this.getDataId(elements[i].outerHTML)
            const arr1 = this.getDataId(JSON.parse(item.postion).text)
            if (arr[0] === arr1[0]) {
              const top = elements[i].offsetTop
              document.getElementsByClassName('tox-edit-area__iframe')[0].contentWindow.scrollTo({
                top: top,
                behavior: 'smooth'
              })
              break
            }
          }
          if ((elements[i].outerHTML).indexOf('操作实训') !== -1 && (JSON.parse(item.postion).text.indexOf('操作实训')) !== -1) {
            const arr = this.getDataId(elements[i].outerHTML)
            const arr1 = this.getDataId(JSON.parse(item.postion).text)
            if (arr[0] === arr1[0]) {
              const top = elements[i].offsetTop
              document.getElementsByClassName('tox-edit-area__iframe')[0].contentWindow.scrollTo({
                top: top,
                behavior: 'smooth'
              })
              break
            }
          }
          if ((elements[i].outerHTML).indexOf('操作实验') !== -1 && (JSON.parse(item.postion).text.indexOf('操作实验')) !== -1) {
            const arr = this.getDataId(elements[i].outerHTML)
            const arr1 = this.getDataId(JSON.parse(item.postion).text)
            if (arr[0] === arr1[0]) {
              const top = elements[i].offsetTop
              document.getElementsByClassName('tox-edit-area__iframe')[0].contentWindow.scrollTo({
                top: top,
                behavior: 'smooth'
              })
              break
            }
          }
          if ((elements[i].outerHTML).indexOf('人工智能实训') !== -1 && (JSON.parse(item.postion).text.indexOf('人工智能实训')) !== -1) {
            const arr = this.getDataId(elements[i].outerHTML)
            const arr1 = this.getDataId(JSON.parse(item.postion).text)
            if (arr[0] === arr1[0]) {
              const top = elements[i].offsetTop
              document.getElementsByClassName('tox-edit-area__iframe')[0].contentWindow.scrollTo({
                top: top,
                behavior: 'smooth'
              })
              break
            }
          }
          if ((elements[i].outerHTML).indexOf('python实训') !== -1 && (JSON.parse(item.postion).text.indexOf('python实训')) !== -1) {
            const arr = this.getDataId(elements[i].outerHTML)
            const arr1 = this.getDataId(JSON.parse(item.postion).text)
            if (arr[0] === arr1[0]) {
              const top = elements[i].offsetTop
              document.getElementsByClassName('tox-edit-area__iframe')[0].contentWindow.scrollTo({
                top: top,
                behavior: 'smooth'
              })
              break
            }
          }
          if ((elements[i].outerHTML).indexOf('附件') !== -1 && (JSON.parse(item.postion).text.indexOf('附件')) !== -1) {
            const arr = this.getSrc(elements[i].outerHTML)
            const arr1 = this.getSrc(JSON.parse(item.postion).text)
            if (arr[0] === arr1[0]) {
              const top = elements[i].offsetTop
              document.getElementsByClassName('tox-edit-area__iframe')[0].contentWindow.scrollTo({
                top: top,
                behavior: 'smooth'
              })
              break
            }
          }
          if (elements[i].className.includes('tips_item') && JSON.parse(item.postion).text.includes('tips_item')) {
            if (this.htmlContentsEqual(elements[i].outerHTML, JSON.parse(item.postion).text)) {
              const top = elements[i].offsetTop
              document.getElementsByClassName('tox-edit-area__iframe')[0].contentWindow.scrollTo({
                top: top,
                behavior: 'smooth'
              })
              break
            }
          }
          if (elements[i].className.includes('case_card') && JSON.parse(item.postion).text.includes('case_card')) {
            const arr = this.getDataId(elements[i].outerHTML)
            const arr1 = this.getDataId(JSON.parse(item.postion).text)
            if (arr[0] === arr1[0]) {
              const top = elements[i].offsetTop
              document.getElementById('tinymce_ifr').contentWindow.scrollTo({
                top: top,
                behavior: 'smooth'
              })
              break
            }
          }
        }
      }
      const mathsElements = document.getElementsByClassName('tox-edit-area__iframe')[0].contentWindow.document.getElementsByClassName('math-tex')
      for (let i = 0; i < mathsElements.length; i++) {
        if (item.text.includes(mathsElements[i].getAttribute('data-latex'))) {
          const top = mathsElements[i].offsetTop
          document.getElementsByClassName('tox-edit-area__iframe')[0].contentWindow.scrollTo({
            top: top,
            behavior: 'smooth'
          })
          break
        }
      }
      if (this.isTable(JSON.parse(item.postion).text)) {
        const elements = document.getElementsByClassName('tox-edit-area__iframe')[0].contentWindow.document.getElementsByTagName('table')
        for (let i = 0; i < elements.length; i++) {
          if (this.compareTableWithElement(JSON.parse(item.postion).text, elements[i])) {
            const top = elements[i].offsetTop
            document.getElementsByClassName('tox-edit-area__iframe')[0].contentWindow.scrollTo({
              top: top,
              behavior: 'smooth'
            })
            break
          }
        }
      }
    },
    isTable(html) {
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = html
      const tableFromHtml = tempDiv.querySelector('table')

      // 检查是否找到表格
      if (!tableFromHtml) {
        return false
      }
      return true
    },
    // 比较表格
    compareTableWithElement(htmlTable, targetElement) {
      // 创建临时元素解析 HTML 表格字符串
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = htmlTable
      const tableFromHtml = tempDiv.querySelector('table')
      // 检查是否找到表格
      if (!tableFromHtml) {
        return false
      }

      // 获取两个表格的所有行
      const rowsFromHtml = Array.from(tableFromHtml.querySelectorAll('tr'))
      const rowsFromElement = Array.from(targetElement.querySelectorAll('tr'))

      // 比较行数
      if (rowsFromHtml.length !== rowsFromElement.length) {
        return false
      }
      // 逐行比较
      for (let i = 0; i < rowsFromHtml.length; i++) {
        const cellsFromHtml = Array.from(rowsFromHtml[i].querySelectorAll('td, th'))
        const cellsFromElement = Array.from(rowsFromElement[i].querySelectorAll('td, th'))

        // 比较单元格数量
        if (cellsFromHtml.length !== cellsFromElement.length) {
          return false
        }

        // 逐单元格比较内容
        for (let j = 0; j < cellsFromHtml.length; j++) {
          const htmlCellContent = cellsFromHtml[j].textContent.trim()
          const elementCellContent = cellsFromElement[j].textContent.trim()

          // 比较单元格内容（忽略空格差异）
          if (htmlCellContent !== elementCellContent) {
            return false
          }
        }
      }
      // 如果所有检查都通过，则内容相同
      return true
    },
    // 比较气泡
    htmlContentsEqual(html1, html2) {
      const div1 = document.createElement('div')
      const div2 = document.createElement('div')
      div1.innerHTML = html1
      div2.innerHTML = html2

      // 获取tips_item中的可见文本（不包含子元素）
      function getVisibleText(div) {
        const tipsItem = div.querySelector('.tips_item')
        if (!tipsItem) return ''
        return Array.from(tipsItem.childNodes)
          .filter(node => node.nodeType === Node.TEXT_NODE)
          .map(node => node.textContent.trim())
          .join('')
      }

      // 获取tips_content中的隐藏文本
      function getHiddenText(div) {
        const tipsContent = div.querySelector('.tips_content')
        return tipsContent ? tipsContent.textContent.trim() : ''
      }

      const visible1 = getVisibleText(div1)
      const visible2 = getVisibleText(div2)
      const hidden1 = getHiddenText(div1)
      const hidden2 = getHiddenText(div2)

      return visible1 === visible2 && hidden1 === hidden2
    },
    getDataId(val) {
      const content = val
      const imgReg = /<div.*?(?:>|\/>)/gi
      const srcReg = /data-id=[\'\"]?([^\'\"]*)[\'\"]?/i
      const arr = content.match(imgReg)
      console.log(arr)
      const srcList = []
      if (arr != null) {
        for (let i = 0; i < arr.length; i++) {
          const src = arr[i].match(srcReg)
          if (src != null) {
            srcList.push(src[1])
          }
        }
        return srcList
      }
      return []
    },
    getVideoSrc(val) {
      const content = val
      const videoReg = /<video.*?(?:>|\/>)/gi // 匹配图片中的img标签
      const srcReg = /src=[\'\"]?([^\'\"]*)[\'\"]?/i // 匹配图片中的src
      const arr = content.match(videoReg) // 筛选出所有的img
      const srcList = []
      if (arr != null) {
        for (let i = 0; i < arr.length; i++) {
          const src = arr[i].match(srcReg)
          srcList.push(src[1])
        }
        return srcList
      }
      return []
    },
    getSrc (val) {
      try {
        const content = val
        const imgReg = /<img.*?(?:>|\/>)/gi // 匹配图片中的img标签
        const srcReg = /src=[\'\"]?([^\'\"]*)[\'\"]?/i // 匹配图片中的src
        const arr = content.match(imgReg) // 筛选出所有的img
        const srcList = []
        console.log(arr)
        if (arr != null) {
          for (let i = 0; i < arr.length; i++) {
            const src = arr[i].match(srcReg)
            srcList.push(src[1])
          }
          return srcList
        }
        return []
      } catch {
        return []
      }
    },
    /**
     * 格式化类型
     *
     * @param type 类型
     * @returns 格式化后的类型，如果为 'OPEN' 则返回 'danger'，如果为 'FIXED' 则返回 'success'
     */
    formatType(type) {
      if (type === 'OPEN') {
        return 'danger'
      }
      if (type === 'FIXED') {
        return 'success'
      }
    },

    /**
     * 格式化类型文本
     *
     * @param type 类型字符串
     * @returns 格式化后的文本
     */
    formatTypeText(type) {
      if (type === 'OPEN') {
        return '未解决'
      }
      if (type === 'FIXED') {
        return '已解决'
      }
    },
    /**
     * 获取数字书籍bug列表
     *
     * @returns Promise<void> 异步操作，无返回值
     */
    async getBugList() {
      const { data } = await getDigitalBugList({ digitalBookId: Number(this.bookId), catalogueId: this.selectTreeId },
        {
          authorization: this.token
        })
      if (!data) return
      data.forEach(item => {
        item.dataId = JSON.parse(item.postion).id
        item.text = JSON.parse(item.postion).text
        item.commentList = []
      })
      this.bugList = data
      this.$nextTick(() => {
        // 渲染所有包含公式的元素
        const mathElements = document.querySelectorAll('.math-tex')
        if (mathElements.length > 0) {
          window.MathJax.typesetPromise(mathElements).catch((err) => {
            console.error('MathJax typeset error:', err)
          })
        }
      })
    },
    showChat () {
      this.$refs.chatDrawer.open()
    },
    checkBase64 (val) {
      try {
        const content = val
        const imgReg = /<img.*?(?:>|\/>)/gi // 匹配图片中的img标签
        const srcReg = /src=[\'\"]?([^\'\"]*)[\'\"]?/i // 匹配图片中的src
        const arr = content.match(imgReg) // 筛选出所有的img
        if (arr != null) {
          for (let i = 0; i < arr.length; i++) {
            const src = arr[i].match(srcReg)
            // 获取图片地址判断是否是base64格式
            if (src[1].indexOf('base64') !== -1) {
              return true
            }
          }
        }
        return false
      } catch {
        // this.$message.warning('请检查图片格式是否正确')
        return false
      }
    },
    closePop () {
      if (this.$refs.tips) { this.$refs.tips.close() }
    },
    initFun () {
      if (!document.getElementsByClassName('tox-edit-area__iframe').length) {
        setTimeout(() => {
          this.initFun()
        }, 500)
        return
      }
      document.getElementsByClassName('tox-edit-area__iframe')[0].contentWindow.document.addEventListener('click', this.richEvent, false)
      document.getElementsByClassName('tox-edit-area__iframe')[0].contentWindow.document.addEventListener('dblclick', this.showImageList, false)
      // document.getElementsByClassName('tox-edit-area__iframe')[0].contentWindow.document.addEventListener('wheel', this.setPage, false)
      document.addEventListener('click', this.closePop)
    },
    setPage(event) {
      if (this.isLoading) {
        return
      }
      const container = document.getElementsByClassName('tox-edit-area__iframe')[0].contentWindow.document.documentElement
      const scrollTop = container.scrollTop // 当前滚动位置
      const scrollHeight = container.scrollHeight // 内容总高度
      const clientHeight = container.clientHeight // 可视区域高度
      console.log(scrollTop, scrollHeight, clientHeight)
      console.log(event)
      if (event.deltaY > 0) { // 向下滚动
        // 如果已经在底部，加载下一章
        if (scrollTop + clientHeight >= scrollHeight) {
          this.loadNextChapter()
        }
      } else if (event.deltaY < 0) { // 向上滚动
        // 如果已经在顶部，加载上一章
        if (scrollTop === 0) {
          this.loadPreviousChapter()
        }
      }
    },
    async loadPreviousChapter() {
      console.log('加载上一章')
      const preNode = this.findPreviousNode(this.bookTree, this.selectTreeId)
      if (preNode) {
        this.isLoading = true
        await this.handleNodeClick(preNode)
        setTimeout(() => {
          this.isLoading = false
        }, 1000)
      }
    },

    // 加载下一章
    async loadNextChapter() {
      console.log('加载下一章')
      const nextNode = this.findNextNode(this.bookTree, this.selectTreeId)
      if (nextNode) {
        this.isLoading = true
        await this.handleNodeClick(nextNode)
        setTimeout(() => {
          this.isLoading = false
        }, 1000)
      }
    },
    findNextNode(tree, targetId) {
      const flattenedArray = []

      function flatten(node) {
        flattenedArray.push(node)
        if (node.childCatalogue) {
          node.childCatalogue.forEach(flatten)
        }
      }

      tree.forEach(flatten)

      for (let i = 0; i < flattenedArray.length; i++) {
        if (flattenedArray[i].id === targetId) {
          return flattenedArray[i + 1] || null
        }
      }

      return null
    },
    findPreviousNode(tree, targetId) {
      const flattenedArray = []

      function flatten(node) {
        flattenedArray.push(node)
        if (node.childCatalogue) {
          node.childCatalogue.forEach(flatten)
        }
      }

      tree.forEach(flatten)

      for (let i = 0; i < flattenedArray.length; i++) {
        if (flattenedArray[i].id === targetId) {
          return flattenedArray[i - 1] || null
        }
      }

      return null
    },
    showImageList (e) {
      if (!e.target || !e.target.classList) {
        return
      }
      if (e.target.classList.contains('img_card_button')) {
        this.imgListInfo = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText)
        this.$refs.imgs.open()
      }
      if (e.target.classList.contains('video_button')) {
        this.videoInfo = {
          src: e.target.src,
          poster: e.target.poster,
          text: e.target.parentNode.getElementsByTagName('p')[1].innerText
        }
        this.$refs.videoCard.open()
      }
    },
    richEvent (e) {
      if (this.$refs.tips) {
        this.$refs.tips.close()
      }
      if (!e.target || !e.target.classList) {
        return
      }
      if (e.target.classList.contains('tips_item')) {
        const item = e.target
        const view = document.getElementsByClassName('tox-edit-area__iframe')[0]
        let y = item.getBoundingClientRect().top + view.getBoundingClientRect().top + 20
        if (window.innerHeight - e.pageY < 195) {
          y = window.innerHeight - 200
        }
        this.tipsPositon = {
          top: y,
          left: item.getBoundingClientRect().left + view.getBoundingClientRect().left
        }
        this.tipsInfo = {
          keyword: item.innerText,
          content: item.children[0].innerText
        }
        this.$refs.tips.show()
      }

      if (e.target.parentNode && e.target.parentNode.parentNode && e.target.parentNode.parentNode.classList.contains('file_download')) {
        if (e.target.classList.contains('download_button')) {
          const url = e.target.parentNode.children[1].innerText
          const fileName = e.target.parentNode.children[2].innerText
          const notif = Notification({
            title: fileName,
            dangerouslyUseHTMLString: true,
            message: '',
            duration: 0
          })
          throttle(function () {
            const xhr = new XMLHttpRequest()
            xhr.open('get', url)
            xhr.responseType = 'blob'
            xhr.addEventListener('progress', (e) => {
              const complete = Math.floor(e.loaded / e.total * 100)
              notif.message = complete + '%'
              if (complete >= 100) {
                notif.close()
              }
            })
            xhr.send()
            xhr.onload = function () {
              if (this.status === 200 || this.status === 304) {
                const blob = new Blob([this.response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8' })
                saveAs(blob, fileName)
              }
            }
          }, 2000)
        } else if (e.target.classList.contains('show_button')) {
          const item = e.target
          this.downloadFun(item)
        } else {
          const item = e.target.parentNode.parentNode.children[3].children[0]
          this.downloadFun(item)
        }
      }
      if (e.target.classList.contains('do_test')) {
        this.testId = e.target.parentNode.getAttribute('data-id')
        this.ids = e.target.parentNode.getAttribute('data-ids')
        this.$refs.doTest.open()
      }
      if (e.target.classList.contains('to_training')) {
        const type = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText).type
        if (type === 'sql') {
          if (this.openFlag) {
            return
          }
          this.openFlag = true
          this.$message.success({ duration: 3000, message: '正在跳转请稍后...' })
          getSqlPlatformToken({}, { authorization: this.token }).then(res => {
            this.openFlag = false
            window.open(res.data)
          })
        }
      }
      if (e.target.classList.contains('to_excel')) {
        const id = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText).id
        this.openExcelTraing(id)
      }
      if (e.target.classList.contains('to_aiTrain')) {
        const id = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText).id
        this.openAiTraing(id)
      }
      if (e.target.classList.contains('to_pythonTrain')) {
        const id = JSON.parse(e.target.parentNode.getElementsByClassName('info')[0].innerText).id
        this.getTrainingPresetFile(id)
      }
      if (e.target.classList.contains('case-btn')) {
        const obj = JSON.parse(e.target.parentNode.parentNode.getElementsByClassName('info')[0].innerText)
        this.$refs.detailRef.open({
          title: obj.title,
          practiceUrl: obj.data.practiceUrl,
          id: obj.id
        })
      }
    },
    isFileSizeGreaterThan200MB(sizeStr) {
      const sizeUnit = sizeStr.slice(-2).toUpperCase() // 获取单位
      const sizeValue = parseFloat(sizeStr) // 获取数值

      // 将大小转换为 MB
      let sizeInMB

      switch (sizeUnit) {
        case 'GB':
          sizeInMB = sizeValue * 1024
          break
        case 'MB':
          sizeInMB = sizeValue
          break
        case 'KB':
          sizeInMB = sizeValue / 1024
          break
        case 'B':
          sizeInMB = sizeValue / (1024 * 1024)
          break
        default:
          throw new Error('Unsupported size unit')
      }

      return sizeInMB > 200 // 判断是否大于 200MB
    },
    downloadFun(item) {
      const url = item.parentNode.children[1].innerText
      const fileName = item.parentNode.children[2].innerText
      const size = item.parentNode.parentNode.children[2].children[1].innerText
      if (this.getFileType(fileName) === 'video') {
        this.videoInfo = {
          src: url,
          poster: '',
          text: ''
        }
        this.$refs.videoCard.open()
      } else if (this.getFileType(fileName) === 'Office') {
        if (this.isFileSizeGreaterThan200MB(size)) {
          this.$message.warning('暂不支持大于200M的附件预览')
          return
        }
        this.officeUrl = url
        this.$nextTick(() => {
          this.$refs.officeView.open()
        })
      } else if (this.getFileType(fileName) === 'img') {
        this.imgListInfo = { content: [{ src: url, info: '' }] }
        this.$refs.imgs.open()
      } else {
        this.$message.warning('该类型文件暂不支持预览')
      }
    },
    getFileType(fileName) {
      // 获取文件后缀名
      const extension = fileName.split('.').pop().toLowerCase()
      // 定义不同类型的文件后缀
      const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp']
      const videoExtensions = ['mp4', 'avi', 'mov', 'mkv', 'flv', 'wmv']
      const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a']
      const officeExtensions = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf']
      const archiveExtensions = ['zip', 'rar', '7z', 'tar', 'gz']

      // 判断文件类型
      if (imageExtensions.includes(extension)) {
        return 'img'
      } else if (videoExtensions.includes(extension)) {
        return 'video'
      } else if (audioExtensions.includes(extension)) {
        return '音频'
      } else if (officeExtensions.includes(extension)) {
        return 'Office'
      } else if (archiveExtensions.includes(extension)) {
        return '压缩文件'
      } else {
        return '其他类型'
      }
    },
    async _takeBook () {
      // 判断是否有人正在使用该书
      const { data } = await takeBook({
        bookId: this.bookId,
        scene: 'BOOK_CATALOGUE_OWN',
        catalogueId: this.selectTreeId,
        webTabUuid: this.uuid
      }, {
        authorization: this.token
      })
      if (!data) {
        this.otherUse = true
        this.editor.blur()
      } else {
        if (this.otherUse) {
          this._getContent(this.selectTreeId)
        }
        this.otherUse = false
        // this.editor.enable()
      }
    },
    async _getBook () {
      if (this.bookId) {
        const { data } = await getBook({
          bookId: this.bookId,
          scene: 'BOOK_CATALOGUE_OWN',
          digitalBookReviewId: this.reviewData.id
        }, {
          authorization: this.token
        })
        this.bookTitle = data.title
        this.bookInfo = data
      }
    },
    async _getBookCatalogue () {
      const { data } = await getBookCatalogueByVersion({
        bookId: this.bookId,
        type: 'CHAPTER'
        // approvedOnly: false
      }, {
        authorization: this.token
      })
      this.bookTree = this.addDepthToTree(data)
      if (this.bookTree.length === 0) {
        this.treeAppend(null , null)
      }
    },
    addDepthToTree (tree, currentDepth = 0) {
      // 确保输入是数组
      if (!Array.isArray(tree)) {
        return tree
      }
      // 遍历每个节点
      return tree.map(node => {
        // 为当前节点添加深度属性
        const nodeWithDepth = {
          ...node,
          depth: currentDepth
        }
        // 如果有子节点，递归处理并增加深度
        if (node.childCatalogue && Array.isArray(node.childCatalogue)) {
          nodeWithDepth.childCatalogue = this.addDepthToTree(node.childCatalogue, currentDepth + 1)
        }
        return nodeWithDepth
      })
    },
    onCreated (editor) {
      // window.editor = this.editor
      // window.slateTransforms = this.slateTransforms
      this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
      if (!this.selectTreeId) {
        editor.disable()
      }
    },
    getHtml () {
      // console.log(tinymce.activeEditor.isDirty())
      return tinymce.activeEditor.getContent()
    },
    async handleNodeClick (nodeData) {
      if (!tinymce.activeEditor || this.loading) return
      if (this.saveTimer) {
        clearInterval(this.saveTimer)
        this.saveTimer = null
      }
      await this.saveHtml()
      this.loading = true
      if (this.checkBase64(this.getHtml())) {
        this.$message.warning('还有未保存的编辑，请稍候')
        return
      }
      this.preNode = nodeData
      this.selectTreeId = nodeData.id
      this.currentKey = nodeData.id
      const editorComponent = this.$refs.tinyMceRef
      if (editorComponent && editorComponent.editor) {
        editorComponent.editor.undoManager.clear()
      }
      this.getBugList()
      // 判断是否加锁
      if (this.keepHeartTime) {
        clearInterval(this.keepHeartTime)
      }
      this._takeBook()
      this.keepHeartTime = setInterval(() => {
        this._takeBook()
      }, 5000)
      setTimeout(async () => {
        await this._getContent(nodeData.id, nodeData)
        this.$nextTick(() => {
          this.$refs.treeSelect.setCurrentKey(this.currentKey)
        })
        this.loading = false
        // tinymce.activeEditor.focus()
        this._getDigitalContentChangeLog(nodeData.id)
        this.contentChanged = false
        setTimeout(() => {
          this.initFun()
        }, 1000)
      }, 0)
    },
    async _getDigitalContentChangeLog(id) {
      const { data } = await getDigitalContentChangeLog({ catalogueId: id, pageNo: 1, pageSize: 1000 }, {
        authorization: this.token
      })
      this.CatalogueAuthorList = data.content
    },
    async _getContent (id, nodeData = null) {
      if (this.selectTreeId === id) {
        const { data } = await getContentEditor({
          catalogueId: id
        }, {
          authorization: this.token
        })
        if (data && data.length > 0) {
          this.htmlId = data[0].id
          this.html = data[0].data
          // this.editor.setHtml(data[0].data)
        } else {
          this.htmlId = 0
          this.html = ''
          if (!this.aiTip && nodeData && nodeData.depth !== 0) {
            this.handleAI('content')
          }
          // this.editor.setHtml('<p>编辑器创建时的默认内容。</p>')
        }
        this.catalogueId = id
        this.$store.dispatch('app/setCatalogueId', id)
        this.tinymceId = String(id)
        this.htmlTemp = this.html
      }
    },
    handleDragStart (node, ev) {
      console.log('drag start', node)
    },
    async handleDrop (draggingNode, dropNode, dropType, ev) {
      if (dropNode.data.depth === 2) {
        this.$message.warning('该章节下不能再添加子章节')
        this._getBookCatalogue()
        return
      }
      // console.log('tree drop: ', draggingNode.data, dropNode.data, dropType)
      await dragCatalogue({
        catalogueId: draggingNode.data.id,
        referCatalogueId: dropNode.data.id,
        position: dropType.toUpperCase()
      }, {
        authorization: this.token
      })
      this._getBookCatalogue()
    },
    eidtDone (data) {
      this.editChapterShow = false
      this._getBookCatalogue()
      // if (this.currChapter.data) {
      //   // 编辑
      //   this.treeData.title = data.title
      //   // this._getContent(this.treeData.id)
      // } else {
      //   // 新增
      //   if (!this.currChapter.parentId) {
      //     // 根目录
      //     this.bookTree.push(data)
      //   } else {
      //     const newChild = data
      //     this.treeData.childCatalogue.push(newChild)
      //   }
      // }
    },
    treeAppend (node, data) {
      if (this.isPublish) return
      this.treeData = data
      if (node) {
        this.currChapter = { bookId: this.bookId, parentId: data.id, data: null }
      } else {
        this.currChapter = { bookId: this.bookId, parentId: 0, data: null }
      }
      this.editChapterShow = true
    },
    treeEdit (node, data) {
      this.treeData = data
      this.currChapter = { bookId: this.bookId, parentId: data.parentId, data }
      this.editChapterShow = true
    },
    async treeRemove (node, data) {
      try {
        this.$confirm('确认删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          await deleteBookCatalogue({
            catalogueId: data.id
          }, {
            authorization: this.token
          })
          this._getBookCatalogue()
          // const parent = node.parent
          // const children = parent.data.childCatalogue || parent.data
          // const index = children.findIndex(d => d.id === data.id)
          // children.splice(index, 1)
        }).catch(() => {
          // console.log('')
        })
      } catch (error) {
        console.log(error)
      }
    },
    deleteDiv(htmlString, targetClasses) {
      // 创建一个临时DOM容器
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = htmlString
      targetClasses.forEach(selector => {
        // 查找所有匹配的div元素
        const targetDivs = tempDiv.querySelectorAll(selector)
        targetDivs.forEach(div => {
          div.parentNode.removeChild(div)
        })
      })
      const targetDivs = tempDiv.querySelectorAll('.draggable-div')
      targetDivs.forEach(div => {
        div.classList.remove('draggable-div-selected')
      })
      // 返回修改后的HTML字符串
      return tempDiv.innerHTML
    },
    async saveHtml (type = false) {
      // if (!tinymce.activeEditor.isDirty()) {
      //   return
      // }
      if (tinymce.activeEditor && this.selectTreeId && !this.loading && this.catalogueId !== 0 && !this.otherUse && this.selectTreeId === this.catalogueId) {
        if (this.checkBase64(this.getHtml())) {
          return
        }
        const newHtml = this.deleteDiv(this.getHtml(), ['.resize-handle', '.move-handle'])
        const obj = {
          data: newHtml,
          catalogueId: this.catalogueId,
          webTabUuid: this.uuid,
          testpaperIdList: this.testElement(newHtml),
          trainingIdList: this.traingElement(newHtml),
          digitalCatalogueData: {
            videoQuantity: this.findVideo(newHtml),
            attachFileQuantity: this.findFile(newHtml),
            imgQuantity: this.findImg(newHtml),
            audioQuantity: this.findAudio(newHtml),
            trainingQuantity: this.findTraining(newHtml)
          }
        }
        if (obj.data === this.htmlTemp) {
          console.log('文本内容一样，不提交')
          if (type) {
            this.$message.success('保存成功')
          }
          return
        }
        // const testDeleteArr = this.hasElementRemovedTest(this.htmlTemp, obj.data, 'test_card')
        // // const traningDeleteArr = this.hasElementRemovedTraing(this.htmlTemp, obj.data, 'training_card')
        // const excelDeleteArr = this.hasElementRemovedTraing(this.htmlTemp, obj.data, 'excel_card')
        // testDeleteArr.forEach(item => {
        //   createTest({
        //     apiType: 'delete',
        //     testPaperId: item
        //   }, {
        //     authorization: this.token
        //   })
        // })
        // excelDeleteArr.forEach(item => {
        //   deleteTraining({
        //     apiType: 'delete'
        //   },
        //   {
        //     trainingId: item
        //   },
        //   {
        //     authorization: this.token
        //   })
        // })
        // console.log(testDeleteArr, excelDeleteArr)
        if (this.htmlId) {
          obj.id = this.htmlId
        }
        const { data } = await saveContent(obj, {
          authorization: this.token
        })
        // tinymce.activeEditor.isNotDirty = true
        if (type) {
          this.$message.success('保存成功')
        }
        this.htmlTemp = obj.data
        this.htmlId = data
      }
    },
    findTraining(htmlStr) {
      const parser = new DOMParser()
      const doc = parser.parseFromString(htmlStr, 'text/html')
      const Training1 = doc.querySelectorAll('.excel_card').length
      const Training2 = doc.querySelectorAll('.training_card').length
      const Training3 = doc.querySelectorAll('.ai_card').length
      return Training1 + Training2 + Training3
    },
    findAudio(htmlStr) {
      const parser = new DOMParser()
      const doc = parser.parseFromString(htmlStr, 'text/html')
      const Audio = doc.querySelectorAll('audio').length
      return Audio
    },
    findFile(htmlStr) {
      const parser = new DOMParser()
      const doc = parser.parseFromString(htmlStr, 'text/html')
      const fileCard = doc.querySelectorAll('.file_download').length
      return fileCard
    },
    findVideo(htmlStr) {
      const parser = new DOMParser()
      const doc = parser.parseFromString(htmlStr, 'text/html')
      const videoLength = doc.querySelectorAll('video').length
      return videoLength
    },
    findImg(htmlStr) {
      const parser = new DOMParser()
      const doc = parser.parseFromString(htmlStr, 'text/html')
      const images = doc.querySelectorAll('img')
      let validImagesCount = 0

      images.forEach((img) => {
        const parent = img.parentElement
        const grandparent = parent ? parent.parentElement : null

        // 检查父级或祖父级是否包含 mceNonEditable 类
        const parentHasClass = parent && parent.classList.contains('mceNonEditable')
        const grandparentHasClass = grandparent && grandparent.classList.contains('mceNonEditable')

        // 仅当父级和祖父级都不包含 mceNonEditable 时，计入
        if (!parentHasClass && !grandparentHasClass) {
          validImagesCount++
        }
      })
      const imgButton = doc.querySelectorAll('.img_card_button')
      imgButton.forEach((item) => {
        validImagesCount += JSON.parse(item.parentNode.getElementsByClassName('info')[0].innerText).content.length
      })
      return validImagesCount
    },

    onFocus (editor) {
      if (this.saveTimer) {
        clearInterval(this.saveTimer)
        this.saveTimer = null
      }
      this.saveTimer = setInterval(async () => {
        // if (!this.contentChanged) return
        // if (!tinymce.activeEditor.hasFocus()) {
        //   clearInterval(this.saveTimer)
        //   this.saveTimer = null
        //   return
        // }
        this.autoTips = true
        await this.saveHtml()
        this.autoTips = false
        this.contentChanged = false
      }, 5000)
    },
    onBlur (editor) {
      // if (this.saveTimer) {
      //   clearInterval(this.saveTimer)
      //   this.saveTimer = null
      // }
    },
    onChange (editor) {
      this.contentChanged = true
    },
    resetHtml () {
      // 使用原生slate删除表格内容
      const content = <p></p>
      tinymce.editor.setContent(content)
    },
    beforeunloadFn (e) {
      if (this.getHtml() !== this.htmlTemp) {
        // this.saveHtml()
        // console.log("文本内容一样，不提交")
        e.returnValue = ('确定离开当前页面吗？')
        return '有未保存的编辑'
      }
      clearInterval(this.saveTimer)
      this.saveTimer = null
    },
    preShowFn () {
      this.preShow = true
      this.saveHtml()
    },
    // AI操作
    handleAI (type = 'menu') {
      this.editChapterShow = false
      this.$refs.setAiRef.open(type)
    },
    updateAITip (val) {
      this.aiTip = val
    },
    handleTrainingClick() {
      const bookmark = tinymce.activeEditor.selection.getBookmark()
      const selectContent = tinymce.activeEditor.selection.getNode()
      console.log(selectContent.getAttribute('data-ids'))
      if (selectContent.getAttribute('data-ids') !== null) {
        setData({
          testId: selectContent.getAttribute('data-id'),
          ids: selectContent.getAttribute('data-ids')
        })
      }
      addTestPopModal({
        createTest(testTitle, flag = false) {
          createTest({ testPaperLinkType: 'DIGITAL_BOOK_CONTENT', testPaperTitle: testTitle, sourceId: store.state.app.activeCatalogueId }, {
            authorization: 'Bearer ' + router.currentRoute.query.token
          }).then(res => {
            tinymce.activeEditor.selection.moveToBookmark(bookmark)
            const media = `<div class='test_card mceNonEditable' data-id="${res.data.id}" data-ids=""   style='margin-bottom:2px;width:100%;background: #E3F5FF;overflow:hidden;padding:30px;box-sizing: border-box;position:relative'><div style="color:#4F4F4F;font-size:20px;font-weight:700">互动学习</div><div style="display:flex;margin-top:40px"><img style="width:30px" src='https://static.bingotalk.cn/bingodev/image/2024072615483836.svg' /><div style="line-height:30px;width:60%">${res.data.title}</div></div><div class='do_test' style="position: absolute; right: 10%; bottom: 26%;  padding-left:10px; padding-right:10px;padding-top:5px;padding-bottom:5px; border: 2px solid #2D9CDB; border-radius: 5px; text-align: center;  color: #2d9cdb;cursor: pointer;">去做题</div></div>`
            tinymce.activeEditor.selection.setContent(media)
            const content = tinymce.activeEditor.getContent()
            tinymce.activeEditor.setContent(content)
            if (!flag) {
              setData({ testId: res.data.id })
            }
          })
        },
        updateTest(testTitle, testId, length, ids, flag = false) {
          var newContent = tinymce.activeEditor.dom.select(`div[data-id="${testId}"]`)[0]
          tinymce.activeEditor.selection.select(newContent)
          createTest({ testPaperLinkType: 'DIGITAL_BOOK_CONTENT', testPaperTitle: testTitle, sourceId: store.state.app.activeCatalogueId, testPaperId: testId }, {
            authorization: 'Bearer ' + router.currentRoute.query.token
          }).then(res => {
            tinymce.activeEditor.selection.moveToBookmark(bookmark)
            const media = `<div class='test_card mceNonEditable' data-id="${res.data.id}" data-ids="${ids}"  style='margin-bottom:2px;width:100%;background: #E3F5FF;overflow:hidden;padding:30px;box-sizing: border-box;position:relative'><div style="color:#4F4F4F;font-size:20px;font-weight:700">互动学习</div><div style="display:flex;margin-top:40px"><img style="width:30px" src='https://static.bingotalk.cn/bingodev/image/2024072615483836.svg' /><div style="line-height:30px;width:60%">${res.data.title}</div></div><div class='do_test' style="position: absolute; right: 10%; bottom: 26%;  padding-left:10px; padding-right:10px;padding-top:5px;padding-bottom:5px; border: 2px solid #2D9CDB; border-radius: 5px; text-align: center;  color: #2d9cdb;cursor: pointer;">去做题</div></div>`
            tinymce.activeEditor.selection.setContent(media)
            // const content = editor.getContent()
            // editor.setContent(content)
            if (flag) {
              setData({ testId: res.data.id, ids: ids })
            }
          })
        }
      })
    },
    aiConfigConfirm(type, html) {
      if (type === 'menu') {
        this._getBookCatalogue()
      } else {
        // this.html = html
        // tinymce.activeEditor.selection.setContent(`<p><span class="math-tex">\\( f: \\mathbb{R}^n \\to \\mathbb{R} \\)</span></p >`)
        tinymce.activeEditor.insertContent(html)
        tinymce.activeEditor.focus()
        this.html = this.getHtml()
        this.saveHtml()
        this.$nextTick(() => {
          // this.renderEditorMath()
        })
      }
    },
    // 渲染编辑器内的公式
    renderEditorMath() {
      if (window.MathJax) {
        MathJax.typesetClear()
        MathJax.typeset([this.editorBody])
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-collapse-item__content{
  font-size: 8px !important;
  position: relative;
  .submit_message{
    width: 100%;
    height: 30px;
    position: absolute;
    bottom:0;
    left:0;
    display: flex;
    justify-content: space-between;
    .message_input{
      width: 240px;
      height: 20px !important;
      font-size: 8px !important;
      padding: 2px;
      .el-input__inner{
        width: 100%;
        height: 20px !important;
      }
    }
    .message_button{
      width: 40px;
      height: 20px;
      margin-top: 2px;
      font-size: 8px !important;
      padding: 5px;
    }
  }
  .message_content{
    width: 100%;
    margin-bottom: 10px;
    .message_item{
      .header{
        width: 100%;
        display: flex;
        font-size: 8px !important;
        margin-top: 5px;
        display: flex;
        .message_tag{
          font-size: 6px;
          height: 14px;
          padding: 2px;
        }
      }
      .message_content{
        margin-top: 3px;
      }
    }
  }
}
::v-deep .el-collapse-item__header{
  font-size: 8px !important;
  height: 20px;
  position: relative;
  .add{
    position: absolute;
    color: #2F80ED;
    left: 110px;
  }
}
.right_content {
  width: 300px;
  // max-width: 300px;
  height: 100%;
  padding: 10px;
  background: #fff;
  overflow: auto;
  @include scrollBar;
  ::v-deep .el-tabs__header{
    transform: scale(0.8);
  }
  ::v-deep .el-tabs__nav-scroll{
    // width: 50%!important;
    margin: 0 auto!important;
  }
  .author_item{
    width: 100%;
    height: 50px;
    background: #fbf9f9;
    margin-top: 10px;
    position: relative;
    .time{
      font-size: 10px;
      height: 12px;
      text-align: left;
      color: #828282;
      padding: 10px;
    }
    .author{
      font-size: 10px;
      height: 12px;
      text-align: left;
      padding-left: 10px;
      position: relative;
      text-indent: 1rem;
    }
    .author::before{
      content: '';
      width: 6px;
      height: 6px;
      border-radius: 3px;
      background: #27AE60;
      position: absolute;
      left: 10px;
      top:3px;
    }
    .button{
      position: absolute;
      font-size: 9px;
    }
    .button1{
      right: 20px;
      bottom:20px;
    }
    .button2{
      right: 20px;
      bottom:-5px;
    }
  }
  .title {
    width: 100%;
    text-align: center;
  }

  .bug_item {
    margin-top: 5px;

    ::v-deep .el-tag {
      font-size: 8px;
      padding: 5px;
      height: 20px;
      line-height: 10px;
    }

    ::v-deep .el-button {
      font-size: 10px;
      // color:red
    }
    ::v-deep .el-collapse {
      border-top: none !important
    }
    .bug_content {
      width: 100%;
      margin-top: 10px;
      padding-bottom: 5px;
      // border-bottom: 1px solid #E0E0E0;
    }

    .bug_text {
      background: #F2F2F2;
      padding: 5px;
      margin-top: 5px;
      font-size: 8px;
      overflow: hidden;
      cursor: pointer;
      max-height: 160px;
      div {
        text-overflow: ellipsis;
        width: 200%;
        height: auto;
        transform: scale(0.5);
        transform-origin: top left;
      }

      ::v-deep img {
        width: 100%;
      }
    }
  }
}

#tinymce{
  img{
    width:100%;
  }
}
.editor-dig {
  width: 100%;
  height: 100%;
  background: #FFF;
  box-sizing: border-box;
  position: relative;
  .word_button{
    width: 82px;
    height:30px;
    cursor: pointer;
    margin-right: 220px;
  }
  .other-use {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba($color: #000000, $alpha: .4);
    z-index: 999999;
  }

  .dig-head {
    width: 100%;
    // min-width: 1060px;
    overflow-x: auto;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    box-sizing: border-box;
    .title{
      margin-left: 180px;
      white-space: nowrap;
    }
    .dig-tips {
      font-size: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .dig-btn {
      border-radius: 4px;
      background: #2F80ED;
      color: #FFF;
      font-size: 14px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 5px 10px;
      box-sizing: border-box;
      cursor: pointer;
      margin-left: 10px;
      white-space: nowrap;
    }
  }

  .dig-box {
    width: 100%;
    min-width: 1060px;
    height: calc(100% - 50px);
    display: flex;
    justify-content: center;
    border-bottom: 1px solid #E0E0E0;
    background: #F9F9F9;
    overflow: hidden;
  }

  .dig-left {
    min-width: 220px;
    max-width: 25vw;
    height: 100%;
    background: #fff;
    padding-right: 5px;
    overflow-x:hidden;
    .chapter-title {
      width: 100%;
      height: 40px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 5px;

      .icon1 {
        color: #000;
        font-size: 16px;
        display: flex;
        align-items: center;
        .mulu{
          font-weight: 800;
        }

        img {
          width: 27px;
          height: 27px;
          margin-right: 5px;
        }
      }

      .add-btn {
        color: #2F80ED;
        font-size: 14px;
        margin-right: 10px;
        cursor: pointer;
      }
    }

    .chapter-body {
      width: 100%;
      height: calc(100% - 40px);
      overflow: auto;
      overflow-x:hidden;
      ::v-deep .el-tree-node__content {
        height: 40px;
        &:hover{
          background-color: #edf6ff !important;
          transform: translateX(5px)
        }
      }

      ::v-deep .el-button + .el-button {
        margin-left: 5px;
      }

      ::v-deep .el-button {
        font-size: 14px;
      }

      ::v-deep .el-tree-node__content > .el-tree-node__expand-icon {
        padding: 5px;
      }

      .tree-body {
        width: calc(100% - 30px);
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        padding-right: 8px;
        .svgIcon{
          width: 12px;
          height: 12px;
          font-size: 12px;
        }
        .chapter-name {
          flex: 1;
          overflow: hidden;
          color: black;
          @include scrollBar;
        }
        .chapter-name-1 {
          font-weight: 800 !important;
          font-size: 16px !important;
        }
        .chapter-name-3 {
          color: #5c5a5a;
        }
        .chapter-option {
          flex-shrink: 0;
          .red_idot{
            display: inline-block;
            margin-right: 10px;
            width: 15px;
            height: 15px;
            font-size: 12px;
            font-weight: 700;
            border-radius: 10px;
            background: red;
            color: #fff;
            text-align: center;
            line-height: 15px;
          }
        }
      }
    }
  }

  .dig-center {
    width: 793.667px;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #FFF;
    position: relative;

    .toolbar {
      width: 793.667px;
      height: 80px;
    }

    .eidtor {
      width: 793.667px;
      height: 100%;
      // padding: 10px;
      overflow-y: auto;
    }
  }

  ::v-deep .w-e-bar-divider {
    display: none;
  }

  ::v-deep .w-e-textarea-video-container {
    background: transparent;
    border: none;
    padding: 0;
    margin: 0;

    video {
      width: 100%;
    }
  }

  .pre {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 100%;
    z-index: 99;
    overflow: auto;
  }
}
</style>
