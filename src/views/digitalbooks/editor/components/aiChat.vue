<template>
  <el-drawer
    :visible.sync="drawer"
    :direction="direction"
    size="33%"
    :modal="false"
    :with-header="false"
    :wrapper-closable="false"
    modal-class="AIdrawer"
    class="AIdrawerWrapper"
    :append-to-body="false"
    :modal-append-to-body="false"
    :show-close="true"
  >
    <i class="el-icon-close close_drawer" @click="drawer = false"></i>
    <SystemView v-if="showSystem && hasSystem" @closeView="showSystem = false" @handleClick="handleClickSystem"/>
    <span v-else class="system-btn" style='top: 70px' @click="showSystem = true">展开</span>
    <JwChat-index
      v-model="inputMsg"
      :tale-list="list"
      :show-right-box="false"
      scroll-type="scroll"
      width="100%"
      height="100%"
      :config="config"
      :tool-config="toolConfig"
      placeholder="请输入你的问题..."
      @enter="bindEnter"
    >
      <template #enterBtn>
        <img
          style="width: 30px"
          src="../../../../assets/digitalbooks/read/send_button.png"
          alt=""
        />
      </template>
      <template #tools>
        <el-dropdown class="type_select">
          <span class="el-dropdown-link">
            {{ type }}<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              @click.native="changeType('AI对话')"
            >AI对话</el-dropdown-item>
            <el-dropdown-item
              @click.native="changeType('图片生成')"
            >图片生成</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
    </JwChat-index>
    <transition name="fade">
      <div v-if="historyShow" class="AIdrawer-history">
        <i class="el-icon-close close_history" @click="historyShow = false"></i>
        <div class="history_main">
          <div
            v-for="(item, index) in historyList.length / 2"
            :key="index"
            class="history_item"
          >
            <i
              class="el-icon-delete history_delete"
              style="color: #f56c6c"
              @click="deleteHistory(index)"
            ></i>
            <p>{{ historyList[2 * index].date }}</p>
            <p v-html="historyList[2 * index].text.text"></p>
            <div
              class="content"
              v-html="historyList[2 * index + 1].text.text"
            ></div>
          </div>
        </div>
      </div>
    </transition>
  </el-drawer>
</template>

<script>
import SystemView from '@/views/digitalbooks/editor/components/AiEditView/systemView'
import { getFileUploadAuthor } from '@/api/user-api'
import tinymce from 'tinymce/tinymce'
import axios from 'axios'
import MarkdownIt from 'markdown-it'
import mk from 'markdown-it-katex'
import hljs from 'highlight.js'
import 'highlight.js/lib/languages/python'
import 'highlight.js/lib/languages/javascript'
import 'highlight.js/lib/languages/java'
import 'highlight.js/lib/languages/cpp'
import 'highlight.js/lib/languages/bash'
import 'highlight.js/styles/github.css'
import 'katex/dist/katex.min.css'
export default {
  props: {
    menuList: {
      type: Array,
      default: () => []
    },
    preNode: {
      type: Object,
      default: () => null
    },
    hasSystem: {
      type: Boolean,
      default: false
    },
  },
  components: {
    SystemView
  },
  data () {
    return {
      showSystem: true,
      drawer: false,
      drawerHistory: false,
      historyShow: false,
      isWork: false,
      canInput: true,
      requestId: '',
      direction: 'rtl',
      copyUrl: require('@/assets/digitalbooks/read/copy.png'),
      resendUrl: require('@/assets/digitalbooks/read/resend_button.png'),
      addUrl: require('@/assets/digitalbooks/read/add_button.png'),
      type: 'AI对话',
      list: [],
      inputMsg: '',
      config: {
        img: require('@/assets/digitalbooks/read/aibutton.png'),
        name: '',
        dept: ''
      },
      historyList: [],
      md: new MarkdownIt({
        html: true,
        breaks: false,
        linkify: true,
        typographer: true,
        tables: true,
        highlight: function (str, lang) {
          if (!lang) lang = 'plaintext'
          if (hljs.getLanguage(lang)) {
            try {
              return '<pre class="hljs"><div class="code-header">' +
                    '<span class="code-lang">' + lang + '</span>' +
                    '<button class="copy-btn" onclick="copyCode(this)">复制</button>' +
                    '</div><code class="' + lang + '">' +
                    hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +
                    '</code></pre>'
            } catch (__) {
              return '<pre class="hljs"><code>' + this.md.utils.escapeHtml(str) + '</code></pre>'
            }
          }
          return '<pre class="hljs"><code>' + this.md.utils.escapeHtml(str) + '</code></pre>'
        }
      }).use(mk, {
        throwOnError: false,
        trust: true,
        macros: {
          '\\sum': '\\sum\\limits',
          '\\f': '\\frac'
        },
        delimiters: [
          { left: '$$', right: '$$', display: true },
          { left: '\\[', right: '\\]', display: true },
          { left: '$', right: '$', display: false },
          { left: '\\(', right: '\\)', display: false }
        ]
      }),
      toolConfig: {
        show: ['history'],
        showEmoji: false,
        callback: (type, plyload) => {
          this.historyShow = true
          this.historyList = JSON.parse(localStorage.getItem('chatHistory'))
        }
      }
    }
  },
  mounted () {
    document.addEventListener('compositionstart', this.setTypeFalse)
    document.addEventListener('compositionend', this.setTypeTrue)
  },
  beforeDestroy () {
    document.removeEventListener('compositionstart', this.setTypeFalse)
    document.removeEventListener('compositionend', this.setTypeTrue)
  },
  methods: {
    setTypeFalse () {
      this.canInput = false
    },
    setTypeTrue () {
      const timer = setTimeout(() => {
        this.canInput = true
        clearTimeout(timer)
      }, 200)
    },
    setButton () {
      this.isWork = false
      const content = document.getElementsByClassName('web__main-item')
      for (let i = 0; i < content.length; i++) {
        if (!content[i].classList.contains('web__main-item--mine') && content[i].getElementsByClassName('copy_button').length === 0) {
          const Image = document.createElement('img')
          Image.className = 'copy_button'
          Image.src = this.copyUrl
          Image.style = 'width:15px;height:15px; position: absolute;bottom: -20px;left: 30px;cursor: pointer;z-index: 999'
          Image.onclick = (e) => {
            const selection = window.getSelection()
            selection.removeAllRanges()
            const range = document.createRange()
            range.selectNode(e.target.parentNode.getElementsByClassName('item_msg')[0])// 传入dom
            selection.addRange(range)
            document.execCommand('copy')// copy是复制
            this.$nextTick(() => {
              selection.removeAllRanges()
            })
            this.$message.success('复制成功')
          }
          content[i].appendChild(Image)
          const Image2 = document.createElement('img')
          Image2.className = 'resend_button'
          Image2.src = this.resendUrl
          Image2.style = 'width:15px;height:15px; position: absolute;bottom: -20px;left:50px;cursor: pointer;z-index: 999'
          Image2.onclick = (e) => {
            this.reSend(e.target.parentNode.getElementsByClassName('item_msg')[0], i)
          }
          content[i].appendChild(Image2)
          const addButton = document.createElement('img')
          addButton.className = 'add_button'
          addButton.src = this.addUrl
          addButton.style = 'width:60px;height:20px; position: absolute;bottom: -23px;left: 75px;cursor: pointer;z-index: 999'
          addButton.onclick = (e) => {
            this.appendToRich(e.target.parentNode.getElementsByClassName('item_msg')[0])
          }
          content[i].appendChild(addButton)
        }
      }
    },
    appendToRich (node) {
      if (tinymce.activeEditor.readonly) {
        this.$message.warning('请先选择章节')
        return
      }
      if (node.getElementsByTagName('img').length > 0) {
        tinymce.activeEditor.selection.setContent(node.getElementsByTagName('img')[0].outerHTML)
      } else { tinymce.activeEditor.selection.setContent('<p>' + node.firstChild.innerHTML + '</p>') }
    },
    getBase64Image (url) {
      return new Promise((resolve, reject) => {
        const img = new Image()
        const canvas = document.createElement('canvas')
        img.crossOrigin = '*'
        img.src = url
        img.onload = function () {
          const width = img.width; const height = img.height
          canvas.width = width
          canvas.height = height

          const ctx = canvas.getContext('2d')
          ctx.fillStyle = 'white'
          ctx.fillRect(0, 0, canvas.width, canvas.height)
          ctx.drawImage(img, 0, 0, width, height)
          const base64 = canvas.toDataURL()
          resolve(base64)
        }
        img.onerror = function (e) {
          reject(new Error(e))
        }
      })
    },
    async reSend (node, index) {
      this.isWork = true
      this.list[index].text = { text: '<div class="message-text" style="white-space: pre-wrap;">我正在重新思考...</div>' }
      const msg = this.parseHtml(this.list[index - 1].text.text).body.innerText
      if (node.getElementsByTagName('img').length > 0) {
        try {
          const response = await fetch(`${process.env.VUE_APP_BASE_API}/api/v2/comm/vt/aigcImg?question=${msg}`)
          if (!response.ok) throw new Error('Network response was not ok')

          const reader = response.body.getReader()
          const textDecoder = new TextDecoder()
          let result = true
          let res = ''
          while (result) {
            const { done, value } = await reader.read()
            if (done) {
              result = false
              this.isWork = false
              let img = JSON.parse(res).data.img
              const type = JSON.parse(res).data.type
              // img = 'https://dashscope-result-wlcb-acdr-1.oss-cn-wulanchabu-acdr-1.aliyuncs.com/1d/78/20250731/7c732c4a/4a478ee5-90ea-44ec-9662-fecbfe5604192979917732.png?Expires=1754037278&OSSAccessKeyId=LTAI5tKPD3TMqf2Lna1fASuh&Signature=lKxg7tiBeEo93oY3uex3KMsD%2FgU%3D'
              if (type !== 'imgUrl') {
                img = await this.uploadImg(img)
              } else {
                img = await this.uploadImg(await this.getBase64Image(img), false)
              }
              if (!img) {
                this.$message.error('图片生成失败，请重试')
                this.list[index].text = { text: '<div class="message-text" style="white-space: pre-wrap;">图片生成失败，请重试</div>' }
              } else {
                this.list[index].text = { text: `<div class="message-text" style="white-space: pre-wrap;"><img style="width:60%;"  src='${img}' data-src='${img}'/></div>` }
              }
              break
            }

            const chunkText = textDecoder.decode(value)
            res += chunkText
          }
        } catch (e) {
          this.$message.error('图片生成失败，请重试')
          this.list[index].text = { text: '<div class="message-text" style="white-space: pre-wrap;">图片生成失败，请重试</div>' }
          this.isWork = false
          const timer = setTimeout(() => {
            this.setButton()
            clearTimeout(timer)
          }, 1000)
        }
      } else {
        try {
          const response = await fetch(`${process.env.VUE_APP_BASE_API}/api/v2/comm/vt/aiChat?question=${msg}&requestId=${this.requestId}`)
          if (!response.ok) throw new Error('Network response was not ok')

          const reader = response.body.getReader()
          const textDecoder = new TextDecoder()
          let result = true
          let res = ''
          while (result) {
            const { done, value } = await reader.read()
            if (done) {
              result = false
              this.isWork = false
              break
            }

            const chunkText = textDecoder.decode(value).replace(/}{"code"/g, '}-down-{"code"')
            const arr = chunkText.split('-down-')
            arr.forEach(item => {
              if (this.isJSON(item)) {
                if (JSON.parse(item).data.result) {
                  res += JSON.parse(item).data.result
                  this.requestId = JSON.parse(item).data.id
                }
              }
            })
            const renderedHtml = this.renderMarkdown(res)
            this.list[index].text = { text: `<div class="message-text" style="white-space: pre-wrap;">${renderedHtml}</div>` }
          }
        } catch (e) {
          this.$message.error('AI算力加速中，请重试')
          this.list[index].text = { text: '<div class="message-text" style="white-space: pre-wrap;">AI算力加速中，请重试</div>' }
          this.isWork = false
          const timer = setTimeout(() => {
            this.setButton()
            clearTimeout(timer)
          }, 1000)
        }
      }
      const timer = setTimeout(() => {
        this.setButton()
        clearTimeout(timer)
      }, 1000)
      if (localStorage.getItem('chatHistory')) {
        const chatHistory = JSON.parse(localStorage.getItem('chatHistory'))
        chatHistory.push(this.list[index - 1])
        chatHistory.push(this.list[index])
        localStorage.setItem('chatHistory', JSON.stringify(chatHistory))
      } else {
        const chatHistory = []
        chatHistory.push(this.list[index - 1])
        chatHistory.push(this.list[index])
        localStorage.setItem('chatHistory', JSON.stringify(chatHistory))
      }
    },
    open () {
      this.drawer = !this.drawer
      // if (this.drawer) {
      //   this.showSystem = true
      // }
    },
    changeType (val) {
      this.type = val
    },
    deleteHistory (index) {
      this.historyList.splice(2 * index, 2)
      localStorage.setItem('chatHistory', JSON.stringify(this.historyList))
    },
    setStorage () {
      if (localStorage.getItem('chatHistory')) {
        const chatHistory = JSON.parse(localStorage.getItem('chatHistory'))
        chatHistory.push(this.list[this.list.length - 2])
        chatHistory.push(this.list[this.list.length - 1])
        localStorage.setItem('chatHistory', JSON.stringify(chatHistory))
      } else {
        const chatHistory = []
        chatHistory.push(this.list[this.list.length - 2])
        chatHistory.push(this.list[this.list.length - 1])
        localStorage.setItem('chatHistory', JSON.stringify(chatHistory))
      }
    },
    getTime () {
      const now = new Date()

      const year = now.getFullYear()
      const month = ('0' + (now.getMonth() + 1)).slice(-2)
      const day = ('0' + now.getDate()).slice(-2)
      const hours = ('0' + now.getHours()).slice(-2)
      const minutes = ('0' + now.getMinutes()).slice(-2)
      const seconds = ('0' + now.getSeconds()).slice(-2)

      return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds
    },
    async bindEnter (e) {
      if (this.inputMsg.length === 0) {
        this.$message.warning('请输入内容')
        return
      }
      this.showSystem = false
      if (!this.canInput) {
        const data = this.inputMsg
        const timer = setTimeout(() => {
          this.inputMsg = data
          clearTimeout(timer)
        })
        return
      }
      if (this.isWork) {
        this.$message.warning('正在回答中，请稍候')
        return
      }
      this.isWork = true
      const msg = this.inputMsg
      if (!msg) return
      const msgObj = {
        date: this.getTime(),
        text: { text: `<div class="message-text" style="white-space: pre-wrap;">${msg}</div>` },
        mine: true
      }
      const result = {
        date: this.getTime(),
        text: { text: '<div class="message-text" style="white-space: pre-wrap;">我正在思考...</div>' },
        mine: false
      }
      if (this.type === 'AI对话') {
        this.getStream(msg)
      } else {
        this.getStreamImg(msg)
      }
      this.list.push(msgObj)
      this.list.push(result)
    },
    async getStream (msg) {
      try {
        const url = `${process.env.VUE_APP_BASE_API}/api/v2/comm/vt/aiChat?question=${msg}&requestId=${this.requestId}`
        const response = await fetch(url)
        if (!response.ok) throw new Error('Network response was not ok')

        const reader = response.body.getReader()
        const textDecoder = new TextDecoder()
        let result = true
        let res = ''
        while (result) {
          const { done, value } = await reader.read()
          if (done) {
            this.setStorage()
            result = false
            this.isWork = false
            const timer = setTimeout(() => {
              this.setButton()
              clearTimeout(timer)
            }, 1000)
            break
          }

          const chunkText = textDecoder.decode(value).replace(/}{"code"/g, '}-down-{"code"')
          const arr = chunkText.split('-down-')
          arr.forEach(item => {
            if (this.isJSON(item)) {
              if (JSON.parse(item).data.result) {
                res += JSON.parse(item).data.result
                this.requestId = JSON.parse(item).data.id
              }
            }
          })
          const renderedHtml = this.renderMarkdown(res)
          this.list[this.list.length - 1].text = { text: `<div class="message-text" style="white-space: pre-wrap;">${renderedHtml}</div>` }
        }
      } catch (e) {
        this.$message.error('AI算力加速中，请重试')
        this.list[this.list.length - 1].text = { text: '<div class="message-text" style="white-space: pre-wrap;">AI算力加速中，请重试</div>' }
        this.isWork = false
        const timer = setTimeout(() => {
          this.setButton()
          clearTimeout(timer)
        }, 1000)
      }
    },
    isJSON (str) {
      if (typeof str === 'string') {
        try {
          JSON.parse(str)
          return true
        } catch (e) {
          return false
        }
      }
      return false
    },
    base64ToImg (base64) {
      return new Promise((resolve, reject) => {
        const img = new Image()
        img.src = 'data:image/jpeg;base64,' + base64
        img.onload = function () {
          resolve(img)
        }
        img.onerror = function (e) {
          reject(e)
        }
      })
    },
    async uploadImg (base64, type = true) {
      let imgData
      if (type) { imgData = 'data:image/png;base64,' + base64 } else { imgData = base64 }
      const arr = imgData.split(',')
      const mime = arr[0].match(/:(.*?);/)[1]
      const suffix = mime.split('/')[1]
      const bstr = atob(arr[1])
      let n = bstr.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      const file = new File([u8arr], `'test'.${suffix}`, {
        type: mime
      })
      const { data } = await getFileUploadAuthor({
        mediaType: 'IMAGE',
        contentType: '',
        quantity: 1,
        fileName: file.name
      })
      const ossCDN = data[0].ossConfig.ossCDN
      try {
        const formData = new FormData()
        formData.append('success_action_status', '200')
        formData.append('callback', '')
        formData.append('key', data[0].fileName)
        formData.append('policy', data[0].policy)
        formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
        formData.append('signature', data[0].signature)
        formData.append('file', file)
        await axios.post(data[0].ossConfig.host, formData, {
        })
        return `${ossCDN}/${data[0].fileName}`
      } catch (error) {
        return false
      }
    },
    async getStreamImg (msg) {
      try {
        const response = await fetch(`${process.env.VUE_APP_BASE_API}/api/v2/comm/vt/aigcImg?question=${msg}`)
        if (!response.ok) {
          throw new Error('Network response was not ok')
        }

        const reader = response.body.getReader()
        const textDecoder = new TextDecoder()
        let result = true
        let res = ''
        while (result) {
          const { done, value } = await reader.read()
          if (done) {
            this.isWork = false
            result = false
            let img = JSON.parse(res).data.img
            const type = JSON.parse(res).data.type
            if (type !== 'imgUrl') {
              img = await this.uploadImg(img)
            } else {
              img = await this.uploadImg(await this.getBase64Image(img), false)
            }
            console.log('img+++++++++++++++++', img, type)
            if (!img) {
              this.$message.error('图片生成失败，请重试')
              this.list[this.list.length - 1].text = { text: '<div class="message-text" style="white-space: pre-wrap;">图片生成失败，请重试</div>' }
              this.setStorage()
              const timer = setTimeout(() => {
                this.setButton()
                clearTimeout(timer)
              }, 1000)
            } else {
              this.list[this.list.length - 1].text = { text: `<div class="message-text" style="white-space: pre-wrap;"><img style="width:60%;"  src='${img}' data-src='${img}'/></div>` }
              this.setStorage()
              const timer = setTimeout(() => {
                this.setButton()
                clearTimeout(timer)
              }, 1000)
            }
            break
          }

          const chunkText = textDecoder.decode(value)
          res += chunkText
        }
      } catch (e) {
        this.$message.error('图片生成失败，请重试')
        this.list[this.list.length - 1].text = { text: '<div class="message-text" style="white-space: pre-wrap;">图片生成失败，请重试</div>' }
        this.setStorage()
        this.isWork = false
      }
    },
    renderMarkdown (text) {
      text = text.replace(/&lt;/g, '<').replace(/&gt;/g, '>')
      let html = this.md.render(text)
      html = html.replace(/>\s+</g, '><')
      html = html.replace(/<\/td>\s+<td>/g, '</td><td>')
      html = html.replace(/<\/th>\s+<th>/g, '</th><th>')
      html = html.replace(/<\/tr>\s+<tr>/g, '</tr><tr>')
      return html
    },
    parseHtml(html) {
      const parser = new DOMParser()
      return parser.parseFromString(html, 'text/html')
    },
    // 系统默认界面
    handleClickSystem(type) {
      if (type === 'menu') {
        if (this.menuList.length === 0) {
          // this.showSystem = false
          this.drawer = false
          this.$emit('handleMenuClick')
        } else {
          this.$message.warning('已有目录，AI暂时无法再次生成目录')
        }
      } else if (type === 'image') {
        if (this.preNode) {
          this.changeType('图片生成')
          this.$message.success('已切换成图片生成，请输入提示词生成图片')
        } else {
          this.$message.warning('请选择目录章节')
        }
      } else if (type === 'training') {
        if (this.preNode) {
          // this.showSystem = false
          this.drawer = false
          this.$emit('handleTrainingClick')
        } else {
          this.$message.warning('请选择目录章节')
        }
      } else if (type === 'content') {
        if (this.preNode) {
          // this.showSystem = false
          this.drawer = false
          this.$emit('handleContentClick')
        } else {
          this.$message.warning('请选择目录章节')
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.type_select {
  // transform: scale(0.6);
  cursor: pointer;
  font-size: 0.9rem;
  margin-left: 5px;
}
::v-deep .el-dropdown-menu__item {
  transform: scale(0.6);
  padding: 0.5rem;
}
::v-deep .el-dropdown-menu__item {
  line-height: 2rem;
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
  opacity: 0;
}
.AIdrawer-history {
  width: 80%;
  height: 80%;
  position: absolute;
  left: 10%;
  bottom: 10%;
  border-radius: 1rem;
  background: #fff;
  box-shadow: 0 0.727273vw 0.909091vw -0.454545vw rgba(0, 0, 0, 0.2),
    0 1.454545vw 2.181818vw 0.181818vw rgba(0, 0, 0, 0.14),
    0 0.545455vw 2.727273vw 0.454545vw rgba(0, 0, 0, 0.12);
  .close_history {
    width: 20px;
    height: 20px;
    position: absolute;
    display: block;
    right: 0;
    top: 10px;
    cursor: pointer;
  }
  .history_main {
    width: 90%;
    height: 90%;
    margin-top: 12%;
    margin-left: 5%;
    overflow: auto;
    @include noScrollBar;
    .history_item {
      border-bottom: 1px solid #e6e6e6;
      font-size: 8px;
      padding-bottom: 10px;
      position: relative;
      .history_delete {
        position: absolute;
        display: block;
        right: 0;
        top: 10px;
        cursor: pointer;
      }
      .content {
        ::v-deep img {
          max-width: 100%;
        }
      }
    }
  }
}
::v-deep .web__msg-menu {
  position: absolute;
  right: 20px;
  bottom: 20px;
  cursor: pointer;
}
::v-deep .web__main-item--mine {
  padding-right: 0 !important;
  .web__main-text {
    background: #2d9cdb !important;
  }
  .web__main-arrow::after {
    border-left-color: #2d9cdb !important;
  }
}
::v-deep .web__main-user {
  display: none !important;
}
::v-deep .web__main-item {
  padding-left: 20px !important;
}
::v-deep .web__main-text {
  max-width: 95% !important;
  table {
    border-collapse: collapse;
    width: 100%;
    margin: 1em 0;
    th, td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }
    th {
      background-color: #f5f5f5;
      font-weight: bold;
    }
    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
  }
}
::v-deep .wrapper {
  width: 100%;
  height: 100% !important;
  padding-bottom: 2rem;
}
::v-deep .chatPage {
  height: 100% !important;
}
::v-deep .web__main-user {
  display: none;
}
::v-deep .header {
  background: #d3e6ff !important;
}
::v-deep .cover {
  width: 60px !important;
  height: auto !important;
  border-radius: 0% !important;
  box-shadow: none !important;
}
::v-deep .el-drawer__container {
  pointer-events: none;
  .close_drawer {
    position: absolute;
    z-index: 999;
    right: 20px;
    top: 10px;
    font-size: 15px;
    cursor: pointer;
  }
  .system-btn{
    position: absolute;
    z-index: 999;
    right: 10px;
    font-size: 12px;
    color: #2F80ED;
    cursor: pointer;
  }
}
.AIdrawerWrapper {
  pointer-events: none;
  // position: relative;
}
::v-deep .el-drawer {
  box-shadow: 0 0.727273vw 0.909091vw -0.454545vw rgba(0, 0, 0, 0.2),
    0 1.454545vw 2.181818vw 0.181818vw rgba(0, 0, 0, 0.14),
    0 0.545455vw 2.727273vw 0.454545vw rgba(0, 0, 0, 0.12);
}
::v-deep .el-drawer__body {
  pointer-events: auto;
  padding: 0px;
  background: #fff;
}
</style>
