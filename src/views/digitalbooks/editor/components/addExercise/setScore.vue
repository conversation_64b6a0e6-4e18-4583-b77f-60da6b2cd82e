<template>
  <div class="set-body" v-if="show">
    <div class="set-main" v-loading='loading' element-loading-background="rgba(255, 255, 255, 0.5)">
      <div class="header">
        批量设置分数
      </div>
      <div class="content">
        <div class="content-item">
          <div class="item-left">选择题{{`（共${ listObj.choiceList.length }道）`}}</div>
          <div class="item-center">
            <el-input v-model='choiceScore' :class="{ 'is-error': choiceError }" placeholder='请输入分数' type='number' style="width: 100%" @blur="validateInput('choice')"/>
          </div>
          <div class="item-right">分</div>
        </div>
        <div class="content-item">
          <div class="item-left">填空题{{`（共${ listObj.fillList.length }道）`}}</div>
          <div class="item-center">
            <el-input v-model='fillScore' :class="{ 'is-error': fillError }" placeholder='请输入分数' type='number' style="width: 100%" @blur="validateInput('fill')"/>
          </div>
          <div class="item-right">分</div>
        </div>
        <div class="content-item">
          <div class="item-left">判断题{{`（共${ listObj.simpleList.length }道）`}}</div>
          <div class="item-center">
            <el-input v-model='simpleScore' :class="{ 'is-error': simpleError }" placeholder='请输入分数' type='number' style="width: 100%" @blur="validateInput('simple')"/>
          </div>
          <div class="item-right">分</div>
        </div>
        <div class="content-item">
          <div class="item-left">简答题{{`（共${ listObj.essayList.length }道）`}}</div>
          <div class="item-center">
            <el-input v-model='essayScore' :class="{ 'is-error': essayError }" placeholder='请输入分数' type='number' style="width: 100%" @blur="validateInput('essay')"/>
          </div>
          <div class="item-right">分</div>
        </div>
        <div class="content-item">
          总分：{{ (Number(choiceScore) * listObj.choiceList.length) + (Number(fillScore) * listObj.fillList.length) + (Number(simpleScore) * listObj.simpleList.length) + (Number(essayScore) * listObj.essayList.length) }} 分
        </div>
      </div>
      <div class="bottom">
        <el-button type='primary' size='small' @click="handleSave">保存</el-button>
        <el-button @click='close' size='small'>取消</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { batchSetQuestionScore } from '@/api/test-api'
export default {
  name: 'SetScore',
  props: {
    testPaperId: {
      type: Number
    }
  },
  data() {
    return {
      show: false,
      loading: false,
      choiceScore: 0,
      fillScore: 0,
      simpleScore: 0,
      essayScore: 0,
      listObj: {},
      choiceError: false,
      fillError: false,
      simpleError: false,
      essayError: false
    }
  },
  methods: {
    validateInput(type) { // 正则判断是否正整数
      const reg = /^[1-9]\d*$/
      if (type === 'choice' && this.listObj.choiceList.length > 0) {
        if (this.choiceScore === '' || this.choiceScore === null || !reg.test(this.choiceScore)) {
          this.choiceError = true
          this.$message.error('请选择大于0的整数分值')
        } else {
          this.choiceError = false
        }
      } else if (type === 'fill' && this.listObj.fillList.length > 0) {
        if (this.fillScore === '' || this.fillScore === null || !reg.test(this.fillScore)) {
          this.fillError = true
          this.$message.error('请选择大于0的整数分值')
        } else {
          this.fillError = false
        }
      } else if (type === 'simple' && this.listObj.simpleList.length > 0) {
        if (this.simpleScore === '' || this.simpleScore === null || !reg.test(this.simpleScore)) {
          this.simpleError = true
          this.$message.error('请选择大于0的整数分值')
        } else {
          this.simpleError = false
        }
      } else if (type === 'essay' && this.listObj.essayList.length > 0) {
        if (this.essayScore === '' || this.essayScore === null || !reg.test(this.essayScore)) {
          this.essayError = true
          this.$message.error('请选择大于0的整数分值')
        } else {
          this.essayError = false
        }
      }
    },
    open(listObj) {
      this.show = true
      this.listObj = listObj
      if (listObj.choiceList.length > 0) {
        this.choiceScore = listObj.choiceList[0].score || 0
      }
      if (listObj.fillList.length > 0) {
        this.fillScore = listObj.fillList[0].score || 0
      }
      if (listObj.simpleList.length > 0) {
        this.simpleScore = listObj.simpleList[0].score || 0
      }
      if (listObj.essayList.length > 0) {
        this.essayScore = listObj.essayList[0].score || 0
      }
    },
    close() {
      this.show = false
      this.choiceScore = 0
      this.fillScore = 0
      this.simpleScore = 0
      this.essayScore = 0
      this.listObj = {}
    },
    async handleSave() {
      if (this.choiceError || this.fillError || this.simpleError || this.essayError) {
        this.$message.error('请修正输入的分值')
        return
      }
      try {
        this.loading = true
        await batchSetQuestionScore({
          testPaperId: this.testPaperId,
          questionTypeMap: {
            CHOICE: Number(this.choiceScore),
            FILL_IN_THE_BLANK_INPUT: Number(this.fillScore),
            SIMPLE_CHOOSE: Number(this.simpleScore),
            ESSAY_QUESTION: Number(this.essayScore)
          }
        })
        this.$emit('saveSuccess')
        this.close()
      } catch (e) {
        this.$message.error('保存失败，请重试')
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped lang='scss'>
.set-body{
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 102;
  display: flex;
  align-items: center;
  justify-content: center;
  .set-main{
    width: 30%;
    max-height: 90vh;
    background: white;
    border-radius: 10px;
    padding: 10px 30px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    .header{
      width: 100%;
      height: 30px;
      line-height: 30px;
      font-size: 16px;
      color: rgba(0, 0, 0, 1);
      font-weight: 500;
    }
    .content{
      width: 100%;
      .content-item{
        width: 100%;
        height: 30px;
        line-height: 30px;
        font-size: 14px;
        color: rgba(0, 0, 0, 1);
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 15px;
        font-weight: 500;
        .item-left{
          height: 100%;
          width: 120px;
          text-align: left;
        }
        .item-center{
          width: calc(100% - 10px - 140px);
          text-align: center;
          margin: 0 5px;
        }
        .item-right{
          width: 20px;
          text-align: center;
        }
      }
    }
    .bottom{
      width: 100%;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 15px;
      margin-top: 20px;
    }
  }
}
::v-deep .is-error .el-input__inner {
  border-color: #F56C6C !important;
  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);
}
</style>
