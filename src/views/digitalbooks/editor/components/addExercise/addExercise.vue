<template>
  <div class="add-exercise-main" :class="{'no-conversion-pb40' : addSource === 'bank' || addSource === 'task'}" v-if='dialogShow'>
    <div class="header">
      <div class="flex items-center" @click="handleClose" style="cursor: pointer">
        <i class="el-icon-arrow-left"></i>
        <span>返回</span>
      </div>
      <div class="header-btn-view">
        <div v-if="addSource.includes('bank') && canEdit" class="header-btn save-btn" @click.s.stop="$refs.choiceQuestionsRef.open()">
          <i class="btn-icon el-icon-edit"></i>
          题库选择
        </div>
        <div v-if="addSource === 'task'" class="header-btn save-btn" @click.s.stop="$refs.choiceTrainingRef.open()">
          <i class="btn-icon el-icon-edit"></i>
          教材中导入
        </div>
        <div v-if="canEdit" class="header-btn ai-btn" @click="handleGenerate">
          <img src='@/assets/digitalbooks/edit/ai-btn.png' />
          AI出卷
        </div>
        <div class="header-btn export-btn" @click="handleExport">
          <i class="el-icon-s-order btn-icon"></i>
          导出为Word
        </div>
        <div v-if="addSource === 'editor'" class="header-btn save-btn" @click.s.stop="handleSave">
          <i class="el-icon-s-promotion btn-icon"></i>
          保存到教材
        </div>
      </div>
    </div>
    <div class="content" @scroll="checkSectionInView">
      <div class="content-title">
        <div class="title">
          <div style="height: 100%;max-width: 50%;position: relative;display: flex;align-items: center;justify-content: center">
            <div style="width: 100%;white-space: pre-wrap;text-align: center" v-if="!isEditName">{{ exerciseName }}</div>
            <el-input v-else v-model="exerciseName" placeholder='请输入习题集名称' @keyup.enter.native="isEditName = false" @blur="isEditName = false" maxlength='30' show-word-limit/>
            <div class="title-btn" v-if="canEdit">
              <div class="edit-btn" @click="isEditName = true">
                <i class="el-icon-edit-outline"></i>
              </div>
              <div v-if="canEdit" class="scoreBtn" @click.stop="handleSetScore">
                批量设置分数
              </div>
            </div>
          </div>
        </div>
        <div class="subTitle">
          <span>成功生成题目后，可调整题目顺序、修改内容并导出使用</span>
          <div class="tip">
            拖拽题目左侧手柄可调整顺序
          </div>
        </div>
      </div>
      <div class="content-view">
        <div class="content-main">
          <el-collapse v-model='activeNames'>
            <el-collapse-item name='choice'>
              <template slot="title">
                <div class="collapse-title-view">
                  <div class="title-left flex items-center">
                    <div class="title">
                      选择题
                    </div>
                    <div class="subtitle">
                      共{{ choiceList.length }}题
                    </div>
                  </div>
                  <div class="title-right flex items-center" v-if="canEdit">
                    <div class="addBtn" @click.stop="addQuestion('choice')">
                      <i class="el-icon-plus"></i>
                      添加题目
                    </div>
                  </div>
                </div>
              </template>
              <div v-if="choiceList.length === 0" class="empty-view flex justify-center align-center">
                <Empty :image-size='60'  :image="empty3" description="暂无数据" />
              </div>
              <draggable v-model="choiceList" :handle="'.drag-handle'" chosen-class="" force-fallback="true" group="choice" animation="1000" @start="dragging = true" @end="dragging = false" @change="onEndDrag($event, 'choice')">
                <transition-group>
                  <div ref='choiceItem' style='width: 100%;' v-for="(item, index) in choiceList" :key="index" @click="handleClickQuestion('choice', index)">
                    <QuestionItem
                      :question-data="item"
                      :index-string="`${index + 1}.`"
                      :is-active="item.isActive"
                      :is-editing="item.isEdit"
                      :test-id="testId"
                      :test-paper-title="exerciseName"
                      :book-id='bookId'
                      :source='addSource'
                      :can-edit='canEdit'
                      @update-test-id="testId = $event"
                      @refresh-questions="getExerciseData"
                      @delete-question="deleteQuestion('choice', item)"/>
                  </div>
                </transition-group>
              </draggable>
            </el-collapse-item>
            <el-collapse-item name='fill'>
              <template slot="title">
                <div class="collapse-title-view">
                  <div class="title-left flex items-center">
                    <div class="title">
                      填空题
                    </div>
                    <div class="subtitle">
                      共{{ fillList.length }}题
                    </div>
                  </div>
                  <div class="title-right flex items-center" v-if="canEdit">
                    <div class="addBtn" @click.stop="addQuestion('fill')">
                      <i class="el-icon-plus"></i>
                      添加题目
                    </div>
                  </div>
                </div>
              </template>
              <div v-if="fillList.length === 0" class="empty-view flex justify-center align-center">
                <Empty :image-size='60'  :image="empty3" description="暂无数据" />
              </div>
              <draggable v-model="fillList" :handle="'.drag-handle'" chosen-class="" force-fallback="true" group="fill" animation="1000" @start="dragging = true" @end="dragging = false" @change="onEndDrag($event, 'fill')">
                <transition-group>
                  <div ref='fillItem' style='width: 100%;' v-for="(item, index) in fillList" :key="index" @click="handleClickQuestion('fill', index)">
                    <QuestionItem
                      :question-data="item"
                      :index-string="`${index + 1}.`"
                      :is-active="item.isActive"
                      :is-editing="item.isEdit"
                      :test-id="testId"
                      :test-paper-title="exerciseName"
                      :book-id='bookId'
                      :source='addSource'
                      :can-edit='canEdit'
                      @update-test-id="testId = $event"
                      @refresh-questions="getExerciseData"
                      @delete-question="deleteQuestion('fill', item)"/>
                  </div>
                </transition-group>
              </draggable>
            </el-collapse-item>
            <el-collapse-item name='simple'>
              <template slot="title">
                <div class="collapse-title-view">
                  <div class="title-left flex items-center">
                    <div class="title">
                      判断题
                    </div>
                    <div class="subtitle">
                      共{{ simpleList.length }}题
                    </div>
                  </div>
                  <div class="title-right flex items-center" v-if="canEdit">
                    <div class="addBtn" @click.stop="addQuestion('simple')">
                      <i class="el-icon-plus"></i>
                      添加题目
                    </div>
                  </div>
                </div>
              </template>
              <div v-if="simpleList.length === 0" class="empty-view flex justify-center align-center">
                <Empty :image-size='60'  :image="empty3" description="暂无数据" />
              </div>
              <draggable v-model="simpleList" :handle="'.drag-handle'" chosen-class="" force-fallback="true" group="simple" animation="1000" @start="dragging = true" @end="dragging = false" @change="onEndDrag($event, 'simple')">
                <transition-group>
                  <div ref='simpleItem' style='width: 100%;' v-for="(item, index) in simpleList" :key="index" @click="handleClickQuestion('simple', index)">
                    <QuestionItem
                      :question-data="item"
                      :index-string="`${index + 1}.`"
                      :is-active="item.isActive"
                      :is-editing="item.isEdit"
                      :test-id="testId"
                      :test-paper-title="exerciseName"
                      :book-id='bookId'
                      :source='addSource'
                      :can-edit='canEdit'
                      @update-test-id="testId = $event"
                      @refresh-questions="getExerciseData"
                      @delete-question="deleteQuestion('simple', item)"/>
                  </div>
                </transition-group>
              </draggable>
            </el-collapse-item>
            <el-collapse-item name='shortAnswer'>
              <template slot="title">
                <div class="collapse-title-view">
                  <div class="title-left flex items-center">
                    <div class="title">
                      简答题
                    </div>
                    <div class="subtitle">
                      共{{ essayList.length }}题
                    </div>
                  </div>
                  <div class="title-right flex items-center" v-if="canEdit">
                    <div class="addBtn" @click.stop="addQuestion('shortAnswer')">
                      <i class="el-icon-plus"></i>
                      添加题目
                    </div>
                  </div>
                </div>
              </template>
              <div v-if="essayList.length === 0" class="empty-view flex justify-center align-center">
                <Empty :image-size='60'  :image="empty3" description="暂无数据" />
              </div>
              <draggable v-model="essayList" :handle="'.drag-handle'" chosen-class="" force-fallback="true" group="shortAnswer" animation="1000" @start="dragging = true" @end="dragging = false" @change="onEndDrag($event, 'shortAnswer')">
                <transition-group>
                  <div ref='essayItem' style='width: 100%;' v-for="(item, index) in essayList" :key="index" @click="handleClickQuestion('shortAnswer', index)">
                    <QuestionItem
                      :question-data="item"
                      :index-string="`${index + 1}.`"
                      :is-active="item.isActive"
                      :is-editing="item.isEdit"
                      :test-id="testId"
                      :test-paper-title="exerciseName"
                      :book-id='bookId'
                      :source='addSource'
                      :can-edit='canEdit'
                      @update-test-id="testId = $event"
                      @refresh-questions="getExerciseData"
                      @delete-question="deleteQuestion('shortAnswer', item)"/>
                  </div>
                </transition-group>
              </draggable>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </div>
    <div v-if="addSource === 'bank'" class="bottom">
      <div v-if="canSaveDraft" class="header-btn export-btn" @click="saveDraft">
        <i class="el-icon-edit btn-icon"></i>
        保存草稿
      </div>
      <div class="header-btn save-btn" @click="nextStep">
        下一步
      </div>
    </div>
    <div v-if="addSource === 'create-bank'" class="bottom">
      <div v-if="canEdit" class="header-btn save-btn" @click="saveBank">
        <i class="el-icon-s-promotion btn-icon"></i>
        保存
      </div>
    </div>
    <div v-if="addSource === 'task'" class="bottom">
      <div v-if="canEdit" class="header-btn save-btn" @click="saveTask">
        <i class="el-icon-s-promotion btn-icon"></i>
        保存
      </div>
    </div>
    <AiAddConfig
      ref='AiAddConfigRef'
      :book-id="bookId"
      :has-data="hasData"
      :exercise-name='exerciseName'
      :test-id='testId'
      :source="addSource"
      :task-id='taskId'
      @updateExerciseName="updateExerciseName"
      @updateTestId="updateTestId"
      @generateSuccess="generateSuccess"/>
    <SetScore ref="setScoreRef" :test-paper-id='Number(testId)' @saveSuccess='getExerciseData'/>
    <ExportDialog
      :visible.sync="exportDialogVisible"
      :exercise-data="getExportData()"
      @export="handleExportComplete"
    />
    <GenerateByAIGC
      ref='generateRef'
      title="生成习题集"
      :content-title="{title0: '已有习题集内容，确定重新生成并覆盖：', title1: '习题集生成提示：'}"
      :content-obj="{content0: '①习题集确定为最终版本', content1: '②生成过程中时间较长，耐心等待'}"
      content-tip="*生成后会删除原习题集内容，不可恢复"
      :data-id='catalogueId'
      @handleNotify='handleNotify'
    />
    <ChoiceQuestions ref="choiceQuestionsRef" :book-id='bookId' :test-id='Number(testId)' @choice-success='getExerciseData'/>
    <CreateBank
      ref="createBankRef"
      :test-paper-name='exerciseName'
      :book-id='bookId'
      :task-id='taskId'
      :test-id='Number(testId)'
      :course-id='courseId'
      :total-score='totalScore'
      @publishSuccess='close'
      @updateName="updateExerciseName"/>
    <ChoiceTraining ref="choiceTrainingRef" :book-id='Number(bookId)' type='exercise' @choice="handleChoice"/>
  </div>
</template>

<script>
import ChoiceTraining from '@/views/digitalbooks/editor/components/choiceTraining/choiceTraining'
import CreateBank from '@/views/digitalbooks/questionBank/createBank'
import ChoiceQuestions from '@/views/digitalbooks/questionBank/components/choiceQuestions'
import GenerateByAIGC from '@/components/classPro/GenerateByAIGC/generateByAIGC'
import SetScore from '@/views/digitalbooks/editor/components/addExercise/setScore'
import draggable from 'vuedraggable'
import AiAddConfig from '@/views/digitalbooks/editor/components/addExercise/aiAddConfig'
import ExportDialog from '@/views/digitalbooks/editor/components/addExercise/exportDialog'
import empty3 from '@/assets/images/empty3.png'
import { Empty, MessageBox } from 'element-ui'
import QuestionItem from '@/views/digitalbooks/editor/components/addExercise/questionItem'
import store from '@/store'
import { createTest, dragQuestion, getTestPaperQuestionList, question } from '@/api/test-api'
import router from '@/router'
import { batchQuestion, digitalHomework, digitalTask } from '@/api/digital-api'
import { getToken } from 'utils/auth'
export default {
  name: 'AddExercise',
  components: {
    Empty,
    QuestionItem,
    AiAddConfig,
    ExportDialog,
    draggable,
    SetScore,
    GenerateByAIGC,
    ChoiceQuestions,
    CreateBank,
    ChoiceTraining
  },
  props: {
    bookId: {
      type: Number,
      default: 0
    },
    courseId: {
      type: Number,
      default: 0
    }
  },
  computed: {
    hasData() {
      return this.choiceList.length > 0 || this.fillList.length > 0 || this.simpleList.length > 0 || this.essayList.length > 0
    },
    totalScore() {
      return this.questionList.reduce((pre, cur) => {
        return pre + (cur.score || 0)
      }, 0)
    }
  },
  data() {
    return {
      empty3,
      dragging: false,
      dialogShow: false,
      testId: '0',
      ids: '',
      questionList: [],
      choiceList: [],
      fillList: [],
      simpleList: [],
      essayList: [],
      cbs: {},
      exerciseName: '习题集',
      isEditName: false,
      activeNames: ['choice', 'fill', 'simple', 'shortAnswer'],
      catalogueId: store.state.app.activeCatalogueId,
      exportDialogVisible: false,
      addSource: '',
      operationType: '',
      taskId: 0,
      canSaveDraft: true,
      canEdit: true,
      token: '',
      currentSection: null
    }
  },
  methods: {
    async onSubmit () {
      if (this.testId === '0') {
        this.$message.warning('请勿添加空题块!')
        return
      } else {
        const { data: testData } = await createTest({
          testPaperId: Number(this.testId),
          testPaperLinkType: 'DIGITAL_BOOK_CONTENT',
          testPaperTitle: this.exerciseName,
          sourceId: store.state.app.activeCatalogueId
        }, {
          authorization: this.token
        })
        this.cbs['updateTest'](this.exerciseName, String(testData.id), this.questionList.length, this.ids)
      }
      this.innitData()
      this.dialogShow = false
      document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '9999'
    },
    handleClose () {
      if (this.canEdit) {
        const totalQuestions = (this.choiceList?.length || 0) +
          (this.fillList?.length || 0) +
          (this.simpleList?.length || 0) +
          (this.essayList?.length || 0)
        if (totalQuestions === 0) {
          if (this.testId !== '0') {
            if (this.addSource === 'task') {
              this.deleteTask()
            } else {
              this.deleteTestPaper()
            }
            return
          } else {
            this.innitData()
            this.dialogShow = false
            this.$emit('closeAddExercise')
            if (this.addSource === 'editor')document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '9999'
            return
          }
        }
        let confirmButtonText = ''
        if (this.addSource === 'bank') {
          confirmButtonText = this.canSaveDraft ? '存为草稿' : '发布'
        } else if (this.addSource === 'create-bank' || this.addSource === 'task') {
          confirmButtonText = '保存'
        } else {
          confirmButtonText = '保存到教材'
        }
        if (this.operationType !== 'edit') {
          MessageBox.confirm('请确认是否有未保存的修改?', '提示', {
            confirmButtonText: confirmButtonText,
            cancelButtonText: '放弃',
            type: 'warning',
            distinguishCancelAndClose: true
          }).then(async () => {
            if (this.addSource === 'editor') {
              this.handleSave()
            } else if (this.addSource === 'create-bank') {
              await this.saveBank()
            } else {
              if (this.canSaveDraft) {
                await this.saveDraft()
              } else {
                this.nextStep()
              }
            }
          }).catch((err) => {
            if (err === 'cancel') {
              if (this.testId !== '0' && this.operationType !== 'edit') {
                if (this.addSource === 'task') {
                  this.deleteTask()
                } else {
                  this.deleteTestPaper()
                }
              } else {
                this.innitData()
                this.dialogShow = false
                this.$emit('closeAddExercise')
                if (this.addSource === 'editor')document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '9999'
              }
            }
          })
        } else {
          // this.updateTestPaper()
          this.close()
        }
      } else {
        this.close()
      }
    },
    async deleteTestPaper() {
      await createTest({
        testPaperId: Number(this.testId),
        apiType: 'delete'
      }, {
        authorization: this.token
      })
      this.innitData()
      this.dialogShow = false
      this.$emit('closeAddExercise')
      if (this.addSource === 'editor') {
        document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '9999'
      }
    },
    close () {
      if (this.addSource.includes('bank')){
        this.updateTestPaper()
      }
      this.innitData()
      this.dialogShow = false
      this.$emit('closeAddExercise')
      if (this.addSource === 'editor') {
        document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '9999'
      }
    },
    open (cbs = {}, source = 'editor', canSaveDraft = true, canEdit = true) {
      this.dialogShow = true
      this.addSource = source
      this.canSaveDraft = canSaveDraft
      this.canEdit = canEdit
      this.token = this.addSource === 'editor' ? 'Bearer ' + router.currentRoute.query.token : getToken()
      if (source === 'editor') {
        this.cbs = cbs
        document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '1'
      } else if (source.includes('bank')) {
        if (this.testId === '0') {
          this.createTestPaper()
        }
      } else if (source === 'task') {
        if (this.testId === '0') {
          this.createTask()
        }
      }
    },

    async createTestPaper() {
      const { data } = await createTest({
        testPaperLinkType: 'DIGITAL_HOMEWORK_TEACHING',
        referLinkSourceId: this.bookId,
        testPaperTitle: this.exerciseName,
        sourceId: 0
      }, {
        authorization: this.token
      })
      this.testId = String(data.id)
    },
    async updateTestPaper() {
      await createTest({
        testPaperLinkType: 'DIGITAL_HOMEWORK_TEACHING',
        referLinkSourceId: this.bookId,
        testPaperTitle: this.exerciseName,
        sourceId: 0,
        testPaperId: Number(this.testId)
      }, {
        authorization: this.token
      })
    },
    innitData() {
      this.testId = '0'
      this.questionList = []
      this.exerciseName = '习题集'
      this.choiceList = []
      this.fillList = []
      this.simpleList = []
      this.essayList = []
      this.addSource = ''
      this.operationType = ''
      this.canSaveDraft = true
      this.canEdit = true
      this.taskId = 0
      this.ids = ''
      this.isEditName = false
    },
    setData (data, source = 'editor') {
      if (source === 'editor') {
        this.testId = String(data.testId)
        this.ids = data.ids
      } else if (source === 'bank') {
        this.testId = String(data.sourceId)
        this.taskId = data.id
      } else if (source === 'create-bank') {
        this.testId = String(data.id)
        this.taskId = data.id
      } else if (source === 'task') {
        this.testId = String(data.id)
        this.taskId = data.taskId
        this.exerciseName = data.title
      }
      this.operationType = 'edit'
      this.getExerciseData(source !== 'task')
    },
    onEndDrag(item, type) {
      let list = []
      if (type === 'choice') {
        list = this.choiceList
      } else if (type === 'fill') {
        list = this.fillList
      } else if (type === 'simple') {
        list = this.simpleList
      } else if (type === 'shortAnswer') {
        list = this.essayList
      }
      this.drag = false
      if (item.timeStamp < 1000) {
        return
      }
      dragQuestion({ testPaperId: this.testId, questionId: item.moved.element.id, previousQuestionId: item.moved.newIndex === 0 ? null : list[item.moved.newIndex - 1 ].id }, {
        authorization: this.token
      })
    },
    handleClickQuestion(type, index) {
      this.choiceList.forEach(item => {
        item.isActive = false
      })
      this.fillList.forEach(item => {
        item.isActive = false
      })
      this.simpleList.forEach(item => {
        item.isActive = false
      })
      this.essayList.forEach(item => {
        item.isActive = false
      })
      if (type === 'choice') {
        this.choiceList[index].isActive = true
      } else if (type === 'fill') {
        this.fillList[index].isActive = true
      } else if (type === 'simple') {
        this.simpleList[index].isActive = true
      } else if (type === 'shortAnswer') {
        this.essayList[index].isActive = true
      }
    },
    handleSetScore() {
      const totalQuestions = (this.choiceList?.length || 0) +
        (this.fillList?.length || 0) +
        (this.simpleList?.length || 0) +
        (this.essayList?.length || 0)

      if (totalQuestions === 0) {
        this.$message.warning('请生成题目后再进行设置分数')
        return
      }
      this.$refs.setScoreRef.open({
        choiceList: this.choiceList,
        fillList: this.fillList,
        simpleList: this.simpleList,
        essayList: this.essayList
      })
    },
    addQuestion(type) {
      let list = []
      switch (type) {
        case 'choice':
          this.choiceList.push({
            question: '',
            questionType: 'CHOICE',
            answer: '',
            analysis: '',
            answerOptionList: [
              {
                answer: '',
                right: true
              },
              {
                answer: '',
                right: false
              },
              {
                answer: '',
                right: false
              },
              {
                answer: '',
                right: false
              }
            ],
            score: 0,
            isEdit: true,
            isActive: false,
            knowledges: []
          })
          list = this.$refs.choiceItem
          break
        case 'fill':
          this.fillList.push({
            question: '',
            questionType: 'FILL_IN_THE_BLANK_INPUT',
            answer: '',
            analysis: '',
            answerOptionList: [],
            score: 0,
            isEdit: true,
            isActive: false,
            knowledges: []
          })
          list = this.$refs.fillItem
          break
        case 'simple':
          this.simpleList.push({
            question: '',
            questionType: 'SIMPLE_CHOOSE',
            answer: '',
            analysis: '',
            answerOptionList: [
              {
                answer: '正确',
                right: true
              },
              {
                answer: '错误',
                right: false
              }
            ],
            score: 0,
            isEdit: true,
            isActive: false,
            knowledges: []
          })
          list = this.$refs.simpleItem
          break
        case 'shortAnswer':
          this.essayList.push({
            question: '',
            questionType: 'ESSAY_QUESTION',
            answer: '',
            analysis: '',
            answerOptionList: [],
            score: 0,
            isEdit: true,
            isActive: false,
            knowledges: []
          })
          list = this.$refs.essayItem
          break
      }
      this.$nextTick(() => {
        const itemEl = list[list.length - 1]
        if (itemEl) {
          itemEl.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          })
        }
      })
    },
    async deleteQuestion(type, item) {
      if (item.id && item.id !== '') {
        const params = {
          apiType: 'delete',
          testPaperId: this.testId
        }
        await question(params, { id: item.id }, {
          authorization: this.token
        })
        await this.getExerciseData()
      } else {
        if (type === 'choice') {
          this.choiceList = this.choiceList.filter(i => i !== item)
        } else if (type === 'fill') {
          this.fillList = this.fillList.filter(i => i !== item)
        } else if (type === 'simple') {
          this.simpleList = this.simpleList.filter(i => i !== item)
        } else if (type === 'shortAnswer') {
          this.essayList = this.essayList.filter(i => i !== item)
        }
      }
    },
    updateTestId(testId) {
      this.testId = String(testId)
    },
    updateExerciseName(exerciseName) {
      this.exerciseName = exerciseName
    },
    generateSuccess(questionObj) {
      this.testId = String(questionObj.exerciseId)
      this.exerciseName = questionObj.exerciseName
      this.getExerciseData()
    },
    async getExerciseData(first = false) {
      try {
        const { data } = await getTestPaperQuestionList({ testPaperId: Number(this.testId) })
        this.questionList = data.questionList
        if (first) {
          this.exerciseName = data.title
        }
        this.ids = this.questionList.reduce((pre, cur) => {
          pre.push(String(cur.id))
          return pre
        }, []).join(',')
        this.questionList.forEach(item => {
          this.$set(item, 'isActive', false)
          this.$set(item, 'isEdit', false)
        })
        this.choiceList = this.questionList.filter((item) => { return item.questionType === 'CHOICE' })
        this.fillList = this.questionList.filter((item) => { return item.questionType === 'FILL_IN_THE_BLANK_INPUT' })
        this.simpleList = this.questionList.filter((item) => { return item.questionType === 'SIMPLE_CHOOSE' })
        this.essayList = this.questionList.filter((item) => { return item.questionType === 'ESSAY_QUESTION' })
      } catch (e) {
        console.log(e)
      }
    },
    handleSave() {
      this.onSubmit()
    },
    handleExport() {
      const totalQuestions = (this.choiceList?.length || 0) +
                           (this.fillList?.length || 0) +
                           (this.simpleList?.length || 0) +
                           (this.essayList?.length || 0)

      if (totalQuestions === 0) {
        this.$message.warning('请生成题目后再进行导出')
        return
      }

      this.exportDialogVisible = true
    },

    getExportData() {
      return {
        exerciseName: this.exerciseName,
        testId: this.testId,
        catalogueId: this.catalogueId,
        questionList: this.questionList,
        choiceList: this.choiceList,
        fillList: this.fillList,
        simpleList: this.simpleList,
        essayList: this.essayList
      }
    },
    handleExportComplete() {
      this.exportDialogVisible = false
      this.$message.success('导出完成！')
    },
    handleNotify() {
      this.$refs.generateRef.close()
      this.$refs.AiAddConfigRef.open()
    },
    handleGenerate() {
      if (this.hasData) {
        this.$refs.generateRef.show(true)
      } else {
        this.$refs.AiAddConfigRef.open()
      }
    },
    // 题库相关操作
    nextStep() {
      const totalQuestions = (this.choiceList?.length || 0) +
        (this.fillList?.length || 0) +
        (this.simpleList?.length || 0) +
        (this.essayList?.length || 0)
      if (totalQuestions === 0) {
        this.$message.warning('请生成题目后再进行下一步')
        return
      }
      this.$refs.createBankRef.open()
    },
    async saveDraft() {
      const totalQuestions = (this.choiceList?.length || 0) +
        (this.fillList?.length || 0) +
        (this.simpleList?.length || 0) +
        (this.essayList?.length || 0)
      if (totalQuestions === 0) {
        this.$message.warning('请生成题目后再进行保存')
        return
      }
      await this.updateTestPaper()
      const params = {
        apiType: this.taskId === 0 ? 'create' : 'update',
        title: this.exerciseName,
        status: 'INACTIVE',
        sourceId: this.testId,
        digitalHomeworkType: 'TEACHING_PRACTICE',
        digitalBookId: this.bookId,
        studentCourseId: this.courseId
      }
      if (this.taskId !== 0) {
        params.id = this.taskId
      }
      await digitalHomework(params)
      this.$message.success('草稿保存成功')
      // this.sourceId = params.id || this.sourceId // 后端未返回id，暂时注释
      this.close()
    },
    async saveBank() {
      const totalQuestions = (this.choiceList?.length || 0) +
        (this.fillList?.length || 0) +
        (this.simpleList?.length || 0) +
        (this.essayList?.length || 0)
      if (totalQuestions === 0) {
        this.$message.warning('请生成题目后再进行保存')
        return
      }
      await createTest({
        testPaperId: Number(this.testId),
        testPaperLinkType: 'DIGITAL_HOMEWORK_TEACHING',
        testPaperTitle: this.exerciseName,
        referLinkSourceId: this.bookId
      }, {
        authorization: this.token
      })
      this.$message.success('保存成功')
      this.close()
    },
    // 任务相关操作
    async createTask() {
      const { data } = await digitalTask({
        apiType: 'create',
        digitalBookId: this.bookId,
        title: this.exerciseName,
        digitalHomeworkType: 'TESTPAPER',
        digitalCatalogueId: this.catalogueId
      }, {}, { authorization: this.token })
      this.taskId = data.id
      this.testId = String(data.sourceId)
    },
    async updateTask() {
      await digitalTask({
        apiType: 'update',
        id: this.taskId,
        digitalBookId: this.bookId,
        title: this.exerciseName,
        digitalHomeworkType: 'TESTPAPER'
      }, {}, { authorization: this.token })
    },
    async deleteTask() {
      await digitalTask({
        apiType: 'delete',
        id: this.taskId
      }, {}, {
        authorization: this.token
      })
      this.innitData()
      this.dialogShow = false
      this.$emit('closeAddExercise')
    },
    async saveTask() {
      const totalQuestions = (this.choiceList?.length || 0) +
        (this.fillList?.length || 0) +
        (this.simpleList?.length || 0) +
        (this.essayList?.length || 0)
      if (totalQuestions === 0) {
        this.$message.warning('请生成题目后再进行保存')
        return
      }
      await this.updateTask()
      this.$message.success('保存成功')
      this.close()
    },
    async handleChoice(id) {
      const { data } = await getTestPaperQuestionList({ testPaperId: Number(id) })
      const questionList = data.questionList
      await batchQuestion({
        id: this.testId,
        questionList: questionList.map(item => {
          delete item.id
          if (item.answerOptionList) {
            item.answerOptionList.map(option => { delete option.id; return option })
          }
          return item
        })
      }, {
        overwrite: true
      }, {
        authorization: this.token
      })
      await this.getExerciseData()
    },
    checkSectionInView() {
      // let currentSection = null
      //每个类型一个块
      const items = document.querySelectorAll('.el-collapse-item')
      let newCurrentSection = null
      items.forEach(item => {
        const rect = item.getBoundingClientRect()
        // 检查当前部分是否在视口中
        // if (rect.top <= 100 && rect.bottom >= 100) {
        if (rect.top <= 10 && rect.bottom >= 10) {
          newCurrentSection = item
        }
        // 如果当前部分改变，更新浮动标题
        if (newCurrentSection !== this.currentSection) {
          if (this.currentSection) {
            let b1=this.currentSection.getElementsByClassName('el-collapse-item__header')[0]
            // let b1=this.currentSection.getElementsByClassName('collapse-title-view')[0]
            b1.classList.remove('scrollheader')
            // this.$refs.b1.classList.remove('scrollheader')
            // b1.style.position=''
            // b1.style.zIndex=''
            // b1.style.top=''
            // b1.style.width=''
          }
          if (newCurrentSection) {
            let b2=newCurrentSection.getElementsByClassName('el-collapse-item__header')[0]
            // let b2=newCurrentSection.getElementsByClassName('collapse-title-view')[0]
            b2.classList.add('scrollheader')
            // this.$refs.b2.classList.add('scrollheader')
            // b2.style.position='fixed'
            // b2.style.zIndex='111'
            // b2.style.top='0px'
            // b2.style.width='50%'
          }
          this.currentSection = newCurrentSection
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.no-conversion-pb40{
  padding-bottom: 40px;
}
.add-exercise-main{
  width: 100vw;
  height: 100vh;
  background: #ffffff;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 100;
  .bottom{
    width: 100%;
    height: 40px;
    background: transparent;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 15px;
    position: fixed;
    bottom: 0;
    z-index: 101;
    gap: 40px;
  }
  .header-btn{
    height: 35px;
    padding: 0 15px;
    border-radius: 5px;
    cursor: pointer;
    color: white;
    font-size: 14px;
    display: flex;
    align-items: center;
    img{
      width: 16px;
      height: 16px;
      object-fit: cover;
      margin-right: 5px;
    }
    .btn-icon{
      font-size: 14px;
      margin-right: 5px;
    }
    &:hover{
      opacity: 0.8;
    }
  }
  .ai-btn{
    background: linear-gradient(95.99deg, #B721FF -67.63%, #21D4FD 109.16%);
  }
  .export-btn{
    background: linear-gradient(90deg, #0BA360 0%, #3CBA92 100%);
  }
  .save-btn{
    background: rgba(47, 128, 237, 1);
  }
  .header{
    width: 100%;
    height: 50px;
    background: transparent;
    //background: #A1C4FD;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;
    position: fixed;
    top: 0;
    z-index: 101;
    .header-btn-view{
      height: 100%;
      display: flex;
      align-items: center;
      gap: 15px;
    }
  }
  .content{
    width: 100%;
    height: 100%;
    overflow: auto;
    .content-title{
      width: 100%;
      height: 160px;
      background: linear-gradient(180deg, #A1C4FD 0%, rgba(194, 233, 251, 0) 100%);
      padding: 45px 0 15px 0;
      overflow: auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      .title{
        height: 60px;
        width: 100%;
        font-size: 26px;
        display: flex;
        align-items: center;
        color: rgba(47, 128, 237, 1);
        font-weight: 600;
        justify-content: center;
        ::v-deep .el-input__inner{
          padding-right: 80px !important;
          height: 50px;
          line-height: 50px;
          font-size: 20px;
        }
        .title-btn{
          height: 100%;
          display: flex;
          align-items: center;
          position: absolute;
          right: -120px;
          top: 50%;
          transform: translateY(-50%);
        }
        .edit-btn{
          height: 20px;
          width: 20px;
          border-radius: 50%;
          border: 10px;
          background-color: rgba(201, 224, 255, 1);
          cursor: pointer;
          font-size: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-left: 5px;
          overflow: hidden;
          margin-right: 10px;
        }
      }
      .subTitle{
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: rgba(102, 102, 102, 1);
        font-size: 12px;
        .tip{
          margin-top: 10px;
          font-size: 10px;
          color: rgba(136, 136, 136, 1);
        }
      }
    }
    .content-view{
      width: 100%;
      //height: calc(100vh - 120px);
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      .content-main{
        width: 100%;
        padding: 0 20%;
        height: 100%;
        ::v-deep .el-collapse{
          border-top: 0;
        }
        ::v-deep .el-collapse-item__header{
          height: 50px;
          border-bottom: 1px solid #e6ebf5 !important;
        }
        ::v-deep .el-collapse-item__content{
          border-bottom: 0 !important;
          padding-bottom: 10px;
        }
        ::v-deep .el-collapse-item__wrap{
          border-bottom: 0 !important;
        }
        .collapse-title-view{
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .title{
            font-size: 20px;
            font-weight: 700;
            height: 50px;
            line-height: 50px;
            //width: 70px;
            border-bottom: 3px solid rgba(47, 128, 237, 1);
          }
          .subtitle{
            font-size: 12px;
            background-color: rgba(230, 240, 255, 1);
            border-radius: 5px;
            color: rgba(47, 128, 237, 1);
            margin-left: 10px;
            height: 20px;
            line-height: 20px;
            padding: 1px 8px;
          }
          .addBtn{
            height: 30px;
            line-height: 30px;
            background: rgba(246, 250, 255, 1);
            border-radius: 5px;
            border: 1px solid rgba(47, 128, 237, 0.34);
            color: rgba(47, 128, 237, 1);
            font-size: 12px;
            padding: 0 5px;
            margin-right: 5px;
            cursor: pointer;
            &:hover{
              background: rgba(47, 128, 237, 0.1);
            }
            i {
              margin-right: 2px;
            }
          }
        }
      }
    }
  }
  .scoreBtn{
    color: rgba(0, 0, 0, 1);
    cursor: pointer;
    text-decoration: underline;
    font-size: 12px;
    margin-right: 10px;
    &:hover{
      color: rgba(47, 128, 237, 1);
    }
  }
  .empty-view{
    height: 120px;
  }
  ::v-deep .el-empty__description{
    font-size: 14px;
    color: rgba(136, 136, 136, 1);
    margin-top: 10px;
  }
  ::v-deep .el-empty__description p{
    font-size: 14px;
    color: rgba(136, 136, 136, 1);
  }
  ::v-deep .el-input__count{
    font-size: 14px !important;
  }
  ::v-deep .scrollheader{
    position: fixed;
    top: 1px;
    z-index: 10000;
    width: 60%;
    background-color: white;
  }
}

</style>
