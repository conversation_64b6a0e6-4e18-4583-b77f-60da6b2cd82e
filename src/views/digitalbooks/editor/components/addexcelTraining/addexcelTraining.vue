<template>
  <div v-if="dialogShow" class="pop_main">
    <div class="header">
      <div class="flex items-center" @click="onCancel">
        <img class="icon" src="@/assets/images/skyclass/arrow-back.png" alt="返回按钮" />
        <span>返回</span>
      </div>
      总分:{{ scoreAll }}
      <div class='flex align-center'>
        <el-button v-if="sourceType === 'task'" type="primary" class="button" @click="choiceTraining">教材中选择</el-button>
        <el-button :loading="saveLoading" type="primary" class="button" @click="saveTraing">保存</el-button>
      </div>
    </div>
    <div class="content">
      <el-form ref="Form" :model="ruleForm" :rules="rules" label-width="10%" class="demo-ruleForm">
        <el-form-item label="实训标题" prop="trainingName">
          <el-input v-model="ruleForm.trainingName" maxlength="30" show-word-limit />
        </el-form-item>
        <el-form-item label="实验说明" prop="description">
          <TinymceEditor v-model="ruleForm.description" :init="init" />
        </el-form-item>
        <el-form-item label="实验步骤" prop="step">
          <div class="main">
            <div class="left">
              <div
                v-for="(content, index) in editorContents"
                :key="index"
                :class="index === tabIndex ? 'active' : ''"
                class="editor-container"
                @click="handleFocus(index)"
              >
                <TinymceEditor
                  v-model="editorContents[index].instructions"
                  :init="init"
                  @onFocus="handleFocus(index)"
                />
                <p class="title">步骤{{ index + 1 }}</p>
                <div class="delete_item" @click.stop="removeEditor(index)">
                  <i class="el-icon-error"></i>
                </div>
              </div>
              <el-button type="primary" class="add" @click="addEditor">添加步骤</el-button>
            </div>
            <div class="right">
              <div class="right_top">
                <el-upload
                  v-if="!editorContents[tabIndex].attachFileId"
                  class="upload"
                  action="''"
                  :before-upload="beforeUpload"
                  :accept="'.xls,.xlsx,.xlsm'"
                  :show-file-list="false"
                ><el-button
                  type="text"
                  class="text"
                >添加excel文档模版</el-button></el-upload>
                <div v-else id="whiteboard"></div>
              </div>
              <div class="button_group">
                <el-button type="text" class="button" @click="deleteExcel">删除模版</el-button>
              </div>
              <div class="anser_title">实验答案<el-tooltip
                class="toltips"
                effect="light"
                placement="top-end"
              >
                <span slot="content" class="tooltip-text">请输入自定义的答案标题和实验结果，如未填标题或者未填结果，系统将视为不正确的实验结果不录入</span>
                <i class="el-icon-warning-outline"></i>
              </el-tooltip></div>

              <div v-for="(item, index) in editorContents[tabIndex].rightResult" :key="index" class="anser_item">
                <el-input v-model="item.label" class="input1" placeholder="输入结果标题 如: E7" />
                <el-input v-model="item.anser" class="input2" placeholder="输入正确实验结果" />
                <el-input v-model="item.score" class="input3" type="number" placeholder="输入分数" />
                <el-button type="text" @click="deletAnserItem(index)">删除</el-button>
              </div>
              <div class="addAnser" @click="addAnserItem()"><i class="el-icon-plus"></i>添加实验字段</div>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <ChoiceTraining ref="choiceTrainingRef" :book-id='Number(bookId)' type='excel' @choice="handleChoice"/>
  </div>
</template>
<script>
import ChoiceTraining from '@/views/digitalbooks/editor/components/choiceTraining/choiceTraining'
import tinymce from 'tinymce/tinymce'
import TinymceEditor from '@tinymce/tinymce-vue'
import 'tinymce/themes/silver/theme'
import defaultConfig from '../../utils/traningConifg.js'
import { training, trainingStep, getTraining } from '@/api/training-api'
import { digitalTask, saveMediaFile } from '@/api/digital-api'
import { getFileUploadAuthor } from '@/api/user-api'
import axios from 'axios'
import { generateWebofficeToken, refreshWebofficeToken } from '@/api/aliyun-api'
import router from '../../../../../router'
import store from '../../../../../store'
import { Message, MessageBox } from 'element-ui'
import { isArray } from 'lodash'
import { addCase } from '@/views/digitalbooks/editor/utils/addCase'
tinymce.PluginManager.add('addCase', addCase)
export default {
  components: { TinymceEditor, ChoiceTraining },
  props: {
    bookId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      cbs: null,
      dialogShow: false,
      token: '',
      init: Object.assign(defaultConfig, {
      }),
      rules: {
        trainingName: [
          { required: true, message: '请输入实训标题', trigger: 'blur' },
          { min: 1, max: 30, message: '长度不能大于30', trigger: 'blur' }
        ]
      },
      ruleForm: {
        trainingName: '',
        description: ''
      },
      ossUrl: '',
      tabIndex: 0,
      demo: null,
      saveLoading: false,
      editorContents: [{
        instructions: '',
        rightResult: [{
          label: '',
          anser: '',
          score: null
        }],
        attachFileId: '',
        url: ''
      }],
      tokenList: {

      },
      sourceType: 'editor',
      taskId: '',
    }
  },
  computed: {
    scoreAll() {
      let num = 0
      this.editorContents.forEach(item => {
        item.rightResult.forEach(item1 => {
          num += Number(item1.score) || 0
        })
      })
      return num
    }
  },
  mounted() {
    this.token = `Bearer ${router.currentRoute.query.token}`
  },
  methods: {
    deletAnserItem(index) {
      try {
        MessageBox.confirm('确认删除答案?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          this.editorContents[this.tabIndex].rightResult.splice(index, 1)
        }).catch(() => {
          console.log('')
        })
      } catch (error) {
        console.log(error)
      }
    },
    addAnserItem() {
      this.editorContents[this.tabIndex].rightResult.push(
        {
          label: '',
          anser: '',
          score: null
        }
      )
    },
    deleteExcel() {
      try {
        MessageBox.confirm('确认删除附件?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          this.editorContents[this.tabIndex].url = ''
          this.editorContents[this.tabIndex].attachFileId = 0
        }).catch(() => {
          console.log('')
        })
      } catch (error) {
        console.log(error)
      }
    },
    initData() {
      this.tabIndex = 0
      this.editorContents = [{
        instructions: '',
        rightResult: [{
          label: '',
          anser: '',
          score: null
        }],
        attachFileId: '',
        url: ''
      }]
      this.ruleForm = {
        trainingName: '',
        description: ''
      }
    },
    async _getGenerateToken(url, token, id) {
      const header = { authorization: token }
      const { data } = await generateWebofficeToken({
        fileUrl: url,
        mediaFileId: id
      }, header)
      this.tokenList = data.body
      this.weboffice({
        AccessToken: data.body.accessToken,
        WebofficeURL: data.body.webofficeURL
      })
    },
    weboffice(tokenInfo) {
      var mount = document.getElementById('whiteboard')
      this.demo = window.aliyun.config({
        mount: mount,
        url: tokenInfo.WebofficeURL,
        refreshToken: this.refreshTokenPromise
      })
      this.demo.setToken({
        token: tokenInfo.AccessToken,
        timeout: 25 * 60 * 1000 // Token过期时间，单位为ms。25分钟之后刷新Token。
      })
    },
    async refreshTokenPromise() {
      const header = { authorization: this.token }
      const { data } = await refreshWebofficeToken({
        accessToken: this.tokenList.accessToken,
        refreshToken: this.tokenList.refreshToken
      }, header)
      return {
        token: data.body.accessToken,
        timeout: 10 * 60 * 1000
      }
    },
    async getOssSign(mediaType = '', fileName, quantity = 1) {
      try {
        const res = await getFileUploadAuthor({
          mediaType,
          contentType: '',
          quantity,
          fileName
        })

        return res
      } catch (error) {
        console.log(error)
        return Promise.reject(error)
      }
    },
    async beforeUpload(file) {
      const mediaType = 'OFFICE'
      const { data } = await this.getOssSign(mediaType, file.name)
      this.ossUrl = data[0].ossConfig.host
      const fileHoast = data[0].ossConfig.ossCDN
      this.progress = true
      const filename = file.name

      const formData = new FormData()
      formData.append('success_action_status', '200')
      formData.append('callback', '')
      formData.append('key', data[0].fileName)
      formData.append('policy', data[0].policy)
      formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
      formData.append('signature', data[0].signature)
      formData.append('file', file)

      await axios.post(this.ossUrl, formData, {
        onUploadProgress: (progress) => {
          const complete = Math.floor(progress.loaded / progress.total * 100)
          this.percent = complete
          if (complete >= 100) {
            this.progress = false
            this.percent = 0
          }
        }
      })

      const res = await saveMediaFile({
        fileName: filename,
        url: data[0].fileName,
        type: mediaType
      }, {
        size: Math.floor(file.size / 1024),
        type: mediaType,
        fileName: filename.substring(0, filename.lastIndexOf('.')),
        url: data[0].fileName,
        expendType: filename.substring(filename.lastIndexOf('.') + 1)
      }, { authorization: this.token }
      )
      this.editorContents[this.tabIndex].attachFileId = res.data.id
      this.editorContents[this.tabIndex].url = fileHoast + '/' + data[0].fileName
      this._getGenerateToken(fileHoast + '/' + data[0].fileName, this.token, res.data.id)
      this.$message.success('上传成功')
      return Promise.reject()
    },
    handleFocus(index, flag = true) {
      if (index === this.tabIndex) { return }
      this.demo && this.demo.destroy()
      this.demo = null
      this.tabIndex = index
      if (this.editorContents[index].url) {
        this._getGenerateToken(this.editorContents[index].url, this.token, this.editorContents[index].attachFileId)
      }
    },
    saveTraing() {
      this.$refs.Form.validate(async (valid) => {
        if (valid) {
          if (this.editorContents.length === 1 && !this.editorContents[0].instructions && !this.editorContents[0].attachFileId) {
            Message.warning('请至少添加一个不为空的步骤')
            return
          }
          this.saveLoading = true
          let linkSourceId = store.state.app.activeCatalogueId
          let trainingLinkType = 'DIGITAL_BOOK_CATALAGUE_CONTENT'
          if (this.sourceType === 'task') {
            const params = {
              digitalBookId: Number(this.bookId),
              title: this.ruleForm.trainingName,
              digitalHomeworkType: 'TRAINING'
            }
            if (this.taskId !== '') {
              params.id = this.taskId
              params.apiType = 'update'
            } else {
              params.apiType = 'create'
              params.digitalCatalogueId = store.state.app.activeCatalogueId
            }
            const { data: taskData } = await digitalTask(params, {}, { authorization: this.token })
            linkSourceId = taskData.id
            trainingLinkType = 'DIGITAL_BOOK_HOME_WORK_TASK'
          }
          const { data } = await training({ ...this.ruleForm, linkSourceId, trainingLinkType, trainingType: 'FINACE_PRACTICE' }, { authorization: this.token })
          for (let i = 0; i < this.editorContents.length; i++) {
            const parms = this.editorContents[i]
            await trainingStep({
              apiType: parms.trainingStepId ? 'update' : 'create'
            }, {
              ...parms,
              rightResult: JSON.stringify(parms.rightResult.filter(item => { return item.label !== '' && item.anser !== '' }).map(item1 => {
                return {
                  label: item1.label,
                  anser: item1.anser,
                  score: item1.score || null
                }
              })),
              trainingId: data.trainingId
            },
            { authorization: this.token })
          }
          if (this.sourceType === 'editor') {
            this.onSubmit({ title: '操作实训', subTitle: data.trainingName, id: data.trainingId })
          } else {
            this.$emit('saveSuccess')
          }
          this.saveLoading = false
          this.initData()
          this.dialogShow = false
        } else {
          return false
        }
      })
    },
    addEditor() {
      this.editorContents.push({
        instructions: '',
        rightResult: [{
          label: '',
          anser: ''
        }],
        attachFileId: '',
        url: ''
      })
    },
    // 删除特定索引的编辑器
    async removeEditor(index) {
      if (this.editorContents.length === 1) {
        Message.warning('请至少添加一个步骤')
        return
      }
      try {
        MessageBox.confirm('确认删除步骤?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          if (this.editorContents[index].trainingStepId) {
            await trainingStep({
              apiType: 'delete'
            }, {
              trainingStepId: this.editorContents[index].trainingStepId
            }, { authorization: this.token })
          }
          this.$nextTick(() => {
            this.handleFocus(0)
            this.editorContents.splice(index, 1)
          })
        }).catch(() => {
          console.log('')
        })
      } catch (error) {
        console.log(error)
      }
    },
    close() {
      this.dialogShow = false
      this.$nextTick(() => {
        this.initData()
        this.demo && this.demo.destroy()
        this.demo = null
      })
      document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '9999'
    },
    open(cbs = {}, sourceType = 'editor') {
      this.$nextTick(() => {
        this.dialogShow = true
      })
      this.sourceType = sourceType
      this.cbs = cbs
      if (this.sourceType === 'editor') {
        document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '1'
      }
    },
    onSubmit(data) {
      if (Object.prototype.toString.call(this.cbs['onSubmit']) === '[object Function]') {
        this.cbs['onSubmit'](data)
      }
      document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '9999'
    },
    isJSON(str) {
      if (typeof str === 'string') {
        try {
          JSON.parse(str)
          return true
        } catch (e) {
          return false
        }
      }
      return false
    },
    async setData(param, deleteId = false) {
      this.dialogShow = true
      this.taskId = param.taskId || ''
      if (param.sourceType && param.sourceType === 'task') {
        this.sourceType = 'task'
      }
      const { data } = await getTraining({
        trainingId: param.id
      }, { authorization: this.token })
      this.ruleForm = {
        ...data
      }
      this.editorContents = data.trainingStepList.map(item => {
        return {
          ...item, url: item.attachFile ? item.attachFile.url : '', rightResult: this.isJSON(item.rightResult) && isArray(JSON.parse(item.rightResult)) ? JSON.parse(item.rightResult) : [{
            label: '',
            anser: '',
            score: null
          }]
        }
      })
      if (deleteId) {
        delete this.ruleForm.trainingId
        this.editorContents.forEach(item => {
          delete item.trainingStepId
        })
      }
      this.tabIndex = 1
      this.handleFocus(0, false)
      console.log(this.editorContents)
      if (this.sourceType === 'editor')document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '1'
    },
    onCancel() {
      try {
        MessageBox.confirm('请确认是否有未保存的修改?', '提示', {
          confirmButtonText: '保存',
          cancelButtonText: '放弃',
          type: 'warning',
          distinguishCancelAndClose: true
        }).then(async () => {
          this.saveTraing()
        }).catch((err) => {
          if (err === 'cancel') {
            this.close()
          }
        })
      } catch (error) {
        console.log(error)
      }
    },
    choiceTraining() {
      this.$refs.choiceTrainingRef.open()
    },
    handleChoice(id) {
      this.setData({
        id: Number(id)
      }, true)
    }
  }
}
</script>

<style lang="scss" scoped>
.tooltip-text{
  font-size: 8px;
}
.anser_title{
  cursor: pointer;
  font-size: 12px;
}
.addAnser {
  width: 100px;
  font-size: 12px;
  cursor: pointer;
}

#whiteboard {
  width: 100%;
  height: 100%;
}

.anser_item {
  display: flex;
  justify-content: space-between;

  ::v-deep .el-input__inner {
    padding: 5px !important;
    padding-top: 5px;
    padding-bottom: 5px;
    height: 30px;
  }

  .input1 {
    width: 110px;
    height: 30px;
    font-size: 10px;
  }

  .input2 {
    width: 320px;
    font-size: 10px;
  }
  .input3 {
    width: 80px;
    font-size: 10px;
  }
}

.pop_main {
  width: 100vw;
  height: 100vh;
  background: #ffffff;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 98;
  overflow: auto;
  @include scrollBar;

  .header {
    width: 100%;
    height: 40px;
    padding: 8px;
    background: #F9F9F9;
    font-size: 12px;
    display: flex;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 98;
    justify-content: space-between;

    .icon {
      width: 14px;
      height: 14px;
      cursor: pointer;
      margin-right: 5px;
      cursor: pointer;
    }

    .button {
      min-width: 50px;
      height: 25px;
      font-size: 12px;
      padding: 5px;
    }
  }

  .content {
    width: 100%;
    padding: 10px;
    margin-top: 40px;

    .main {
      width: 100%;
      height: 500px;
      display: flex;
      justify-content: space-between;

      .left {
        width: 382px;
        height: 481px;
        overflow-x: hidden;
        overflow-y: auto;
        @include scrollBar;

        .editor-container {
          width: 370px;
          min-height: 100px;
          background: #FBFBFB;
          padding: 10px;
          padding-top: 20px;
          border-radius: 5px;
          position: relative;
          margin-top: 10px;
          box-sizing: border-box;
          border: 1px solid #ffffff;

          .title {
            position: absolute;
            font-size: 10px;
            font-weight: bold;
            top: -20px;
            left: 10px;
          }

          .delete_item {
            position: absolute;
            font-size: 14px;
            font-weight: bold;
            top: -8px;
            right: 10px;
            color: #BDBDBD;
            cursor: pointer;
          }
        }

        .active {
          border: 1px solid #98C4FF;
        }

        .add {
          font-size: 12px;
          width: 80px;
          padding: 5px;
          display: block;
          margin: 0 auto;
          margin-top: 20px;
        }

      }

      .right {
        width: 574px;
        height: 481px;

        .text {
          display: block;
          margin: 0 auto;
          font-size: 12px;
          margin-top: 30%;
        }

        .right_top {
          width: 574px;
          height: 401px;
          border: 1px solid #E3E3E3;

          ::v-deep .el-upload {
            width: 100%;
            height: 100%;
          }
        }

        .button_group {
          width: 100%;
          display: flex;
          justify-content: flex-end;
          margin-top: 5px;

          .button {
            font-size: 12px;
          }
        }

        .input {
          margin-top: 5px;
        }
      }
    }
  }
}
</style>
