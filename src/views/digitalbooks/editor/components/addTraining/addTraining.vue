<template>
  <NormalDialog
    v-if="dialogShow"
    width="700px"
    :title="title"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="flex flex-col editor-dig w">
      <!-- <div class="mb10">
        <p>类型：</p><el-select v-model="form.type" placeholder="请选择实训类型">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div> -->
      <div class="mb10">
        <p>标题：</p><el-input v-model="form.title" class="w" placeholder="标题" disabled />
      </div>
      <div class="mb10">
        <p>副标题：</p><el-input v-model="form.subTitle" class="w" placeholder="请输入副标题" maxlength="20" show-word-limit />
      </div>
    </div>
    <template #footer>
      <div class="edu-btn" @click="onSubmit">确定</div>
      <div v-if='taskId' class="edu-btn" @click="doTraining">查看实训</div>
    </template>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import store from '@/store'
import { digitalTask } from '@/api/digital-api'
import router from '@/router'
import { getSqlPlatformToken, training } from '@/api/training-api'
export default {
  components: { NormalDialog },
  props: {
    bookId: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      cbs: null,
      dialogShow: false,
      appendToBody: false,
      title: '添加实训',
      form: {
        title: '操作实训',
        subTitle: '',
        type: 'sql'
      },
      options: [{
        value: 'sql',
        label: 'SQL实训'
      }],
      sourceType: 'editor',
      taskId: null,
      token: '',
      openLoading: false
    }
  },
  mounted () {
    this.dialogShow = false
    this.token = `Bearer ${router.currentRoute.query.token}`
  },
  methods: {
    close () {
      this.dialogShow = false
      if (this.sourceType === 'editor') {
        document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '9999'
      }
      this.initData()
    },
    open (cbs = {}, sourceType = 'editor') {
      this.dialogShow = true
      this.cbs = cbs
      this.sourceType = sourceType
      if (sourceType === 'editor') {
        document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '1'
      }
    },
    initData () {
      this.form = {
        title: '操作实训',
        subTitle: '',
        type: 'sql'
      }
      this.taskId = null
      this.sourceType = 'editor'
    },
    async onSubmit () {
      if (this.sourceType === 'task') {
        const params = {
          digitalBookId: Number(this.bookId),
          title: this.form.title,
          digitalHomeworkType: 'TRAINING'
        }
        if (this.taskId) {
          params.id = this.taskId
          params.apiType = 'update'
        } else {
          params.apiType = 'create'
          params.digitalCatalogueId = store.state.app.activeCatalogueId
        }
        const { data: taskData } = await digitalTask(params, {}, { authorization: this.token })
        const linkSourceId = taskData.id
        const trainingLinkType = 'DIGITAL_BOOK_HOME_WORK_TASK'
        await training({
          linkSourceId,
          trainingLinkType,
          trainingType: 'SQL_PRACTICE',
          trainingName: this.form.title,
          description: this.form.subTitle,
          trainingId: this.form.trainingId || null
        }, { authorization: this.token })
        this.$emit('saveSuccess')
        this.close()
      } else {
        if (Object.prototype.toString.call(this.cbs['onSubmit']) === '[object Function]') {
          this.cbs['onSubmit'](this.form)
          this.form = {
            title: '操作实训',
            subTitle: '',
            type: 'sql'
          }
        }
        document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '9999'
      }
    },
    setData (data, sourceType = 'editor') {
      this.sourceType = sourceType
      if (sourceType === 'editor') {
        this.form = data
        document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '1'
      } else {
        this.form.title = data.title
        this.form.subTitle = data.subtitle || ''
        this.form.trainingId = data.trainingId || null
        this.taskId = data.taskId || null
        this.dialogShow = true
      }
    },
    onCancel () {
      if (Object.prototype.toString.call(this.cbs['onCancel']) === '[object Function]') {
        this.cbs['onCancel']()
      }
    },
    doTraining() {
      this.openLoading = true
      this.$message.success({ duration: 3000, message: '正在跳转请稍后...' })
      getSqlPlatformToken({}, { authorization: this.token }).then(res => {
        this.openLoading = false
        window.open(res.data)
      })
    }
  }
}
</script>

  <style lang="scss" scoped>

  .editor-dig {
    position: relative;
    z-index: 99999;
    ::v-deep .el-select{
      width: 83%;
      .el-input__suffix{
        top:13px
      }
      .is-focus{
        .el-input__suffix{
        top:-13px
      }
      }
      .el-input{
        width: 100%;
      }
    }
    ::v-deep .el-input{
      width: 83%;
    }
    ::v-deep .el-input__inner {
      transition: none;
    }
    .school-disc {
      margin-top: 10PX;
    }

    .mb10 {
      margin-bottom: 10PX;
      display: flex;
      justify-content: space-between;
      p{
        white-space: nowrap;
        line-height: 0px;
      }
    }

    .school-disc2 {
      width: 5PX;
      height: 5PX;
      background: #828282;
      border-radius: 50%;
      margin-right: 5PX;
    }
    .edu-btn{
      margin-left: 10px !important;
    }
  }
  </style>
