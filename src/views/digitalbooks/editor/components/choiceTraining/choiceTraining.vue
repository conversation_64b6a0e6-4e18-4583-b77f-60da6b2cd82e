<template>
  <div class="no-conversion" v-if='show'>
    <div class="choice-main">
      <div class="choice-title">
        选择题块
        <i class="el-icon-close" @click="close"></i>
      </div>
      <div class="choice-content" @click="handleChoice">
        <Read :pre-mode="true" :book-id-props="String(bookId)" source-type='task'/>
      </div>
    </div>
  </div>
</template>

<script>
import Read from '@/views/digitalbooks/read/index.vue'
export default {
  name: 'ChoiceTraining',
  components: { Read },
  props: {
    bookId: {
      type: Number,
      default: 0
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      show: false
    }
  },
  methods: {
    open() {
      this.show = true
    },
    close() {
      this.show = false
    },
    handleChoice(event) {
      const target = event.target;
      console.log(target)
      let className = ''
      let trainingType = ''
      switch (this.type) {
        case 'excel':
          className = 'excel_card';
          trainingType = '财务实训';
          break;
        case 'ai':
          className = 'ai_card';
          trainingType = '人工智能实训';
          break;
        case 'python':
          className = 'python_card';
          trainingType = 'Python实训';
          break;
        case 'exercise':
          className = 'test_card';
          trainingType = '习题';
          break;
      }
      const classList = ['excel_card', 'ai_card', 'python_card', 'test_card']
      if (target.className.includes(className) || target.parentNode.className.includes(className) || target.parentNode.parentNode.className.includes(className)) {
        let id = null
        if (target.className.includes(className)) {
          id = target.getAttribute('data-id')
        } else if (target.parentNode.className.includes(className)) {
          id = target.parentNode.getAttribute('data-id')
        } else if (target.parentNode.parentNode.className.includes(className)) {
          id = target.parentNode.parentNode.getAttribute('data-id')
        }
        this.$emit('choice', id)
        this.close()
      } else {
        if (classList.some(c => target.className.includes(c) || target.parentNode.className.includes(c) || target.parentNode.parentNode.className.includes(c))) {
          this.$message.warning(`请选择${trainingType}`)
        }
      }
    }
  }
}
</script>

<style scoped lang='scss'>
.no-conversion{
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 101;
  display: flex;
  align-items: end;
  justify-content: center;
  .choice-main{
    width: 100%;
    height: 90%;
    background: #e1effb;
    border-radius: 10px 10px 0 0;
    .choice-title{
      width: 100%;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 20px;
      padding: 0 20px;
      i {
        cursor: pointer;
      }
    }
    .choice-content{
      width: 100%;
      height: calc(100% - 60px);
      margin-top: 10px;
    }
  }
}
</style>
