<template>
  <li :class="{ 'editor-dig': !isDynamic }" class="li no-conversion">
    <!-- 点击折叠展开 -->
    <!-- <div class="li-box article-singer-container" @click="toggle"> -->
    <div
      :style="{ paddingLeft: `${index * 10 + 5}px`}"
      class="li-box"
      :class="{ 'li-active': +model.id === +heighitId, 'li-box-1': index === 0, 'li-box-3': index === 2 }"
      @click="handleClick(model)"
    >
      <!-- 显示内容 -->
      {{ model && model.title }}
      <div v-if="model.canRead" class="arrow">
        <span
          v-if="!isDynamic && isFolder"
          :class="open ? 'el-icon-caret-bottom' : 'el-icon-caret-right'"
          @click.stop="toggle"
        ></span>
      </div>
      <span v-if=" !isDynamic && !model.canRead" class="locked"><i class="el-icon-lock"></i></span>

      <!-- 显示折叠展开的图标，如果没有下级目录的话，则不显示 -->
      <!-- <span v-if="isFolder">[{{ open?'-':'+' }}]</span> -->
    </div>
    <!-- 控制是否显示下级目录 -->
    <ul v-show="open" v-if="isFolder" :style="{ paddingLeft: `${index * 10}px` }">
      <!-- 重点代码，调用自身，实现递归，绑定数据 -->
      <DigTree
        v-for="model in model.childCatalogue"
        :key="model && model.title"
        :model="model"
        :index="index + 1"
        :heighit-id="heighitId"
        :is-dynamic="isDynamic"
        @handleClick="handleClick"
      />
    </ul>
  </li>
</template>
<script>
export default {
  name: 'DigTree',
  props: {
    model: {
      type: [Object, Array],
      required: true
    },
    index: {
      type: Number,
      required: true
    },
    // 动态布局开关
    isDynamic: {
      type: Boolean,
      default: true
    },
    heighitId: {
      type: Number,
      required: false,
      default: 0
    }
  },
  data () {
    return {
      open: this.model.canRead
    }
  },
  computed: {
    // 控制是否有下级目录和显示下级目录
    isFolder () {
      return this.model.childCatalogue && this.model.childCatalogue.length
    }
  },
  methods: {
    // 点击折叠展开的方法
    toggle () {
      if (this.isFolder) {
        this.open = !this.open
      }
    },
    handleClick (item) {
      this.$emit('handleClick', item)
    }
  }
}
</script>

<style lang="scss" scoped>
.li{
  padding: 0 !important;
}
.no-conversion{
  .locked{
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
  }
  .li-box {
    padding: 5px 0;
    min-height: 40px;
    padding-right: 20px !important;
    font-size: 14px;
    line-height: 20px;
    display: flex;
    align-items: center;
    font-weight: 400;
    position: relative;
    cursor: pointer;
    &:hover {
      background: #E8F2FF;
      transform: translateX(5px)
    }
  }

  .li-box-1 {
    font-weight: 800 !important;
    font-size: 16px !important;
  }

  .li-box-3 {
    color: #5c5a5a;
  }

  .li-active {
    background: #E8F2FF;
  }

  .arrow {
    width: 20px;
    flex-shrink: 0;
    position: absolute;
    right: 2px;
    //top:3px
  }

  .editor-dig {
    .li-box {
      padding: 5px 0;
      min-height: 40px;
      padding-right: 26px !important;
      font-size: 14px;
      line-height: 30px;
      cursor: pointer;
      display: flex;
      align-items: center;
      position: relative;
      &:hover {
        background: #E8F2FF;
        transform: translateX(5px)
      }
    }

    .li-active {
      background: #E8F2FF;
    }

    .arrow {
      width: 20px;
    }
  }
}

</style>
