<template>
  <div class="question-view">
    <div class='header'>
      <div class="type-name">
        {{ questionData.questionType === 'CHOICE' ? '选择题' :
        questionData.questionType === 'FILL_IN_THE_BLANK_INPUT' ? '填空题' :
          questionData.questionType === 'SIMPLE_CHOOSE' ? '判断题' :
            questionData.questionType === 'ESSAY_QUESTION' ? '简答题' :
              questionData.questionType || '未知类型'}}
      </div>
      <div class="flex align-center">
        <div v-if="testPaperType === 1 && isWrong(null) && sourceType !== 'editor'" class="header-btn" @click="addWrong">加入错题</div>
        <img v-if="testPaperType === 1 && isRight(null)" src='@/assets/digitalbooks/edit/right.png' alt='' />
        <img v-if="testPaperType === 1 && isWrong(null)" src='@/assets/digitalbooks/edit/wrong.png' alt='' />
      </div>
    </div>
    <div class='content'>
      <div class='title'>
        <div class="index-view">{{ indexString }}</div>
        <div class="score-view">
          <span>（{{questionData.score}}分）</span>
        </div>
        <span v-if="questionData.questionType !== 'FILL_IN_THE_BLANK_INPUT'">
          {{formatterFill(questionData.question)}}
          <span>{{choiceType(questionData)}}</span>
        </span>
        <Fill
          v-else
          :test-paper-type="testPaperType"
          :original-string="questionData"
          :show-index="false"
          :show-score="false"
          @inputDown="submitFill"
        />
      </div>
      <div class="answer-item" v-for="(item, index) in questionData.answerOptionList" :key="index" @click='choiceAnswer(questionData,item)'>
        <div class="label" :class="{'label-select' : isSelected(item), 'label-wrong' : testPaperType === 1 && isWrong(item), 'label-right' : testPaperType === 1 && isRight(item)}">{{getOptionLabel(index)}}</div>
        <span :class="{'text-wrong' : testPaperType === 1 && isWrong(item)}">{{ item.answer }}</span>
      </div>
      <el-input
        v-if="testPaperType===0 && questionData.questionType === 'ESSAY_QUESTION'"
        v-model="itemData.answer"
        class="essay_input"
        type="textarea"
        maxlength="500"
        :rows="5"
        show-word-limit
        @blur="submitEssay(questionData)"
      />
      <div class="essay_answer" :class="{'essay_answer_wrong' : isWrong(null)}" v-if="testPaperType===1 && questionData.questionType === 'ESSAY_QUESTION' && questionData.answerUser">
        {{ questionData.answerUser && questionData.answerUser.answerIds }}
      </div>
    </div>
    <div v-if='testPaperType === 1' class='bottom'>
      <div class="flex align-center">
        <div class="bottom-tip answer-tip">{{ questionData.questionType !== 'ESSAY_QUESTION'?`题目答案`:'参考答案'}}</div>
        <div class="bottom-content" style="font-weight: 500">
          <span v-if="questionData.questionType !== 'ESSAY_QUESTION'">{{ getAnswer(questionData) }}</span>
          <span v-else>{{ questionData.answer }}</span>
        </div>
      </div>
      <div class="flex align-center">
        <div class="bottom-tip analysis-tip">题目解析</div>
        <div class="bottom-content">
          <span>{{ questionData.analysis || '暂无解析' }}</span>
        </div>
      </div>
      <div class="flex align-center" v-if='questionData.knowledges.length > 0'>
        <div class="bottom-tip point-tip">知识点</div>
        <div class="bottom-point">
          <div v-for="(item, index) in questionData.knowledges" :key='index' class="point-item">{{item}}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { userErrorQuestion } from '@/api/digital-api'
import Fill from '@/views/digitalbooks/editor/components/fill'
import { answerQuestion } from '@/api/test-api'
import router from '@/router'
export default {
  name: 'DoExerciseItem',
  components: {
    Fill
  },
  props: {
    testPaperType: {
      type: Number,
      default: 0
    },
    questionData: {
      type: Object,
      default: () => ({
        question: '',
        questionType: '',
        answer: '',
        analysis: '',
        answerOptionList: [],
        score: 0,
        knowledges: []
      })
    },
    indexString: {
      type: String,
      default: '1.'
    },
    testId: {
      type: String,
      default: '0'
    },
    sourceType: {
      type: String,
      default: 'editor'
    },
    courseId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      itemData: {
        answer: ''
      }
    }
  },
  watch: {
    questionData: {
      handler(newVal) {
        this.itemData.answer = newVal.answerUser ? newVal.answerUser.answerIds || '' : ''
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    choiceType(question) {
      if (question.questionType === 'CHOICE') {
        if (question.answer.split(',').length > 1) {
          return '（多选题）'
        } else {
          return ''
        }
      } else {
        return ''
      }
    },
    formatterFill(item) {
      return item.replace(/\[.*?\]/g, '___')
    },
    getOptionLabel(index) {
      const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
      return letters[index] || ''
    },
    choiceAnswer(item, option) {
      if (this.testPaperType !== 0) return
      let arr = item.answerUser && item.answerUser.answerIds ? item.answerUser.answerIds.split(',') : []
      if (arr.indexOf(String(option.id)) !== -1) {
        arr = arr.filter(function(item) {
          return item !== String(option.id)
        })
      } else {
        if (item.questionType === 'SIMPLE_CHOOSE' && arr.length > 0) {
          arr = [] // 判断题只能选择一个答案，清空之前的答案
        }
        arr.push(String(option.id))
      }
      arr = arr.sort((a, b) => {
        const indexA = item.answerOptionList.findIndex(obj => String(obj.id) === a)
        const indexB = item.answerOptionList.findIndex(obj => String(obj.id) === b)
        return indexA - indexB
      })
      let answer = ''
      if (arr.length === 0) {
        answer = ''
      } else if (arr.length === 1) {
        answer = arr[0]
      } else {
        answer = arr.join(',')
      }
      answerQuestion({ questionId: item.id, testPaperId: this.testId, studentCourseId: this.studentCourseId, answer: answer }, {
        authorization: router.currentRoute.query.token && 'Bearer ' + router.currentRoute.query.token
      }).then(res => {
        this.$emit('updateAnswerData', res.data)
      })
    },
    isSelected(option) {
      let selected = false
      if (this.testPaperType === 0) {
        if (this.questionData.answerUser && this.questionData.answerUser.answerIds.split(',').indexOf(String(option.id)) !== -1) {
          selected = true
        }
      }
      return selected
    },
    isWrong(option) {
      let isWrong = false
      if (!option) {
        if (this.questionData.answerUser && this.questionData.answerUser.result === 'WRONG') {
          isWrong = true
        }
      } else {
        if (this.questionData.answerUser && this.questionData.answerUser.answerIds.split(',').indexOf(String(option.id)) !== -1) {
          if (this.questionData.answerUser && this.questionData.answerUser.result === 'WRONG') {
            isWrong = true
          }
        }
      }
      return isWrong
    },
    isRight(option) {
      let isRight = false
      if (!option) {
        if (this.questionData.answerUser && this.questionData.answerUser.result !== 'WRONG') {
          isRight = true
        }
      } else {
        if (this.questionData.answerUser && this.questionData.answerUser.answerIds.split(',').indexOf(String(option.id)) !== -1) {
          if (this.questionData.answerUser && this.questionData.answerUser.result !== 'WRONG') {
            isRight = true
          }
        }
      }
      return isRight
    },
    submitFill(data) {
      if (this.testPaperType !== 0) return
      const item = data.item
      const answer = data.arr.join(',')
      answerQuestion({ questionId: item.id, testPaperId: this.testId, studentCourseId: this.studentCourseId, answer: answer }, {
        authorization: router.currentRoute.query.token && 'Bearer ' + router.currentRoute.query.token
      }).then(res => {
        this.$emit('updateAnswerData', res.data)
      })
    },
    submitEssay(item) {
      if (this.testPaperType !== 0) return
      answerQuestion({ questionId: item.id, testPaperId: this.testId, studentCourseId: this.studentCourseId, answer: this.itemData.answer }, {
        authorization: router.currentRoute.query.token && 'Bearer ' + router.currentRoute.query.token
      }).then(res => {
        this.$emit('updateAnswerData', res.data)
      })
    },
    getAnswer(question) {
      if (question.questionType === 'CHOICE' || question.questionType === 'SIMPLE_CHOOSE') {
        const list = question.answer.split(',')
        return question.answerOptionList
          .filter((option, index) => {
            option.index = index
            return list.includes(String(option.id))
          })
          .map(option => this.getOptionLabel(option.index))
          .join(', ')
      } else {
        return question.answer || ''
      }
    },
    async addWrong() {
      try {
        await userErrorQuestion({
          questionId: this.questionData.id,
          courseId: this.courseId,
          status: 'ACTIVE'
        })
        this.$message.success('已加入错题')
      } catch (e) {
        this.$message.error(e.message || '操作失败')
      }
    }
  }
}
</script>

<style scoped lang='scss'>
.question-view{
  width: 100%;
  min-height: 100px;
  padding: 15px 10px 20px 20px;
  border: 1px solid rgba(224, 224, 224, 1);
  box-shadow: 0px 4px 12px 0px rgba(47, 128, 237, 0.1);
  border-radius: 12px;
  margin-top: 15px;
  .header{
    width: 100%;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    img{
      width: 25px;
      height: 25px;
      margin-right: 10px;
    }
    .type-name{
      font-size: 14px;
      font-weight: 500;
      color: rgba(47, 128, 237, 1);
      background-color: rgba(230, 240, 255, 1);
      padding: 0 15px;
      border-radius: 15px;
      height: 25px;
    }
    .header-btn{
      font-size: 12px;
      background: rgba(47, 128, 237, 1);
      color: white;
      padding: 3px 10px;
      border-radius: 5px;
      cursor: pointer;
      margin-right: 15px;
    }
  }
  .content{
    width: 100%;
    margin-top: 10px;
    .title{
      width: 100%;
      font-size: 14px;
      padding: 0 60px 0 20px;
      position: relative;
      min-height: 25px;
      .index-view{
        position: absolute;
        left: 0;
        top: 0;
        width: 15px;
        height: 100%;
      }
      .score-view{
        position: absolute;
        right: 0;
        top: 0;
        width: 80px;
        height: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    .essay_answer{
      width: 100%;
      font-size: 14px;
      padding: 15px 20px;
      margin-top: 10px;
      color: rgba(47, 128, 237, 1);
      background-color: rgba(230, 240, 255, 1);
      border-radius: 5px;
      min-height: 50px;
    }
    .essay_answer_wrong{
      color: rgba(235, 87, 87, 1);
      background-color: rgba(255, 235, 235, 1);
    }
    .answer-item{
      width: 100%;
      min-height: 30px;
      display: flex;
      //align-items: center;
      justify-content: flex-start;
      padding-left: 10px;
      font-size: 14px;
      margin-top: 8px;
      cursor: pointer;
      .label{
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(243, 243, 243, 1);
        border-radius: 50%;
        margin-right: 10px;
        flex-shrink: 0;
      }
      .label-select{
        background-color: rgba(47, 128, 237, 1);
        color: rgba(255, 255, 255, 1);
      }
      .label-right{
        background-color: rgba(39, 174, 96, 1);
        color: rgba(255, 255, 255, 1);
      }
      .label-wrong{
        background-color: rgba(235, 87, 87, 1);
        color: rgba(255, 255, 255, 1);
      }
      .text-wrong{
        color: rgba(235, 87, 87, 1);
      }
      .text-right{
        color: rgba(39, 174, 96, 1);
      }
    }
  }
  .bottom{
    width: 100%;
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 10px 20px 10px 30px;
    font-size: 12px;
    background: rgba(251, 251, 251, 1);
    border-radius: 10px;
    .bottom-tip{
      width: 70px;
      height: 25px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 5px;
      font-weight: 500;
      flex-shrink: 0;
    }
    .answer-tip{
      background: linear-gradient(90deg, #ABECD6 0%, #FBED96 100%);
    }
    .analysis-tip{
      background: linear-gradient(90deg, #FBC2EB 0%, #A6C1EE 100%);
    }
    .point-tip{
      background: linear-gradient(90deg, #84FAB0 0%, #8FD3F4 100%);
    }
    .bottom-content{
      font-size: 14px;
      margin-left: 10px;
      white-space: pre-wrap;
      line-height: 25px;
      width: 100%;
    }
    .bottom-point{
      font-size: 14px;
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
      margin-left: 10px;
      align-items: center;
      .point-item{
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 8px;
        background: rgba(47, 128, 237, 0.17);
        border: 1px solid rgba(47, 128, 237, 1);
        border-radius: 15px;
        color: rgba(47, 128, 237, 1);
        min-width: 50px;
      }
      .point-delete{
        color: rgba(47, 128, 237, 1);
        margin-left: 5px;
        cursor: pointer;
      }
      .point-add{
        color: rgba(47, 128, 237, 1);
        font-size: 16px;
        cursor: pointer;
      }
    }
  }
}
.essay_input{
  margin-top: 20px;
}
</style>
