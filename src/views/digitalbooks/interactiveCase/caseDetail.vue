<template>
  <div v-if="detailShow" class="detail-body">
    <div class="detail-main" :class="{'detail-main-h5' : type === 'h5'}">
      <div class='header-view' style='height: 50px;font-size: 20px;'>
        <img v-if="type !== 'h5'" class="back" src="@/assets/digitalbooks/arrow-left.svg" @click="back" />
        <i v-else class="el-icon-close close" style="font-size:20px" @click="back"></i>
        {{ title }}
      </div>
      <div class='content-view' style='height: calc(100% - 50px); ' v-loading='loading || infoLoading' element-loading-background="rgba(255, 255, 255, 0.3)" element-loading-text='页面加载中'>
        <iframe
          id="scratch-iframe"
          ref="scratchFrame"
          :src="iframeUrl"
          @load="loading = false"
          style="border: none"
          width="100%"
          height="100%"
          allowfullscreen
          allow="microphone *; camera *"
          sandbox="allow-same-origin allow-scripts allow-popups allow-modals"
        ></iframe>
      </div>
    </div>
  </div>

</template>

<script>
import { getTraining } from '@/api/training-api'

export default {
  name: 'CaseDetail',
  data() {
    return {
      detailShow: false,
      title: '交互案例',
      iframeUrl: '',
      loading: false,
      infoLoading: false,
      type: ''
    }
  },
  mounted() {
    this.type = window.location.href.includes('bingoBook/bookRead') ? 'h5' : 'pc'
  },
  methods: {
    back() {
      this.detailShow = false
      this.iframeUrl = ''
    },
    open(item) {
      this.detailShow = true
      if (item.id) {
        this.getTrainingInfo(item.id)
      } else {
        this.iframeUrl = item.practiceUrl
        this.title = item.trainingName ? item.trainingName + '交互案例' : '交互案例'
      }
      this.loading = true
    },
    async getTrainingInfo(id) {
      this.infoLoading = true
      try {
        const { data } = await getTraining({
          trainingId: id
        })
        if (!data) {
          this.$message.warning('该实验已被删除')
          this.back()
          return
        }
        this.title = data.trainingName ? data.trainingName + '交互案例' : '交互案例'
        this.iframeUrl = data.practiceUrl
      } catch (e) {
        console.log(e)
      } finally {
        this.infoLoading = false
      }
    }
  }
}
</script>

<style scoped lang='scss'>
.detail-body{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: end;
  .detail-main-h5{
    height: 95% !important;
  }
  .detail-main{
    width: 100%;
    height: 100%;
    background-color: #f1f7ff;
    .header-view{
      display: flex;
      width: 100%;
      align-items: center;
      justify-content: center;
      position: relative;
      font-weight: 500;
      background-color: rgba(245, 250, 255, 1);
      .back{
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
      }
      .close{
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        font-size: 24px;
      }
    }
    .content-view{
      width: 100%;
      overflow: hidden;
    }
  }
}

</style>
