<template>
  <div v-if="caseShow" class="case-body" v-loading="loading">
    <div class="header_view">
      <img class="back" src="@/assets/digitalbooks/arrow-left.svg" @click="back" />
      <div class="head-title article-singer-container">
        交互案例
      </div>
    </div>
    <div class="content_view">
      <div v-if="caseList.length == 0" style='width:100%;height:100%;display: flex;align-items: center;justify-content: center'>
        <Empty description="暂无数据" />
      </div>
      <div v-else  class="content_item_view">
        <div class="content_item" v-for="item in caseList" :key='item.id'>
          <div class="case-item-body">
            <div class="case-img-view">
              <img :src="item.cover && item.cover !== '' ? item.cover : defaultCover" alt='' class="case-img"/>
            </div>
            <div class="case-content-view">
              <div class="title">
                {{ item.trainingName || '案例标题'}}
              </div>
              <el-tooltip class="item" effect="dark" placement="top" :disabled="!showTooltip">
                <div slot="content">
                  <div style="max-width: 25vw; white-space: pre-wrap">
                    {{ item.subtitle || ''}}
                  </div>
                </div>
                <div class="subTitle" @mouseenter="checkOverflow" @mouseleave="showTooltip = false">
                  {{ item.subtitle || ''}}
                </div>
              </el-tooltip>
            </div>
            <div class="case-btn" @click='toDetail(item)'>立即体验</div>
          </div>
        </div>
      </div>
    </div>
    <CaseDetail ref='detailRef' />
  </div>
</template>

<script>
import CaseDetail from '@/views/digitalbooks/interactiveCase/caseDetail'
import { Empty } from 'vant'
import { getTrainingCaseList } from '@/api/training-api'
export default {
  name: 'InteractiveCase',
  components: { Empty, CaseDetail },
  data() {
    return {
      caseShow: false,
      loading: false,
      caseList: [],
      defaultCover: require('@/assets/digitalbooks/caseDefault.png'),
      showTooltip: false
    }
  },
  methods: {
    back() {
      this.caseShow = false
    },
    open(data) {
      this.caseShow = true
      this.getCaseList(data.digitalBook)
    },
    async getCaseList(digitalBook) {
      try {
        this.loading = true
        const { data } = await getTrainingCaseList({
          bookId: digitalBook.id
        })
        this.caseList = data || []
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    },
    toDetail(item) {
      this.$refs.detailRef.open(item)
    },
    checkOverflow(event) {
      if (event.target.scrollHeight > event.target.clientHeight) {
        this.showTooltip = true
      } else {
        this.showTooltip = false
      }
    }
  },
  mounted() {
    // 页面加载时可以执行一些初始化操作
  }
}
</script>

<style scoped lang='scss'>
.case-body {
  width: 100%;
  height: 100%;
  padding: 10px 20px 30px 20px;
  background-color: #F1F7FF;
  position: fixed;
  top: 0;
  left: 0;
  .header_view{
    height: 40px;
    display: flex;
    width: 100%;
    align-items: center;
    margin-bottom: 10px;
    .back {
      width: 20px;
      height: 20px;
      object-fit: cover;
      cursor: pointer;
    }
    .head-title {
      color: #000;
      font-size: var(--font-size-XXL);
      font-weight: 500;
      margin-left: 10px;
      width: 250px;
    }
  }
  .content_view{
    width: 100%;
    height: calc(100% - 50px);
    background-color: white;
    border-radius: 5px;
    padding: 20px;
    overflow-y: auto;
    .content_item_view{
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      gap: 45px;
      .content_item{
        width: calc(33% - 30px);
        height: 255px;
        border: 1px solid rgba(181, 218, 255, 1);
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.06);
        border-radius: 10px;
        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .case-item-body{
          width: 100%;
          height: 100%;
          background-color: rgba(245, 250, 255, 1);
          border-radius: 10px;
          padding-bottom: 10px;
          .case-img-view{
            width: 100%;
            height: calc(100% - 80px);
            .case-img{
              width: 100%;
              height: 100%;
              object-fit: cover;
              border-radius: 10px 10px 0 0;
            }
          }
          .case-content-view{
            width: 100%;
            height: 60px;
            padding: 5px 10px;
            .title{
              width: 100%;
              height: 45%;
              display: flex;
              align-items: center;
              font-size: 16px;
              font-weight: 500;
            }
            .subTitle{
              width: 100%;
              height: 55%;
              font-size: 12px;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              white-space: pre-wrap;
              text-overflow: ellipsis;
              color: rgba(51, 51, 51, 1);
            }
          }
          .case-btn{
            margin-left: 10px;
            height: 20px;
            width: 25%;
            background-color: rgba(47, 128, 237, 1);
            color: white;
            font-size: 12px;
            border-radius: 15px;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
          }
        }
      }
    }
  }
}
</style>
