class GeolocationHelper {
  constructor() {
    this.isSupported = 'geolocation' in navigator
    this.watchId = null
  }

  isGeolocationSupported() {
    return this.isSupported
  }

  getCurrentPosition(options = {}) {
    return new Promise((resolve, reject) => {
      if (!this.isSupported) {
        reject(new Error('当前浏览器不支持地理位置API'))
        return
      }

      const defaultOptions = {
        enableHighAccuracy: false,
        timeout: 20000,
        maximumAge: 0
      }

      const finalOptions = { ...defaultOptions, ...options }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          const locationData = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            altitude: position.coords.altitude,
            altitudeAccuracy: position.coords.altitudeAccuracy,
            heading: position.coords.heading,
            speed: position.coords.speed,
            timestamp: position.timestamp
          }
          resolve(locationData)
        },
        (error) => {
          console.log('获取定位失败:', error)
          reject(this.handleGeolocationError(error))
        },
        finalOptions
      )
    })
  }

  watchPosition(successCallback, errorCallback, options = {}) {
    if (!this.isSupported) {
      errorCallback(new Error('当前浏览器不支持地理位置API'))
      return null
    }

    const defaultOptions = {
      enableHighAccuracy: true,
      timeout: 15000,
      maximumAge: 0
    }

    const finalOptions = { ...defaultOptions, ...options }

    this.watchId = navigator.geolocation.watchPosition(
      (position) => {
        const locationData = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          altitude: position.coords.altitude,
          altitudeAccuracy: position.coords.altitudeAccuracy,
          heading: position.coords.heading,
          speed: position.coords.speed,
          timestamp: position.timestamp
        }
        successCallback(locationData)
      },
      (error) => {
        errorCallback(this.handleGeolocationError(error))
      },
      finalOptions
    )

    return this.watchId
  }

  clearWatch() {
    if (this.watchId !== null) {
      navigator.geolocation.clearWatch(this.watchId)
      this.watchId = null
    }
  }

  handleGeolocationError(error) {
    let message = '获取位置信息失败'
    switch (error.code) {
      case error.PERMISSION_DENIED:
        message = '用户拒绝了地理位置权限请求'
        break
      case error.POSITION_UNAVAILABLE:
        message = '位置信息不可用'
        break
      case error.TIMEOUT:
        message = '获取位置信息超时'
        break
      default:
        message = `未知错误: ${error.message}`
        break
    }

    return new Error(message)
  }

  async checkPermission() {
    if (!navigator.permissions) {
      return 'unsupported'
    }

    try {
      const permission = await navigator.permissions.query({ name: 'geolocation' })
      return permission.state
    } catch (error) {
      console.warn('检查地理位置权限失败:', error)
      return 'unknown'
    }
  }

  async requestPermission() {
    try {
      const permission = await this.checkPermission()

      if (permission === 'granted') {
        return true
      }

      const position = await this.getCurrentPosition({
        timeout: 10000,
        enableHighAccuracy: false
      })
      return !!position
    } catch (error) {
      console.warn('请求地理位置权限失败:', error)

      if (error.message.includes('拒绝')) {
        console.log('用户拒绝了权限请求')
      }

      return false
    }
  }

  async getIPLocation() {
    try {
      const response = await fetch('https://ipapi.co/json/')
      const data = await response.json()

      return {
        latitude: data.latitude,
        longitude: data.longitude,
        city: data.city,
        region: data.region,
        country: data.country_name,
        ip: data.ip,
        source: 'ip'
      }
    } catch (error) {
      console.warn('获取IP定位失败:', error)
      throw new Error('无法获取IP定位信息')
    }
  }

  async getLocationByIPAPI() {
    return new Promise((resolve, reject) => {
      const callbackName = 'ipapi_callback_' + Date.now()

      const script = document.createElement('script')
      script.src = `http://ip-api.com/json/?lang=zh-CN&callback=${callbackName}`

      window[callbackName] = (data) => {
        document.head.removeChild(script)
        delete window[callbackName]

        if (data.status === 'success') {
          resolve({
            status: data.status,
            country: data.country,
            countryCode: data.countryCode,
            region: data.region,
            regionName: data.regionName,
            city: data.city,
            zip: data.zip,
            lat: data.lat,
            lon: data.lon,
            timezone: data.timezone,
            isp: data.isp,
            org: data.org,
            as: data.as,
            query: data.query,
            source: 'ip-api'
          })
        } else {
          reject(new Error('IP-API请求失败'))
        }
      }

      script.onerror = () => {
        document.head.removeChild(script)
        delete window[callbackName]
        reject(new Error('IP-API请求失败'))
      }

      setTimeout(() => {
        if (window[callbackName]) {
          document.head.removeChild(script)
          delete window[callbackName]
          reject(new Error('IP-API请求超时'))
        }
      }, 10000)
      document.head.appendChild(script)
    })
  }

  async getLocation(options = {}) {
    const { useIPFallback = true, timeout = 10000 } = options

    try {
      const gpsLocation = await this.getCurrentPosition({ timeout })
      return {
        ...gpsLocation,
        source: 'gps'
      }
    } catch (gpsError) {
      console.warn('GPS定位失败:', gpsError.message)

      if (useIPFallback) {
        try {
          const ipLocation = await this.getIPLocation()
          return ipLocation
        } catch (ipError) {
          console.warn('IP定位失败:', ipError.message)
          throw new Error('无法获取位置信息')
        }
      } else {
        throw gpsError
      }
    }
  }
}

const geolocationHelper = new GeolocationHelper()

export default geolocationHelper
