export function addMoveAndDrag(editor, selectedDiv) {
  // 设置选中的div
  function setSelectedDiv(div) {
    // 移除之前选中div的选中状态
    if (selectedDiv && selectedDiv !== div) {
      selectedDiv.classList.remove('draggable-div-selected')
      hideResizeHandles(selectedDiv)
    }
    // 设置新的选中div
    selectedDiv = div
    const doc = editor.getWin().document
    const moveHandle = doc.createElement('div')
    moveHandle.className = 'move-handle'
    const handle = doc.createElement('div')
    handle.className = 'resize-handle'
    if (!(div.querySelector('.move-handle'))) {
      div.appendChild(moveHandle)
    }
    if (!(div.querySelector('.resize-handle'))) {
      div.appendChild(handle)
    }
    if (!div.classList.contains('draggable-div-selected')) {
      div.classList.add('draggable-div-selected')
      // handles.addEventListener('mousedown', addDragListeners.bind(null, div, handles))
      showResizeHandles(div)
      addDragListeners(div, handle)
      addMoveDragListeners(div, div.querySelector('.move-handle'))
    }
  }
  // 显示调整手柄
  function showResizeHandles(div) {
    const handles = div.querySelectorAll('.resize-handle,.move-handle')
    handles.forEach(handle => {
      handle.style.display = 'block'
    })
  }
  // 隐藏调整手柄
  function hideResizeHandles(div) {
    const handles = div.querySelectorAll('.resize-handle,.move-handle')
    handles.forEach(handle => {
      handle.style.display = 'none'
      if (handle) {
        handle.parentNode.removeChild(handle)
      }
    })
  }
  const addDragListeners = (div, handle) => {
    const doc = editor.getDoc()
    // // 使用新版 API 获取文档对象
    // const doc = editor.getWin().document;
    let isResizing = false
    let startX, startY, initialWidth, initialHeight
    const startResize = (e) => {
      e.preventDefault()
      isResizing = true
      startX = e.clientX
      startY = e.clientY
      initialWidth = div.offsetWidth
      initialHeight = div.offsetHeight

      doc.addEventListener('mousemove', resize)
      doc.addEventListener('mouseup', stopResize)
    }

    const resize = (e) => {
      if (!isResizing) return
      const deltaX = e.clientX - startX
      const deltaY = e.clientY - startY
      div.style.width = `${initialWidth + deltaX}px`
      div.style.height = `${initialHeight + deltaY}px`
      editor.dom.setAttrib(div, 'style', div.getAttribute('style')) // 保持撤销记录
    }
    const stopResize = () => {
      isResizing = false
      doc.removeEventListener('mousemove', resize)
      doc.removeEventListener('mouseup', stopResize)
      editor.undoManager.add()// 记录操作到撤销栈
    }
    handle.addEventListener('mousedown', startResize)
  }
  const addMoveDragListeners = (div, handle) => {
    const doc = editor.getDoc()
    let isDragging = false
    let offsetX, offsetY, initialLeft, initialTop

    const startDrag = (e) => {
      e.preventDefault()
      isDragging = true
      initialLeft = parseInt(getComputedStyle(div).left) || 0
      initialTop = parseInt(getComputedStyle(div).top) || 0

      offsetX = e.clientX - initialLeft
      offsetY = e.clientY - initialTop
      doc.addEventListener('mousemove', drag)
      doc.addEventListener('mouseup', stopDrag)
    }

    const drag = (e) => {
      if (!isDragging) return
      const parent = div.parentElement
      const parentRect = parent.getBoundingClientRect()
      let newLeft = e.clientX - offsetX
      const newTop = e.clientY - offsetY
      // 限制在编辑器范围内
      newLeft = Math.max(0, Math.min(newLeft, parentRect.width - div.offsetWidth))
      // newTop = Math.max(0, Math.min(newTop, parentRect.height - div.offsetHeight))

      div.style.left = `${newLeft}px`
      div.style.top = `${newTop}px`
      editor.dom.setAttrib(div, 'style', div.getAttribute('style'))
    }

    const stopDrag = () => {
      isDragging = false
      doc.removeEventListener('mousemove', drag)
      doc.removeEventListener('mouseup', stopDrag)
      editor.undoManager.add()
    }

    handle.addEventListener('mousedown', startDrag)
  }
  // 点击编辑器其他地方时取消选中
  editor.on('click', function(e) {
    // 如果点击的是可拖拽div
    if (e.target.classList.contains('draggable-div')) {
      e.preventDefault()
      e.stopPropagation()
      e.target.contentEditable = 'true'
      setSelectedDiv(e.target)
    } else if (e.target.closest('.draggable-div')) { // 如果点击的是div内部元素
      e.preventDefault()
      e.stopPropagation()
      e.target.closest('.draggable-div').contentEditable = 'true'
      setSelectedDiv(e.target.closest('.draggable-div'))
    } else if (selectedDiv) { // 点击其他地方，取消选中
      selectedDiv.contentEditable = 'false'
      selectedDiv.classList.remove('draggable-div-selected')
      hideResizeHandles(selectedDiv)
      selectedDiv = null
    }
  })
  editor.on('keydown', function(e) {
    if (e.keyCode === 46 || e.keyCode === 8) {
      // debugger
      if (selectedDiv) {
        // const div = selectedDiv.querySelector('.draggable-main')
        const divs = selectedDiv.querySelectorAll('.draggable-main')
        let canRemove = true
        divs.forEach(item => {
          if (!item.innerHTML || item.innerHTML === '<br>' || item.innerHTML === '<br data-mce-bogus="1">' || item.innerHTML === '' || item.innerHTML === '&nbsp;') {
            console.log('可以删除')
            canRemove = true
          } else {
            canRemove = false
          }
        })
        if (divs.length > 1 || divs.length === 0){
          canRemove = false
        }
        if (selectedDiv && selectedDiv.parentNode && canRemove) {
          selectedDiv.parentNode.removeChild(selectedDiv)
          selectedDiv = null
        }
      }
    }
  })
}
