import geolocationHelper from './geolocation'
import store from '@/store'
import { getSupportLocation } from '@/api/course-api'

class LocationService {
  constructor() {
    this.isInitialized = false
    this.initPromise = null
  }

  async init() {
    if (this.isInitialized) {
      return
    }

    if (this.initPromise) {
      return this.initPromise
    }

    this.initPromise = this._fetchLocation()
    try {
      await this.initPromise
      this.isInitialized = true
    } catch (error) {
      this.isInitialized = true
    } finally {
      this.initPromise = null
    }
  }

  async _fetchLocation() {
    try {
      const [locationData, supportRegionsResponse] = await Promise.all([
        geolocationHelper.getLocationByIPAPI(),
        getSupportLocation()
      ])

      if (locationData && locationData.regionName) {
        store.dispatch('app/setLocationName', locationData.regionName)
      }

      if (supportRegionsResponse.data && supportRegionsResponse.data.length > 0) {
        store.dispatch('app/setSupportedRegions', supportRegionsResponse.data)

        const realLocation = locationData.regionName
        const supportedRegions = supportRegionsResponse.data

        const matchedLocation = supportedRegions.find(region => {
          return region.name === realLocation ||
                 realLocation.includes(region.name) ||
                 region.name.includes(realLocation)
        })

        const defaultSelection = matchedLocation ? matchedLocation.name : '全国'
        store.dispatch('app/setSelectedRegion', defaultSelection)
      } else {
        store.dispatch('app/setSelectedRegion', '全国')
      }

      return locationData.regionName
    } catch (error) {
      store.dispatch('app/setLocationName', '')
      store.dispatch('app/setSelectedRegion', '全国')
    }
  }

  async refresh() {
    this.isInitialized = false
    return this.init()
  }

  getCurrentLocationName() {
    return store.getters.locationName
  }
}

const locationService = new LocationService()

export default locationService
