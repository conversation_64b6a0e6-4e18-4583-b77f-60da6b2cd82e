import geolocationHelper from './geolocation'
import store from '@/store'
import { getSupportLocation } from '@/api/course-api'

class LocationService {
  constructor() {
    this.isInitialized = false
    this.initPromise = null
  }

  async init() {
    if (this.isInitialized) {
      return
    }

    if (this.initPromise) {
      return this.initPromise
    }

    this.initPromise = this._fetchLocation()
    try {
      await this.initPromise
      this.isInitialized = true
    } catch (error) {
      this.isInitialized = true
    } finally {
      this.initPromise = null
    }
  }

  async _fetchLocation() {
    let locationData = null
    let supportRegionsResponse = null

    // 分别处理两个接口，互不影响
    try {
      locationData = await geolocationHelper.getLocationByIPAPI()
      if (locationData && locationData.regionName) {
        store.dispatch('app/setLocationName', locationData.regionName)
      }
    } catch (error) {
      console.warn('获取IP定位失败:', error)
      store.dispatch('app/setLocationName', '')
    }

    try {
      supportRegionsResponse = await getSupportLocation()
      if (supportRegionsResponse.data && supportRegionsResponse.data.length > 0) {
        store.dispatch('app/setSupportedRegions', supportRegionsResponse.data)
      }
    } catch (error) {
      console.warn('获取支持区域失败:', error)
    }

    // 设置默认选择区域
    if (supportRegionsResponse && supportRegionsResponse.data && supportRegionsResponse.data.length > 0) {
      const realLocation = locationData?.regionName
      const supportedRegions = supportRegionsResponse.data

      if (realLocation) {
        const matchedLocation = supportedRegions.find(region => {
          return region.name === realLocation ||
                 realLocation.includes(region.name) ||
                 region.name.includes(realLocation)
        })
        const defaultSelection = matchedLocation ? matchedLocation.name : '全国'
        store.dispatch('app/setSelectedRegion', defaultSelection)
      } else {
        store.dispatch('app/setSelectedRegion', '全国')
      }
    } else {
      store.dispatch('app/setSelectedRegion', '全国')
    }

    return locationData?.regionName
  }

  async refresh() {
    this.isInitialized = false
    return this.init()
  }

  getCurrentLocationName() {
    return store.getters.locationName
  }
}

const locationService = new LocationService()

export default locationService
