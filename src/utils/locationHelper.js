import store from '@/store'

export function getLocationNameParam() {
  const currentChannel = window.localStorage.getItem('currentChannel')
  if (currentChannel !== 'aigc') {
    return null
  }

  const selectedRegion = store.getters.selectedRegion
  const realLocation = store.getters.locationName
  const supportedRegions = store.getters.supportedRegions

  let locationNameParam = null

  if (selectedRegion && selectedRegion !== '全国') {
    locationNameParam = selectedRegion
  } else {
    const isRealLocationSupported = supportedRegions && supportedRegions.some(region =>
      region.name === realLocation ||
      realLocation.includes(region.name) ||
      region.name.includes(realLocation)
    )
    if (isRealLocationSupported) {
      locationNameParam = realLocation
    }
  }

  return locationNameParam
}

export function addLocationNameParam(params = {}) {
  const locationNameParam = getLocationNameParam()
  if (locationNameParam) {
    params.locationName = locationNameParam
  }
  return params
}
