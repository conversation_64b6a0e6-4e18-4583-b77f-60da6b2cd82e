const { exec } = require('child_process')

class IntelligentRecordingDetector {
  constructor(window) {
    this.window = window
    this.isActive = false
    this.detectionInterval = null
    this.monitoringInterval = null

    this.behaviorData = {
      blurStartTime: 0,
      blurCount: 0,
      totalBlurDuration: 0,
      lastDetectionTime: 0,
      detectedApps: [],
      confidence: 0
    }

    this.config = {
      detectionInterval: 15000,
      minBlurDuration: 5000,
      detectionCooldown: 10000,
      confidenceThreshold: 0.7
    }

    this.cleanDetectionCount = 0
    this.requiredCleanDetections = 2
  }

  start() {
    if (this.isActive) return
    this.isActive = true
    console.log('🔍 [IntelligentRecordingDetector] 启动智能录屏检测器')
    this.performDetection()
    this.detectionInterval = setInterval(() => {
      this.performDetection()
    }, this.config.detectionInterval)
  }

  stop() {
    if (!this.isActive) return
    this.isActive = false
    console.log('🔍 [IntelligentRecordingDetector] 停止智能录屏检测器')
    if (this.detectionInterval) {
      clearInterval(this.detectionInterval)
      this.detectionInterval = null
    }
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = null
    }
  }

  onWindowBlur() {
    this.behaviorData.blurStartTime = Date.now()
    this.behaviorData.blurCount++
    console.log('🔍 [IntelligentRecordingDetector] 窗口失去焦点，记录可疑行为')
  }

  onWindowFocus() {
    if (this.behaviorData.blurStartTime > 0) {
      const blurDuration = Date.now() - this.behaviorData.blurStartTime
      this.behaviorData.totalBlurDuration += blurDuration

      console.log(`🔍 [IntelligentRecordingDetector] 窗口重新获得焦点，失焦时长: ${blurDuration}ms`)

      if (blurDuration > this.config.minBlurDuration) {
        console.log('🔍 [IntelligentRecordingDetector] 检测到可疑失焦行为，立即进行录屏检测')
        this.performDetection()
      }
      this.behaviorData.blurStartTime = 0
    }
  }

  async performDetection() {
    try {
      console.log('🔍 [IntelligentRecordingDetector] 开始执行录屏检测...')
      const detectedApps = await this.detectScreenRecordingSoftware()

      const safeDetectedApps = Array.isArray(detectedApps) ? detectedApps : []
      console.log('🔍 [IntelligentRecordingDetector] 检测结果:', safeDetectedApps)

      this.behaviorData.detectedApps = safeDetectedApps
      this.behaviorData.lastDetectionTime = Date.now()

      const confidence = this.calculateConfidence(safeDetectedApps)
      this.behaviorData.confidence = confidence

      console.log('📊 [IntelligentRecordingDetector] 计算置信度:', confidence)

      if (confidence > this.config.confidenceThreshold) {
        this.handleRecordingDetected(safeDetectedApps, confidence)
      } else if (safeDetectedApps.length > 0) {
        this.handleSuspiciousActivity(safeDetectedApps, confidence)
      } else {
        console.log('✅ [IntelligentRecordingDetector] 未检测到录屏软件')
      }
    } catch (error) {
      console.log('🔍 [IntelligentRecordingDetector] 录屏检测执行失败:', error.message)
      console.error('🔍 [IntelligentRecordingDetector] 错误详情:', error)
    }
  }

  calculateConfidence(detectedApps) {
    let confidence = 0

    if (detectedApps.length > 0) {
      confidence += 0.5
      if (detectedApps.length > 1) {
        confidence += 0.1
      }
    }

    if (this.behaviorData.blurCount > 2 && this.behaviorData.totalBlurDuration > 10000) {
      confidence += 0.3
    }

    const timeSinceLastBlur = Date.now() - this.behaviorData.blurStartTime
    if (timeSinceLastBlur < 60000 && this.behaviorData.blurCount > 0) {
      confidence += 0.2
    }

    return Math.min(confidence, 1.0)
  }

  handleRecordingDetected(detectedApps, confidence) {
    console.log('🚨 [IntelligentRecordingDetector] 确认检测到录屏行为!')
    console.log('📱 [IntelligentRecordingDetector] 检测到的软件:', detectedApps.join(', '))
    console.log('📊 [IntelligentRecordingDetector] 置信度:', (confidence * 100).toFixed(1) + '%')

    this.showBlackScreenProtection('persistent')
    this.startPersistentMonitoring()
  }

  handleSuspiciousActivity(detectedApps, confidence) {
    console.log('⚠️ [IntelligentRecordingDetector] 检测到可疑录屏活动')
    console.log('📱 [IntelligentRecordingDetector] 检测到的软件:', detectedApps.join(', '))
    console.log('📊 [IntelligentRecordingDetector] 置信度:', (confidence * 100).toFixed(1) + '%')
    console.log('📝 [IntelligentRecordingDetector] 可疑活动不触发黑屏，仅记录')
  }

  startPersistentMonitoring() {
    if (this.monitoringInterval) return

    console.log('🔍 [IntelligentRecordingDetector] 启动持续监控模式')

    this.cleanDetectionCount = 0

    this.monitoringInterval = setInterval(async () => {
      try {
        const detectedApps = await this.detectScreenRecordingSoftware()
        const safeDetectedApps = Array.isArray(detectedApps) ? detectedApps : []

        console.log('🔍 [IntelligentRecordingDetector] 持续监控检测结果:', safeDetectedApps)

        if (safeDetectedApps.length === 0) {
          this.cleanDetectionCount++
          console.log(`✅ [IntelligentRecordingDetector] 清洁检测 ${this.cleanDetectionCount}/${this.requiredCleanDetections}`)

          if (this.cleanDetectionCount >= this.requiredCleanDetections) {
            console.log('✅ [IntelligentRecordingDetector] 连续清洁检测达到阈值，确认录屏软件已关闭')
            this.handleRecordingStopped()
          }
        } else {
          this.cleanDetectionCount = 0
          console.log('🔍 [IntelligentRecordingDetector] 仍检测到录屏软件，重置清洁计数')

          this.window.webContents.send('recording-still-active', {
            detectedApps: safeDetectedApps,
            timestamp: new Date().toLocaleString()
          })
        }
      } catch (error) {
        console.log('🔍 [IntelligentRecordingDetector] 持续监控检测失败:', error.message)
      }
    }, 3000)
  }

  handleRecordingStopped() {
    console.log('✅ [IntelligentRecordingDetector] 录屏软件已关闭，准备恢复正常显示')

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = null
    }

    this.hideBlackScreenProtection()

    this.behaviorData = {
      blurStartTime: 0,
      blurCount: 0,
      totalBlurDuration: 0,
      lastDetectionTime: Date.now(),
      detectedApps: [],
      confidence: 0
    }
  }

  showBlackScreenProtection(type = 'temporary') {
    if (!this.window || this.window.isDestroyed()) {
      console.log('❌ [IntelligentRecordingDetector] 窗口不可用，无法显示黑屏保护')
      return
    }

    console.log(`🛡️ [IntelligentRecordingDetector] 显示黑屏保护 (${type})`)

    const blackScreenScript = `
      (function() {
        const existingBlackScreen = document.getElementById('electron-black-screen-protection');
        if (existingBlackScreen) {
          existingBlackScreen.remove();
        }

        const blackScreen = document.createElement('div');
        blackScreen.id = 'electron-black-screen-protection';
        blackScreen.style.cssText = \`
          position: fixed !important;
          top: 0 !important;
          left: 0 !important;
          width: 100vw !important;
          height: 100vh !important;
          background: #000 !important;
          z-index: 2147483647 !important;
          pointer-events: none !important;
          opacity: 1 !important;
          visibility: visible !important;
        \`;

        document.body.appendChild(blackScreen);
        console.log('🛡️ 黑屏保护已激活');

        ${type === 'temporary' ? `
        setTimeout(() => {
          const blackScreen = document.getElementById('electron-black-screen-protection');
          if (blackScreen) {
            blackScreen.remove();
            console.log('⏰ 临时黑屏保护已自动移除');
          }
        }, 10000);
        ` : ''}
      })();
    `

    this.window.webContents.executeJavaScript(blackScreenScript).catch(error => {
      console.log('❌ [IntelligentRecordingDetector] 执行黑屏脚本失败:', error.message)
    })
  }

  hideBlackScreenProtection() {
    if (!this.window || this.window.isDestroyed()) {
      return
    }

    console.log('✅ [IntelligentRecordingDetector] 隐藏黑屏保护')

    const hideScript = `
      (function() {
        const blackScreen = document.getElementById('electron-black-screen-protection');
        if (blackScreen) {
          blackScreen.remove();
          console.log('✅ 黑屏保护已移除');
        }
      })();
    `

    this.window.webContents.executeJavaScript(hideScript).catch(error => {
      console.log('❌ [IntelligentRecordingDetector] 执行隐藏脚本失败:', error.message)
    })
  }

  async detectScreenRecordingSoftware() {
    const platform = process.platform
    const suspiciousApps = {
      common: [
        'obs', 'obs64', 'obs-studio', 'OBS',
        'screenflow', 'ScreenFlow',
        'camtasia', 'CamtasiaStudio', 'Camtasia',
        'bandicam', 'bdcam', 'Bandicam',
        'snagit', 'SnagitEditor', 'Snagit',
        'fraps', 'Fraps',
        'xsplit', 'XSplit'
      ],
      windows: [
        'QQScreenShot.exe', 'QQ.exe',
        'Bandicam.exe', 'bdcam.exe',
        'obs64.exe', 'obs32.exe',
        'CamtasiaStudio.exe', 'Camtasia.exe',
        'SnagitEditor.exe', 'Snagit32.exe'
      ],
      darwin: [
        'ScreenFlow', 'QuickTime Player',
        'OBS', 'obs',
        'Camtasia 2023', 'Camtasia 2024',
        'CleanShot X', 'Kap'
      ]
    }

    return new Promise((resolve) => {
      let command = ''
      let processNames = [...suspiciousApps.common]

      if (platform === 'win32') {
        processNames = processNames.concat(suspiciousApps.windows)
        command = 'tasklist /FO CSV'
      } else if (platform === 'darwin') {
        processNames = processNames.concat(suspiciousApps.darwin)
        command = 'ps aux'
      } else {
        // Linux 或其他系统
        command = 'ps aux'
      }

      exec(command, (error, stdout, stderr) => {
        if (error) {
          console.log('🔍 [IntelligentRecordingDetector] 录屏软件检测命令执行失败:', error.message)
          resolve([])
          return
        }

        const detectedApps = []
        const output = stdout.toLowerCase()

        processNames.forEach(appName => {
          const searchName = appName.toLowerCase()

          let isDetected = false

          if (platform === 'darwin') {
            const patterns = [
              `/${searchName}.app/`,
              `/${searchName}/`,
              ` ${searchName} `,
              `\t${searchName}\t`,
              `\n${searchName}\n`
            ]
            isDetected = patterns.some(pattern => output.includes(pattern.toLowerCase()))
          } else {
            isDetected = output.includes(searchName)
          }

          if (isDetected) {
            detectedApps.push(appName)
            console.log('🔍 [IntelligentRecordingDetector] 匹配到录屏软件:', appName)
          }
        })

        if (detectedApps.length > 0) {
          console.log('🚨 [IntelligentRecordingDetector] 检测到录屏软件正在运行:', detectedApps.join(', '))
          console.log('⚠️ [IntelligentRecordingDetector] 检测时间:', new Date().toLocaleString())
        }

        resolve(detectedApps)
      })
    })
  }
}

module.exports = IntelligentRecordingDetector
