// module.exports = {
//   plugins: {
//     autoprefixer: {}, // 用来给不同的浏览器自动添加相应前缀，如-webkit-，-moz-等等
//     "postcss-px-to-viewport": {
//       unitToConvert: "px", // 要转化的单位
//       viewportWidth: 900, // UI设计稿的宽度
//       unitPrecision: 6, // 转换后的精度，即小数点位数
//       propList: ["*"], // 指定转换的css属性的单位，*代表全部css属性的单位都进行转换
//       viewportUnit: "vh", // 指定需要转换成的视窗单位，默认vw
//       fontViewportUnit: "vh", // 指定字体需要转换成的视窗单位，默认vw
//       selectorBlackList: ["wrap","course-center","course-detail","team"], // 指定不转换为视窗单位的类名，
//       minPixelValue: 1, // 默认值1，小于或等于1px则不进行转换
//       mediaQuery: true, // 是否在媒体查询的css代码中也进行转换，默认false
//       replace: true, // 是否转换后直接更换属性值
//       exclude: [/node_modules/], // 设置忽略文件，用正则做目录名匹配
//       landscape: false // 是否处理横屏情况
//     }
//   }
// };

// module.exports = ({ file }) => {
//   const designWidth = file.dirname.includes(path.join('src','views','H5')) ? 375 : 900;
//   const designViewportUnit = file.dirname.includes(path.join('src','views','H5')) ? 'vw' : 'vh';
//   const designFontViewportUnit = file.dirname.includes(path.join('src','views','H5')) ? 'vw' : 'vh';
//   console.log(file);

//   return {
//     plugins: {
//       autoprefixer: {},
//       "postcss-px-to-viewport": {
//         unitToConvert: "px", // 要转化的单位
//         viewportWidth: 900, // UI设计稿的宽度
//         unitPrecision: 6, // 转换后的精度，即小数点位数
//         propList: ["*"], // 指定转换的css属性的单位，*代表全部css属性的单位都进行转换
//         viewportUnit: 'vh', // 指定需要转换成的视窗单位，默认vw
//         fontViewportUnit: 'vh', // 指定字体需要转换成的视窗单位，默认vw
//         selectorBlackList: ["wrap","course-center","course-detail","team"], // 指定不转换为视窗单位的类名，
//         minPixelValue: 1, // 默认值1，小于或等于1px则不进行转换
//         mediaQuery: true, // 是否在媒体查询的css代码中也进行转换，默认false
//         replace: true, // 是否转换后直接更换属性值
//         exclude: [/node_modules/], // 设置忽略文件，用正则做目录名匹配
//         landscape: false // 是否处理横屏情况
//       }
//     }
//   }

// }
const path = require("path");

module.exports = ({ webpack }) => {
  const designWidth = webpack.resourcePath.includes( path.join("src", "views", "H5") )||webpack.resourcePath.includes( path.join("src", "views", "community") )  || webpack.resourcePath.includes( path.join("src", "components", "H5",))||webpack.resourcePath.includes( path.join("src", "views", "bingoBook",) )  ? 375 : webpack.resourcePath.includes( path.join("src", "views", "parent",) ) ? 428 : 1100;
  // const designViewportUnit = webpack.resourcePath.includes( path.join("src", "views", "H5") ) || webpack.resourcePath.includes( path.join("src", "views", "parent") ) || webpack.resourcePath.includes( path.join("src", "components", "H5") ) ? 'vw' : 'vw';
  // const designFontViewportUnit = webpack.resourcePath.includes( path.join("src", "views", "H5") ) || webpack.resourcePath.includes( path.join("src", "views", "parent") )|| webpack.resourcePath.includes( path.join("src", "components", "H5") )  ? 'vw' : 'vw';

  return {
    plugins: {
      autoprefixer: {},
      "postcss-px-to-viewport": {
        unitToConvert: "px", // 要转化的单位
        viewportWidth: designWidth, // UI设计稿的宽度
        unitPrecision: 6, // 转换后的精度，即小数点位数
        propList: ["*"], // 指定转换的css属性的单位，*代表全部css属性的单位都进行转换
        // viewportUnit: designViewportUnit, // 指定需要转换成的视窗单位，默认vw
        // fontViewportUnit: designFontViewportUnit, // 指定字体需要转换成的视窗单位，默认vw
        viewportUnit: 'vw', // 指定需要转换成的视窗单位，默认vw
        fontViewportUnit: 'vw', // 指定字体需要转换成的视窗单位，默认vw
        // selectorBlackList: ["wrap","course-detail","team", "grade-wrap", "edu-btn-opacity", "edu-btn", "edu-btn-op", "bingo-dialog", "bingo-normal-dialog", "el-select", "el-select-dropdown"], // 指定不转换为视窗单位的类名，
        selectorBlackList: ["editor-dig", "dig-task-box",'dig-noteContent','no-conversion','no-conversion-pb40', 'add-exercise-main', 'question-view', 'do-exercise-main','choice-item-main', 'wrong-item'], // 指定不转换为视窗单位的类名，
        minPixelValue: 1, // 默认值1，小于或等于1px则不进行转换
        mediaQuery: true, // 是否在媒体查询的css代码中也进行转换，默认false
        replace: true, // 是否转换后直接更换属性值
        exclude: [/node_modules/], // 设置忽略文件，用正则做目录名匹配
        landscape: false // 是否处理横屏情况
      }
    }
  };
};
